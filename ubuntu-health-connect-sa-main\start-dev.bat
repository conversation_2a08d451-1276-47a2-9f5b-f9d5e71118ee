@echo off
echo 🚀 Ubuntu Health Connect SA - Starting Development Environment
echo ================================================================

REM Load environment variables from .env file
if exist .env (
    echo 📋 Loading environment configuration...
    for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
        if not "%%a"=="" if not "%%a:~0,1%"=="#" (
            set "%%a=%%b"
        )
    )
) else (
    echo ⚠️ No .env file found, using defaults...
    set VITE_FRONTEND_PORT=8085
    set VITE_BACKEND_PORT=5001
    set FLASK_PORT=5001
)

echo.
echo 🔧 Configuration:
echo   Frontend Port: %VITE_FRONTEND_PORT%
echo   Backend Port: %VITE_BACKEND_PORT%
echo.

REM Start backend in a new window
echo 🔧 Starting Backend Server...
start "Ubuntu Health Backend" cmd /k "cd /d "%~dp0" && python "Health Agent Voice/backend_minimal.py""

REM Wait a moment for backend to start
echo ⏳ Waiting for backend to initialize...
timeout /t 3 /nobreak >nul

REM Start frontend
echo 🌐 Starting Frontend Server...
echo 📱 Frontend will be available at: http://localhost:%VITE_FRONTEND_PORT%
echo 🔧 Backend will be available at: http://localhost:%VITE_BACKEND_PORT%
echo.
echo ✅ Both services are starting! Check the backend window for backend logs.
echo 🌐 Your browser should open automatically to the frontend.
echo.
echo 💡 To stop: Close this window and the backend window, or press Ctrl+C in both.
echo.

REM Start frontend (this will block until frontend stops)
npm run start:frontend:only

echo.
echo 👋 Development environment stopped.
pause
