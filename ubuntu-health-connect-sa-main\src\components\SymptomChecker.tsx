
import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, MessageSquare, Phone, Send, User, Bot, AlertTriangle, Clock, Loader2, Stethoscope, Pill, FileText, Calendar, MapPin, Package } from 'lucide-react';
import { openAIHealthService, type ChatMessage } from '@/services/openaiService';
import { medicalReportService, type MedicalReportData } from '@/services/medicalReportService';
import { patientCaseService } from '@/services/patientCaseService';
import { chatInteractionService } from '@/services/chatInteractionService';

interface SymptomCheckerProps {
  onBack: () => void;
  language: string;
}

function getInitialMessage(language: string): string {
  const messages: Record<string, string> = {
    'en': 'Hello! I am your AI health assistant powered by advanced medical knowledge. I can help you in your preferred language and provide personalized health guidance. How are you feeling today? Please describe any symptoms or health concerns you may have.',
    'zu': 'Sawubona! Ngingumsizi wakho wezempilo we-AI onamandla olwazi lwezokwelapha oluthuthukile. Ngingakusiza ngolimi lwakho olukhetha futhi nginikeze ukuqondisisa kwezempilo okwenziwe ngawe. Uzizwa kanjani namuhla? Sicela uchaze izimpawu noma ukukhathazeka kwezempilo ongakuba nakho.',
    'af': 'Hallo! Ek is jou AI-gesondheidsassistent wat aangedryf word deur gevorderde mediese kennis. Ek kan jou help in jou voorkeur taal en persoonlike gesondheidsleiding verskaf. Hoe voel jy vandag? Beskryf asseblief enige simptome of gesondheidsbekommernisse wat jy mag hê.'
  };
  return messages[language] || messages['en'];
}

export const SymptomChecker = ({ onBack, language }: SymptomCheckerProps) => {
  const [currentMessage, setCurrentMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [chatSessionId, setChatSessionId] = useState<string | null>(null);
  const [conversation, setConversation] = useState<ChatMessage[]>([
    {
      id: '1',
      role: 'assistant',
      content: getInitialMessage(language),
      timestamp: new Date()
    }
  ]);
  const [showReport, setShowReport] = useState(false);
  const [currentSeverity, setCurrentSeverity] = useState<'low' | 'moderate' | 'high' | 'critical'>('low');
  const [recommendations, setRecommendations] = useState<string[]>([]);
  const [reportData, setReportData] = useState<MedicalReportData | null>(null);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize chat session when component mounts
  useEffect(() => {
    const sessionId = chatInteractionService.startChatSession(
      'AI Health Assistant Chat',
      language,
      {
        // Try to get patient info from localStorage or context
        patientId: localStorage.getItem('currentPatientId') || undefined,
        idNumber: localStorage.getItem('currentPatientIdNumber') || undefined,
        phone: localStorage.getItem('currentPatientPhone') || undefined
      }
    );
    setChatSessionId(sessionId);

    // Add initial AI message to the session
    const initialMessage = {
      id: '1',
      role: 'assistant' as const,
      content: getInitialMessage(language),
      timestamp: new Date()
    };
    chatInteractionService.addMessageToSession(sessionId, initialMessage);

    // Cleanup function to end session when component unmounts
    return () => {
      if (sessionId) {
        chatInteractionService.endChatSession(sessionId, 'Chat session ended by user');
      }
    };
  }, [language]);

  // Auto-scroll to bottom when new messages are added
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [conversation, isLoading]);

  const getLanguageText = (key: string) => {
    const translations = {
      en: {
        healthAssistant: 'AI Health Assistant Chat',
        typeMessage: 'Describe your symptoms or ask a health question...',
        send: 'Send',
        callOption: 'Prefer to call? Dial 10010 (Free)',
        generateReport: 'Generate Medical Report',
        back: 'Back to Dashboard',
        errorMessage: 'I apologize, but I encountered a technical issue. Please try again or call 10010 for immediate assistance if this is urgent.',
        aiTyping: 'AI Assistant is typing...',
        poweredBy: 'Powered by OpenAI GPT-4',
        severity: 'Assessment Level',
        recommendations: 'Recommendations',
        emergency: 'Emergency',
        emergencyHelp: 'I need emergency medical help',
        checkup: 'Checkup',
        generalCheckup: 'I need a general health checkup',
        medication: 'Medication',
        medicationHelp: 'I need help with my medication'
      },
      zu: {
        healthAssistant: 'Ukuxoxa Nomsizi Wezempilo we-AI',
        typeMessage: 'Chaza izimpawu zakho noma ubuze umbuzo wezempilo...',
        send: 'Thumela',
        callOption: 'Ufisa ukushayela? Shayela ku-10010 (Mahhala)',
        generateReport: 'Dala Umbiko Wezokwelapha',
        back: 'Buyela Kwideshibhodi',
        errorMessage: 'Ngiyaxolisa, kodwa nginenkinga yobuchwepheshe. Sicela uzame futhi noma ushayele ku-10010 uma lokhu kuphuthumayo.',
        aiTyping: 'Umsizi we-AI uyabhala...',
        poweredBy: 'Kunamandla ka-OpenAI GPT-4',
        severity: 'Izinga Lokuhlola',
        recommendations: 'Izincomo',
        emergency: 'Isimo Esiphuthumayo',
        emergencyHelp: 'Ngidinga usizo lwezokwelapha olusheshayo',
        checkup: 'Ukuhlolwa',
        generalCheckup: 'Ngidinga ukuhlolwa kwezempilo okuvamile',
        medication: 'Imithi',
        medicationHelp: 'Ngidinga usizo ngemithi yami'
      },
      af: {
        healthAssistant: 'AI Gesondheid Assistent Gesprek',
        typeMessage: 'Beskryf jou simptome of vra \'n gesondheidsvraag...',
        send: 'Stuur',
        callOption: 'Verkies om te bel? Skakel 10010 (Gratis)',
        generateReport: 'Genereer Mediese Verslag',
        back: 'Terug na Bord',
        errorMessage: 'Ek vra om verskoning, maar ek het \'n tegniese probleem ondervind. Probeer asseblief weer of skakel 10010 vir onmiddellike hulp as dit dringend is.',
        aiTyping: 'AI Assistent tik...',
        poweredBy: 'Aangedryf deur OpenAI GPT-4',
        severity: 'Assesseringsvlak',
        recommendations: 'Aanbevelings',
        emergency: 'Noodgeval',
        emergencyHelp: 'Ek het nood mediese hulp nodig',
        checkup: 'Ondersoek',
        generalCheckup: 'Ek het \'n algemene gesondheidsondersoek nodig',
        medication: 'Medikasie',
        medicationHelp: 'Ek het hulp nodig met my medikasie'
      }
    };
    return translations[language as keyof typeof translations]?.[key] || translations.en[key];
  };

  const sendMessage = async () => {
    if (!currentMessage.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: currentMessage.trim(),
      timestamp: new Date()
    };

    const updatedConversation = [...conversation, userMessage];
    setConversation(updatedConversation);
    setCurrentMessage('');
    setIsLoading(true);

    // Add user message to chat session
    if (chatSessionId) {
      chatInteractionService.addMessageToSession(chatSessionId, userMessage);
    }

    try {
      // Call OpenAI service
      const response = await openAIHealthService.sendMessage(updatedConversation, language);

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.message,
        timestamp: new Date()
      };

      setConversation(prev => [...prev, aiMessage]);

      // Add AI message to chat session
      if (chatSessionId) {
        chatInteractionService.addMessageToSession(chatSessionId, aiMessage);
      }

      // Update severity and recommendations
      if (response.severity) {
        setCurrentSeverity(response.severity);
      }
      if (response.recommendations) {
        setRecommendations(response.recommendations);
      }

      // Show urgent care alert if needed
      if (response.urgentCare) {
        // You could add a modal or alert here for urgent cases
        console.warn('Urgent care recommended for user');
      }

    } catch (error) {
      console.error('❌ OpenAI API Error in SymptomChecker:', error);

      // Show specific error message to user
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: language === 'zu' ?
          'Ngiyaxolisa, kodwa nginenkinga yokuxhumana ne-AI. Sicela uzame futhi noma ushayele ku-10010 uma lokhu kuphuthumayo.' :
          language === 'af' ?
          'Ek vra om verskoning, maar ek het \'n probleem om met die AI te kommunikeer. Probeer asseblief weer of skakel 10010 as dit dringend is.' :
          'I apologize, but I\'m having trouble connecting to the AI service. Please try again or call 10010 if this is urgent.',
        timestamp: new Date()
      };

      setConversation(prev => [...prev, errorMessage]);

      // Add error message to chat session
      if (chatSessionId) {
        chatInteractionService.addMessageToSession(chatSessionId, errorMessage);
      }

      // Show user-friendly error alert
      alert(language === 'zu' ?
        'Inkinga ye-AI: Sicela uhlole ukuxhumana kwakho kwe-inthanethi futhi uzame futhi.' :
        language === 'af' ?
        'AI Probleem: Gaan asseblief jou internetverbinding na en probeer weer.' :
        'AI Service Issue: Please check your internet connection and try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-600 text-white';
      case 'high': return 'bg-orange-500 text-white';
      case 'moderate': return 'bg-yellow-500 text-white';
      case 'low': return 'bg-green-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      case 'high': return <AlertTriangle className="w-4 h-4" />;
      case 'moderate': return <Clock className="w-4 h-4" />;
      case 'low': return <MessageSquare className="w-4 h-4" />;
      default: return <MessageSquare className="w-4 h-4" />;
    }
  };

  const generateMedicalReport = async () => {
    setIsGeneratingReport(true);
    try {
      const report = await medicalReportService.generateMedicalReport(conversation, language);
      setReportData(report);

      // End the chat session with a comprehensive summary when report is generated
      if (chatSessionId) {
        const summary = `AI Health Assistant Chat completed with medical report generation. Patient reported symptoms: ${report.symptomsReported.primary.join(', ')}. AI Assessment - Severity: ${report.assessmentSummary.severity}, Priority: ${report.assessmentSummary.priority}, Urgent Care Required: ${report.assessmentSummary.urgentCare ? 'Yes' : 'No'}. Recommendations: ${report.aiRecommendations.immediate.slice(0, 2).join(', ')}.`;

        // Create AI assessment for medical records
        const aiAssessment = {
          symptoms: report.symptomsReported.primary,
          severity: report.assessmentSummary.severity === 'critical' ? 'Critical' as const :
                   report.assessmentSummary.severity === 'high' ? 'High' as const :
                   report.assessmentSummary.severity === 'moderate' ? 'Medium' as const : 'Low' as const,
          recommendations: [...report.aiRecommendations.immediate, ...report.aiRecommendations.shortTerm].slice(0, 5),
          triageDecision: report.assessmentSummary.urgentCare ? 'Urgent Care' as const :
                         report.followUpPlan.appointmentNeeded ? 'Schedule Appointment' as const : 'Self-Care' as const
        };

        // Update the session with AI assessment before ending
        const session = chatInteractionService.getSession(chatSessionId);
        if (session) {
          session.aiAssessment = aiAssessment;
        }

        await chatInteractionService.endChatSession(chatSessionId, summary);
        setChatSessionId(null); // Clear session ID

        console.log(`✅ AI Health Assistant Chat session ended and saved to medical records with AI assessment`);
      }

      // Create patient case for healthcare provider dashboard using real patient data
      const currentPatientId = localStorage.getItem('currentPatientId') || 'UNKNOWN_PATIENT';
      const currentPatientIdNumber = localStorage.getItem('currentPatientIdNumber') || 'UNKNOWN_ID';
      const currentPatientName = localStorage.getItem('currentPatientName') || 'Unknown Patient';
      const currentPatientAge = parseInt(localStorage.getItem('currentPatientAge') || '0');
      const currentPatientLocation = localStorage.getItem('currentPatientLocation') || 'Unknown Location';

      // Ensure the patient is properly registered in the system
      try {
        const { patientRegistrationService } = await import('@/services/patientRegistrationService');
        const existingPatient = patientRegistrationService.getPatientByIdNumber(currentPatientIdNumber);

        if (!existingPatient) {
          console.warn(`⚠️ Patient ${currentPatientIdNumber} not found in registration system during medical report generation`);
        } else {
          console.log(`✅ Patient ${existingPatient.patient.personalInfo.firstName} ${existingPatient.patient.personalInfo.lastName} found in system`);
        }
      } catch (error) {
        console.error('❌ Error checking patient registration:', error);
      }

      const patientCase = patientCaseService.createCase(
        currentPatientId,
        currentPatientName,
        currentPatientAge,
        currentPatientLocation,
        language === 'en' ? 'English' : language === 'zu' ? 'isiZulu' : 'Afrikaans',
        report,
        conversation
      );

      console.log('✅ Patient case created:', patientCase);

      // Handle urgent cases - start monitoring and notify healthcare providers
      if (report.assessmentSummary.urgentCare) {
        console.log('🚨 URGENT CASE DETECTED - Starting monitoring and provider notification');

        // Start AI monitoring for urgent case
        try {
          const { intelligentMonitoringAgent } = await import('@/services/intelligentMonitoringAgent');
          const monitoringSessionId = intelligentMonitoringAgent.startMonitoringForCase(patientCase);
          console.log(`🤖 AI monitoring started for urgent case: ${monitoringSessionId}`);

          // Show urgent care alert to patient
          alert(`🚨 URGENT MEDICAL ATTENTION REQUIRED

Your symptoms indicate you need immediate medical care.

✅ Your case has been flagged as URGENT
✅ Healthcare providers have been notified
✅ AI monitoring has been activated
✅ You will receive check-in messages

IMMEDIATE ACTIONS:
• Call emergency services (10177) if symptoms worsen
• Go to nearest hospital if condition deteriorates
• Respond to AI check-in messages
• Healthcare provider will contact you shortly

Your safety is our priority.`);

        } catch (error) {
          console.error('❌ Error starting monitoring for urgent case:', error);
          alert('Your case has been flagged as urgent and sent to healthcare providers for immediate review.');
        }
      } else {
        // Non-urgent case notification
        const severityMessage = report.assessmentSummary.severity === 'high' ?
          'Your case has been sent to healthcare providers for review within 24 hours.' :
          'Your medical report has been generated and saved. Follow the recommendations provided.';

        alert(`✅ Medical Report Generated

${severityMessage}

You can view your complete report below.`);
      }

      setShowReport(true);
    } catch (error) {
      console.error('❌ Error generating medical report:', error);

      // Show specific error message to user
      const errorMsg = language === 'zu' ?
        'Asikwazi ukudala umbiko wezokwelapha manje. Sicela uzame futhi noma ushayele ku-10010 uma lokhu kuphuthumayo.' :
        language === 'af' ?
        'Ons kan nie nou \'n mediese verslag genereer nie. Probeer asseblief weer of skakel 10010 as dit dringend is.' :
        'Unable to generate medical report at this time. Please try again or call 10010 if this is urgent.';

      alert(errorMsg);
    } finally {
      setIsGeneratingReport(false);
    }
  };

  if (showReport && reportData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 p-4">
        <div className="max-w-4xl mx-auto space-y-6">
          <Button variant="ghost" onClick={() => setShowReport(false)} className="mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Chat
          </Button>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Medical Assessment Report
              </CardTitle>
              <CardDescription>AI-Generated Assessment - To be reviewed by healthcare professional</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-2">Patient Information</h3>
                  <p><strong>Name:</strong> {localStorage.getItem('currentPatientName') || 'Unknown Patient'}</p>
                  <p><strong>ID:</strong> {localStorage.getItem('currentPatientIdNumber') || 'Unknown ID'}</p>
                  <p><strong>Date:</strong> {new Date().toLocaleDateString()}</p>
                  <p><strong>Time:</strong> {new Date().toLocaleTimeString()}</p>
                  <p><strong>Language:</strong> {language === 'en' ? 'English' : language === 'zu' ? 'Zulu' : 'Afrikaans'}</p>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Assessment Summary</h3>
                  <Badge className={getSeverityColor(reportData.assessmentSummary.severity)}>
                    {reportData.assessmentSummary.priority}
                  </Badge>
                  <p className="text-sm mt-2">{reportData.assessmentSummary.description}</p>
                  {reportData.assessmentSummary.urgentCare && (
                    <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded-md">
                      <div className="flex items-center gap-2 text-red-700">
                        <AlertTriangle className="w-4 h-4" />
                        <span className="font-medium text-sm">Urgent Care Required</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Conversation Summary</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm">{reportData.conversationSummary}</p>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Symptoms Reported</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2 text-sm">Primary Symptoms:</h4>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      {reportData.symptomsReported.primary.map((symptom, index) => (
                        <li key={index}>{symptom}</li>
                      ))}
                    </ul>
                  </div>
                  {reportData.symptomsReported.secondary.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2 text-sm">Secondary Symptoms:</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm">
                        {reportData.symptomsReported.secondary.map((symptom, index) => (
                          <li key={index}>{symptom}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
                <div className="mt-3 text-sm text-gray-600">
                  <p><strong>Duration:</strong> {reportData.symptomsReported.duration}</p>
                  <p><strong>Onset:</strong> {reportData.symptomsReported.onset}</p>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">AI Recommendations</h3>
                <div className="space-y-4">
                  <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                    <h4 className="font-medium mb-2 text-red-800 flex items-center gap-2">
                      <AlertTriangle className="w-4 h-4" />
                      Immediate Care:
                    </h4>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      {reportData.aiRecommendations.immediate.map((rec, index) => (
                        <li key={index}>{rec}</li>
                      ))}
                    </ul>
                  </div>

                  <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                    <h4 className="font-medium mb-2 text-yellow-800 flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      Short-term (24-48 hours):
                    </h4>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      {reportData.aiRecommendations.shortTerm.map((rec, index) => (
                        <li key={index}>{rec}</li>
                      ))}
                    </ul>
                  </div>

                  <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                    <h4 className="font-medium mb-2 text-green-800 flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Long-term Care:
                    </h4>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      {reportData.aiRecommendations.longTerm.map((rec, index) => (
                        <li key={index}>{rec}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Follow-up Plan</h3>
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="flex items-center gap-2 mb-2">
                        <Calendar className="w-4 h-4 text-blue-600" />
                        <strong>Appointment Needed:</strong> {reportData.followUpPlan.appointmentNeeded ? 'Yes' : 'No'}
                      </p>
                      <p className="flex items-center gap-2 mb-2">
                        <Clock className="w-4 h-4 text-blue-600" />
                        <strong>Timeframe:</strong> {reportData.followUpPlan.timeframe}
                      </p>
                      <p className="flex items-center gap-2 mb-2">
                        <MapPin className="w-4 h-4 text-blue-600" />
                        <strong>Location:</strong> {reportData.followUpPlan.location}
                      </p>
                    </div>
                    <div>
                      <p className="flex items-center gap-2 mb-2">
                        <Package className="w-4 h-4 text-blue-600" />
                        <strong>Medication Delivery:</strong> {reportData.followUpPlan.medicationDelivery ? 'Arranged' : 'Not needed'}
                      </p>
                      <p className="flex items-center gap-2 mb-2">
                        <Phone className="w-4 h-4 text-blue-600" />
                        <strong>Next Check-in:</strong> {reportData.followUpPlan.nextCheckIn}
                      </p>
                    </div>
                  </div>
                  {reportData.followUpPlan.additionalNotes.length > 0 && (
                    <div className="mt-3">
                      <h4 className="font-medium mb-2">Additional Notes:</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm">
                        {reportData.followUpPlan.additionalNotes.map((note, index) => (
                          <li key={index}>{note}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>

              {(reportData.riskFactors.length > 0 || reportData.redFlags.length > 0) && (
                <div>
                  <h3 className="font-semibold mb-2">Risk Assessment</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {reportData.riskFactors.length > 0 && (
                      <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                        <h4 className="font-medium mb-2 text-orange-800">Risk Factors:</h4>
                        <ul className="list-disc list-inside space-y-1 text-sm">
                          {reportData.riskFactors.map((factor, index) => (
                            <li key={index}>{factor}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {reportData.redFlags.length > 0 && (
                      <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                        <h4 className="font-medium mb-2 text-red-800 flex items-center gap-2">
                          <AlertTriangle className="w-4 h-4" />
                          Red Flags:
                        </h4>
                        <ul className="list-disc list-inside space-y-1 text-sm">
                          {reportData.redFlags.map((flag, index) => (
                            <li key={index}>{flag}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="flex gap-4">
                <Button onClick={onBack} className="flex-1">
                  Return to Dashboard
                </Button>
                <Button variant="outline" className="flex-1">
                  Schedule Appointment
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-purple-900/20 via-slate-900 to-black"></div>
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]"></div>

      <div className="relative z-10 h-screen flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-2 bg-black/20 backdrop-blur-xl border-b border-white/10">
          <Button
            variant="ghost"
            onClick={onBack}
            className="text-white hover:bg-white/10 border border-white/20 text-xs px-2 py-1"
          >
            <ArrowLeft className="w-3 h-3 mr-1" />
            {getLanguageText('back')}
          </Button>

          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <Bot className="w-3 h-3 text-white" />
            </div>
            <div className="text-center">
              <h1 className="text-white font-bold text-sm">{getLanguageText('healthAssistant')}</h1>
              <p className="text-gray-400 text-xs">{getLanguageText('poweredBy')}</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {currentSeverity !== 'low' && (
              <Badge className={`${getSeverityColor(currentSeverity)} text-xs px-1 py-0`}>
                {getSeverityIcon(currentSeverity)}
                <span className="ml-1 capitalize">{currentSeverity}</span>
              </Badge>
            )}
          </div>
        </div>
          
        {/* Chat Messages Container */}
        <div className="flex-1 flex flex-col min-h-0">
          {/* Messages Area with Fixed Height and Scroll */}
          <div className="flex-1 overflow-y-auto px-2 py-2 space-y-2 scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
            {conversation.map((message) => (
              <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} animate-fade-in-scale`}>
                <div className={`max-w-[90%] sm:max-w-[75%] ${
                  message.role === 'user'
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl rounded-br-sm'
                    : 'bg-white/10 backdrop-blur-xl border border-white/20 text-white rounded-xl rounded-bl-sm'
                } p-2 shadow-lg`}>

                  {/* Message Header */}
                  <div className="flex items-center gap-1.5 mb-1">
                    {message.role === 'assistant' ? (
                      <div className="w-4 h-4 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full flex items-center justify-center">
                        <Bot className="w-2 h-2 text-white" />
                      </div>
                    ) : (
                      <div className="w-4 h-4 bg-white/20 rounded-full flex items-center justify-center">
                        <User className="w-2 h-2 text-white" />
                      </div>
                    )}
                    <span className="text-xs font-medium opacity-80">
                      {message.role === 'assistant' ? 'AI Assistant' : 'You'}
                    </span>
                    <span className="text-xs opacity-60 ml-auto">
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </span>
                  </div>

                  {/* Message Content */}
                  <p className="text-xs leading-snug whitespace-pre-wrap">{message.content}</p>
                </div>
              </div>
            ))}

            {/* Loading indicator */}
            {isLoading && (
              <div className="flex justify-start animate-fade-in-scale">
                <div className="max-w-[90%] sm:max-w-[75%] bg-white/10 backdrop-blur-xl border border-white/20 text-white rounded-xl rounded-bl-sm p-2 shadow-lg">
                  <div className="flex items-center gap-1.5 mb-1">
                    <div className="w-4 h-4 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full flex items-center justify-center">
                      <Bot className="w-2 h-2 text-white" />
                    </div>
                    <span className="text-xs font-medium opacity-80">AI Assistant</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex space-x-1">
                      <div className="w-1 h-1 bg-blue-400 rounded-full animate-bounce"></div>
                      <div className="w-1 h-1 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                      <div className="w-1 h-1 bg-pink-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                    </div>
                    <span className="text-xs opacity-70">{getLanguageText('aiTyping')}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Invisible element to scroll to */}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="p-2 bg-black/20 backdrop-blur-xl border-t border-white/10">
            {/* Quick Actions */}
            <div className="flex flex-wrap gap-1 mb-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentMessage(getLanguageText('emergencyHelp'))}
                className="bg-red-500/20 text-red-300 border-red-500/30 hover:bg-red-500/30 backdrop-blur-sm text-xs px-1.5 py-0.5 h-6"
              >
                <AlertTriangle className="w-2.5 h-2.5 mr-0.5" />
                {getLanguageText('emergency')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentMessage(getLanguageText('generalCheckup'))}
                className="bg-white/10 text-white border-white/20 hover:bg-white/20 backdrop-blur-sm text-xs px-1.5 py-0.5 h-6"
              >
                <Stethoscope className="w-2.5 h-2.5 mr-0.5" />
                {getLanguageText('checkup')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentMessage(getLanguageText('medicationHelp'))}
                className="bg-white/10 text-white border-white/20 hover:bg-white/20 backdrop-blur-sm text-xs px-1.5 py-0.5 h-6"
              >
                <Pill className="w-2.5 h-2.5 mr-0.5" />
                {getLanguageText('medication')}
              </Button>
            </div>

            {/* Message Input */}
            <div className="flex gap-1.5 items-end">
              <div className="flex-1 relative">
                <Textarea
                  value={currentMessage}
                  onChange={(e) => setCurrentMessage(e.target.value)}
                  placeholder={getLanguageText('typeMessage')}
                  className="min-h-[32px] max-h-[80px] resize-none bg-white/10 backdrop-blur-xl border-white/20 text-white placeholder:text-gray-400 rounded-lg pr-8 text-xs focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50"
                  onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && (e.preventDefault(), sendMessage())}
                  disabled={isLoading}
                />
                <Button
                  size="sm"
                  className="absolute right-1 bottom-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-full w-6 h-6 p-0"
                  onClick={sendMessage}
                  disabled={!currentMessage.trim() || isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="w-2.5 h-2.5 animate-spin" />
                  ) : (
                    <Send className="w-2.5 h-2.5" />
                  )}
                </Button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-1 mt-2">
              <Button variant="outline" className="flex-1 flex items-center gap-1 bg-white/10 text-white border-white/20 hover:bg-white/20 backdrop-blur-sm text-xs py-1 h-7">
                <Phone className="w-2.5 h-2.5" />
                {getLanguageText('callOption')}
              </Button>
              <Button
                onClick={generateMedicalReport}
                className="flex-1 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white text-xs py-1 h-7"
                disabled={conversation.length < 3 || isGeneratingReport}
              >
                {isGeneratingReport ? (
                  <>
                    <Loader2 className="w-2.5 h-2.5 animate-spin mr-1" />
                    {language === 'zu' ? 'Kudala...' : language === 'af' ? 'Genereer...' : 'Generating...'}
                  </>
                ) : (
                  getLanguageText('generateReport')
                )}
              </Button>
            </div>

            {/* Emergency Notice */}
            {currentSeverity === 'critical' && (
              <div className="mt-2 p-1.5 bg-red-500/20 border border-red-500/30 rounded-md backdrop-blur-sm">
                <div className="flex items-center gap-1 text-red-300">
                  <AlertTriangle className="w-3 h-3" />
                  <span className="font-medium text-xs">Urgent Medical Attention Recommended</span>
                </div>
                <p className="text-xs text-red-200 mt-0.5">
                  Based on your symptoms, please seek immediate medical care or call emergency services.
                </p>
              </div>
            )}

            {/* Status Bar */}
            <div className="flex items-center justify-between mt-1 text-xs text-gray-400">
              <span className="text-xs">End-to-end encrypted • Communicate in your preferred language</span>
              <span className="flex items-center gap-1">
                <div className="w-1 h-1 bg-green-400 rounded-full animate-pulse"></div>
                Online
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
