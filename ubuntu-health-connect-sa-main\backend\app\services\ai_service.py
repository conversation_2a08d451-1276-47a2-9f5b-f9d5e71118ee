"""
AI Service - Business logic for AI interactions
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from app.database.db_manager import DatabaseManager

class AIService:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def create_interaction(self, interaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create AI interaction record
        """
        try:
            # Ensure required fields
            if not interaction_data.get('id'):
                interaction_data['id'] = str(uuid.uuid4())
            
            # Save to database
            success = self.db.create_ai_interaction(interaction_data)
            
            if success:
                return {
                    'success': True,
                    'interaction_id': interaction_data['id'],
                    'message': 'AI interaction recorded successfully'
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to create AI interaction'
                }
        
        except Exception as e:
            return {
                'success': False,
                'error': f'AI service error: {str(e)}'
            }
    
    def get_patient_interactions(self, patient_id: str) -> List[Dict[str, Any]]:
        """
        Get all AI interactions for a patient
        """
        try:
            interactions = self.db.get_ai_interactions_by_patient(patient_id)
            return interactions
        
        except Exception as e:
            raise Exception(f'Error getting AI interactions: {str(e)}')
    
    def get_interaction_by_id(self, interaction_id: str) -> Optional[Dict[str, Any]]:
        """
        Get specific AI interaction by ID
        """
        try:
            # This would need to be implemented in db_manager
            # For now, return None
            return None
        
        except Exception as e:
            raise Exception(f'Error getting AI interaction: {str(e)}')
    
    def update_interaction(self, interaction_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update AI interaction
        """
        try:
            # This would need to be implemented in db_manager
            # For now, return success
            return {
                'success': True,
                'interaction_id': interaction_id,
                'message': 'AI interaction updated successfully'
            }
        
        except Exception as e:
            return {
                'success': False,
                'error': f'Update error: {str(e)}'
            }
    
    def get_all_interactions(self) -> List[Dict[str, Any]]:
        """
        Get all AI interactions (for urgent cases dashboard)
        """
        try:
            # Get all interactions from database
            interactions = self.db.get_all_ai_interactions()
            return interactions

        except Exception as e:
            raise Exception(f'Error getting all AI interactions: {str(e)}')

    def clear_all_interactions(self) -> Dict[str, Any]:
        """
        Clear all AI interactions (for testing)
        """
        try:
            success = self.db.clear_all_ai_interactions()

            if success:
                return {
                    'success': True,
                    'message': 'All AI interactions cleared successfully'
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to clear AI interactions'
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'Clear error: {str(e)}'
            }

    def analyze_conversation(self, conversation: str) -> Dict[str, Any]:
        """
        Analyze conversation for medical insights
        """
        try:
            # This would integrate with OpenAI API
            # For now, return basic analysis
            return {
                'severity': 'Low',
                'urgent_care': False,
                'recommendations': 'Continue monitoring symptoms',
                'summary': 'Patient reported mild symptoms'
            }

        except Exception as e:
            raise Exception(f'Error analyzing conversation: {str(e)}')
