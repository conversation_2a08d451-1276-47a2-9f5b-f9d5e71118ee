// Healthcare Database Service
// Manages patient data, medical records, and AI interactions

export interface DatabasePatient {
  id: string;
  personalInfo: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    age: number;
    gender: 'Male' | 'Female' | 'Other';
    idNumber: string;
    phone: string;
    email: string;
    address: string;
    emergencyContact: {
      name: string;
      relationship: string;
      phone: string;
    };
  };
  healthcareProviders: string[]; // Provider IDs
  medicalHistory: MedicalCondition[];
  aiInteractions: string[]; // AI Interaction IDs
  medications: Medication[];
  allergies: Allergy[];
  vaccinations: Vaccination[];
  labResults: LabResult[];
  appointments: Appointment[];
  insurance: InsuranceInfo;
  createdAt: string;
  updatedAt: string;
}

export interface MedicalCondition {
  id: string;
  condition: string;
  diagnosedDate: string;
  diagnosedBy: string;
  severity: 'Mild' | 'Moderate' | 'Severe' | 'Critical';
  status: 'Active' | 'Resolved' | 'Chronic' | 'Under Treatment';
  notes?: string;
  medications?: string[];
}

export interface Medication {
  name: string;
  dosage: string;
  frequency: string;
  startDate: string;
  endDate?: string;
  prescribedBy: string;
}

export interface Allergy {
  allergen: string;
  severity: 'Mild' | 'Moderate' | 'Severe' | 'Life-threatening';
  reaction: string;
  diagnosedDate: string;
}

export interface Vaccination {
  vaccine: string;
  date: string;
  provider: string;
  batchNumber?: string;
}

export interface LabResult {
  id: string;
  testName: string;
  result: string;
  normalRange: string;
  date: string;
  orderedBy: string;
}

export interface Appointment {
  id: string;
  providerId: string;
  date: string;
  type: string;
  status: 'Scheduled' | 'Completed' | 'Cancelled' | 'No Show';
  notes?: string;
}

export interface InsuranceInfo {
  provider?: string;
  policyNumber?: string;
  isActive?: boolean;
}

export interface ChatInteraction {
  id: string;
  patientId: string;
  type: 'AI Health Assistant Chat' | 'Voice Triage' | 'Chat Monitor';
  timestamp: string;
  duration?: string;
  language: string;
  messages: {
    id: string;
    sender: 'patient' | 'ai' | 'provider';
    content: string;
    timestamp: string;
    type: 'text' | 'audio' | 'image';
  }[];
  summary: string;
  aiAssessment?: {
    symptoms: string[];
    severity: 'Low' | 'Medium' | 'High' | 'Critical';
    recommendations: string[];
    triageDecision: 'Self-Care' | 'Schedule Appointment' | 'Urgent Care' | 'Emergency';
  };
  status: 'active' | 'completed' | 'escalated';
}

class HealthcareDatabaseService {
  private patients: Map<string, DatabasePatient> = new Map();
  private aiInteractions: Map<string, ChatInteraction> = new Map();
  private patientProviderAccess: Map<string, Set<string>> = new Map(); // Patient ID -> Provider IDs

  constructor() {
    this.initializeDatabase();
  }

  /**
   * Initialize database - no mock data, only real patient data
   */
  private initializeDatabase() {
    // Real patients and AI interactions are added via registration service and chat interactions
    console.log('Healthcare Database initialized - ready for real patient data');
  }

  /**
   * Get patient by ID with provider access control
   */
  async getPatientById(patientId: string, providerId: string): Promise<DatabasePatient | null> {
    try {
      const patient = this.patients.get(patientId);
      if (!patient) return null;

      // Check provider access
      if (providerId !== 'SYSTEM' && !this.hasProviderAccess(patientId, providerId)) {
        console.warn(`Provider ${providerId} does not have access to patient ${patientId}`);
        return null;
      }

      return patient;
    } catch (error) {
      console.error('Error getting patient by ID:', error);
      return null;
    }
  }

  /**
   * Search patients by criteria with provider access control
   */
  async searchPatients(criteria: any, providerId: string): Promise<DatabasePatient[]> {
    try {
      const results: DatabasePatient[] = [];
      
      for (const [patientId, patient] of this.patients) {
        // Check provider access
        if (providerId !== 'SYSTEM' && !this.hasProviderAccess(patientId, providerId)) {
          continue;
        }

        // Apply search criteria
        let matches = false;
        
        if (criteria.idNumber && patient.personalInfo.idNumber.includes(criteria.idNumber)) {
          matches = true;
        }
        
        if (criteria.phone && patient.personalInfo.phone.includes(criteria.phone)) {
          matches = true;
        }
        
        if (criteria.firstName && 
            patient.personalInfo.firstName.toLowerCase().includes(criteria.firstName.toLowerCase())) {
          matches = true;
        }
        
        if (criteria.lastName && 
            patient.personalInfo.lastName.toLowerCase().includes(criteria.lastName.toLowerCase())) {
          matches = true;
        }
        
        if (criteria.patientId && patient.id.toLowerCase().includes(criteria.patientId.toLowerCase())) {
          matches = true;
        }
        
        if (matches) {
          results.push(patient);
        }
      }
      
      return results;
    } catch (error) {
      console.error('Error searching patients:', error);
      return [];
    }
  }

  /**
   * Add or update patient
   */
  async addOrUpdatePatient(patient: DatabasePatient): Promise<boolean> {
    try {
      this.patients.set(patient.id, patient);
      
      // Grant access to all providers in the patient's provider list
      patient.healthcareProviders.forEach(providerId => {
        this.grantProviderAccess(patient.id, providerId);
      });
      
      console.log(`✅ Added/updated patient: ${patient.id}`);
      return true;
    } catch (error) {
      console.error('Error adding/updating patient:', error);
      return false;
    }
  }

  /**
   * Get AI interactions for a patient
   */
  async getPatientAIInteractions(patientId: string, providerId: string): Promise<ChatInteraction[]> {
    try {
      // Check provider access
      if (providerId !== 'SYSTEM' && !this.hasProviderAccess(patientId, providerId)) {
        console.warn(`Provider ${providerId} does not have access to patient ${patientId} AI interactions`);
        return [];
      }

      const interactions: ChatInteraction[] = [];
      
      for (const interaction of this.aiInteractions.values()) {
        if (interaction.patientId === patientId) {
          interactions.push(interaction);
        }
      }
      
      // Sort by timestamp (newest first)
      return interactions.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );
    } catch (error) {
      console.error('Error getting patient AI interactions:', error);
      return [];
    }
  }

  /**
   * Add AI interaction
   */
  async addAIInteraction(interaction: ChatInteraction): Promise<boolean> {
    try {
      this.aiInteractions.set(interaction.id, interaction);
      
      // Add interaction ID to patient's AI interactions list
      const patient = this.patients.get(interaction.patientId);
      if (patient) {
        if (!patient.aiInteractions.includes(interaction.id)) {
          patient.aiInteractions.push(interaction.id);
          patient.updatedAt = new Date().toISOString();
          this.patients.set(patient.id, patient);
        }
      }
      
      console.log(`✅ Added AI interaction: ${interaction.id} for patient: ${interaction.patientId}`);
      return true;
    } catch (error) {
      console.error('Error adding AI interaction:', error);
      return false;
    }
  }

  /**
   * Get AI interactions summary for a patient
   */
  async getAIInteractionsSummary(patientId: string, providerId: string): Promise<string> {
    try {
      const interactions = await this.getPatientAIInteractions(patientId, providerId);
      
      if (interactions.length === 0) {
        return 'No AI interactions recorded for this patient.';
      }

      let summary = `AI Interactions Summary for Patient ${patientId}\n`;
      summary += `Total Interactions: ${interactions.length}\n\n`;

      interactions.forEach((interaction, index) => {
        summary += `${index + 1}. ${interaction.type} - ${new Date(interaction.timestamp).toLocaleString()}\n`;
        summary += `   Status: ${interaction.status}\n`;
        summary += `   Summary: ${interaction.summary}\n`;
        
        if (interaction.aiAssessment) {
          summary += `   Severity: ${interaction.aiAssessment.severity}\n`;
          summary += `   Symptoms: ${interaction.aiAssessment.symptoms.join(', ')}\n`;
          summary += `   Decision: ${interaction.aiAssessment.triageDecision}\n`;
        }
        
        summary += '\n';
      });

      return summary;
    } catch (error) {
      console.error('Error getting AI interactions summary:', error);
      return 'Error generating AI interactions summary.';
    }
  }

  /**
   * Check if provider has access to patient
   */
  private hasProviderAccess(patientId: string, providerId: string): boolean {
    const providerAccess = this.patientProviderAccess.get(patientId);
    return providerAccess ? providerAccess.has(providerId) : false;
  }

  /**
   * Grant provider access to patient
   */
  private grantProviderAccess(patientId: string, providerId: string): void {
    if (!this.patientProviderAccess.has(patientId)) {
      this.patientProviderAccess.set(patientId, new Set());
    }
    this.patientProviderAccess.get(patientId)!.add(providerId);
  }

  /**
   * Get all patients (system access only)
   */
  async getAllPatients(): Promise<DatabasePatient[]> {
    return Array.from(this.patients.values());
  }

  /**
   * Get database statistics
   */
  getStatistics() {
    return {
      totalPatients: this.patients.size,
      totalAIInteractions: this.aiInteractions.size,
      patientsWithAIInteractions: new Set(
        Array.from(this.aiInteractions.values()).map(i => i.patientId)
      ).size
    };
  }
}

export const healthcareDatabase = new HealthcareDatabaseService();
export default healthcareDatabase;
