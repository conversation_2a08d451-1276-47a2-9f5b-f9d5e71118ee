"""
HealthConnect SA Voice Assistant
A Flask application that provides voice-based health assistance in South African languages
using OpenAI Agents SDK and <PERSON>wi<PERSON> for phone integration.
"""

import os
import json
import logging
import uuid
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS

# Import database manager
from database.db_manager import DatabaseManager

# Initialize Flask app
app = Flask(__name__)

# Enable CORS for frontend-backend communication
CORS(app, origins=['http://localhost:8081', 'http://localhost:8082', 'http://127.0.0.1:8081', 'http://127.0.0.1:8082'])

# Initialize database
db = DatabaseManager()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize OpenAI client
try:
    # Check if API key is set properly
    if not OPENAI_API_KEY or OPENAI_API_KEY.startswith('PUT_YOUR_'):
        logger.warning("OpenAI API key not set properly. Please update config/settings.py")
        openai_client = None
    else:
        openai_client = OpenAI(api_key=OPENAI_API_KEY)
        logger.info("OpenAI client initialized successfully")
except Exception as e:
    logger.error(f"Error initializing OpenAI client: {e}")
    openai_client = None

# Initialize Twilio client
twilio_client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

# Ensure logs directory exists
os.makedirs('logs', exist_ok=True)

class HealthAssistant:
    """
    Health Assistant class that manages OpenAI Assistant interactions
    """
    
    def __init__(self):
        self.assistant_id = None
        self.thread_id = None
        self.setup_assistant()
    
    def setup_assistant(self):
        """
        Create and configure the OpenAI Assistant for health consultations
        """
        try:
            # Check if OpenAI client is available
            if not openai_client:
                logger.error("OpenAI client not available. Please check your API key.")
                return

            # Create assistant with health-focused instructions
            assistant = openai_client.beta.assistants.create(
                name="HealthConnect SA Voice Assistant",
                instructions="""
                You are a compassionate, multilingual health assistant for HealthConnect SA, 
                serving the people of South Africa. You speak isiZulu fluently and can help 
                users understand their health symptoms.

                Your role:
                1. Listen carefully to health symptoms and concerns
                2. Ask relevant follow-up questions to understand the situation better
                3. Provide helpful, empathetic responses in isiZulu
                4. Suggest appropriate next steps (see a doctor, emergency care, home remedies)
                5. Never diagnose - always recommend professional medical consultation for serious concerns
                6. Be culturally sensitive and respectful
                7. Keep responses concise for voice interaction (2-3 sentences max)

                Language Guidelines:
                - Respond primarily in isiZulu
                - Use simple, clear language that elderly users can understand
                - Include common isiZulu greetings and polite expressions
                - If user speaks English, you may respond in English but offer isiZulu

                Safety Guidelines:
                - For emergency symptoms (chest pain, difficulty breathing, severe bleeding), 
                  immediately advise calling emergency services
                - Always recommend seeing a healthcare professional for persistent symptoms
                - Provide emotional support and reassurance

                Start conversations with: "Sawubona! Ngingakusiza kanjani namuhla ngesimo sakho sezempilo?"
                (Hello! How can I help you today with your health situation?)
                """,
                model="gpt-4o-mini",
                tools=[]
            )
            
            self.assistant_id = assistant.id
            logger.info(f"Health Assistant created with ID: {self.assistant_id}")
            
        except Exception as e:
            logger.error(f"Error creating assistant: {str(e)}")
            # Don't raise the exception, just log it and continue
            # The app can still run for basic functionality
            self.assistant_id = None
            logger.warning("Assistant creation failed. Voice functionality will be limited.")
    
    def create_thread(self):
        """
        Create a new conversation thread
        """
        try:
            if not openai_client:
                logger.error("OpenAI client not available for creating thread")
                return None

            thread = openai_client.beta.threads.create()
            self.thread_id = thread.id
            logger.info(f"New thread created: {self.thread_id}")
            return self.thread_id
        except Exception as e:
            logger.error(f"Error creating thread: {str(e)}")
            return None
    
    def send_message(self, message, thread_id=None):
        """
        Send a message to the assistant and get response
        """
        try:
            # Check if OpenAI client and assistant are available
            if not openai_client or not self.assistant_id:
                logger.error("OpenAI client or assistant not available")
                return "Ngiyaxolisa, angikwazi ukukusiza manje. Sicela uzame futhi. (Sorry, I can't help you right now. Please try again.)", thread_id

            if not thread_id:
                thread_id = self.thread_id or self.create_thread()
            
            # Add user message to thread
            openai_client.beta.threads.messages.create(
                thread_id=thread_id,
                role="user",
                content=message
            )
            
            # Run the assistant
            run = openai_client.beta.threads.runs.create(
                thread_id=thread_id,
                assistant_id=self.assistant_id
            )
            
            # Wait for completion
            while run.status in ['queued', 'in_progress']:
                run = openai_client.beta.threads.runs.retrieve(
                    thread_id=thread_id,
                    run_id=run.id
                )
            
            if run.status == 'completed':
                # Get the assistant's response
                messages = openai_client.beta.threads.messages.list(
                    thread_id=thread_id
                )
                
                # Get the latest assistant message
                for msg in messages.data:
                    if msg.role == "assistant":
                        response = msg.content[0].text.value
                        logger.info(f"Assistant response: {response}")
                        return response, thread_id
            
            logger.error(f"Run failed with status: {run.status}")
            return "Ngiyaxolisa, kukhona inkinga. Sicela uzame futhi. (Sorry, there's a problem. Please try again.)", thread_id
            
        except Exception as e:
            logger.error(f"Error sending message: {str(e)}")
            return "Ngiyaxolisa, angikwazi ukukusiza manje. (Sorry, I can't help you right now.)", thread_id

# Initialize the health assistant
health_assistant = HealthAssistant()

def log_conversation(caller_number, user_input, ai_response, thread_id):
    """
    Log conversation to JSON file for record keeping
    """
    try:
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "caller_number": caller_number,
            "thread_id": thread_id,
            "user_input": user_input,
            "ai_response": ai_response
        }
        
        log_file = f"logs/conversation_log.json"
        
        # Read existing logs or create new list
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                logs = json.load(f)
        else:
            logs = []
        
        # Add new entry
        logs.append(log_entry)
        
        # Write back to file
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(logs, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Conversation logged for {caller_number}")
        
    except Exception as e:
        logger.error(f"Error logging conversation: {str(e)}")

@app.route('/voice', methods=['POST'])
def handle_voice_call():
    """
    Main Twilio webhook endpoint for handling voice calls
    """
    try:
        # Get caller information
        caller_number = request.form.get('From', 'Unknown')
        call_sid = request.form.get('CallSid', 'Unknown')
        
        logger.info(f"🔥 TEST MODE: Incoming call from {caller_number}, Call SID: {call_sid}")

        # Create TwiML response
        response = VoiceResponse()

        # Welcome message in English for testing (easier to understand)
        welcome_message = """
        Hello! Welcome to HealthConnect SA Voice Assistant.
        This is a test call. I am your health assistant.
        Please tell me about your health concern and I will help you.
        """
        
        response.say(
            welcome_message,
            voice='alice',
            language='en'  # Note: Twilio doesn't have native isiZulu, we'll use English voice
        )
        
        # Start recording and transcription
        response.record(
            action='/process_speech',
            method='POST',
            max_length=30,  # 30 seconds max per recording
            transcribe=True,
            transcribe_callback='/transcription'
        )
        
        return str(response)
        
    except Exception as e:
        logger.error(f"Error in voice handler: {str(e)}")
        response = VoiceResponse()
        response.say("Sorry, there was an error. Please try again later.")
        return str(response)

@app.route('/process_speech', methods=['POST'])
def process_speech():
    """
    Process the recorded speech and continue conversation
    """
    try:
        # Get transcription (if available immediately)
        transcription = request.form.get('TranscriptionText', '')
        caller_number = request.form.get('From', 'Unknown')
        
        logger.info(f"Processing speech from {caller_number}: {transcription}")
        
        response = VoiceResponse()
        
        if transcription:
            # Get AI response
            ai_response, thread_id = health_assistant.send_message(transcription)
            
            # Log the conversation
            log_conversation(caller_number, transcription, ai_response, thread_id)
            
            # Speak the AI response
            response.say(ai_response, voice='alice', language='en')
            
            # Continue conversation - record next input
            response.record(
                action='/process_speech',
                method='POST',
                max_length=30,
                transcribe=True,
                transcribe_callback='/transcription'
            )
        else:
            # No transcription available, ask user to repeat
            response.say(
                "Ngiyaxolisa, angizwanga kahle. Sicela uphinde ukhulume. (Sorry, I didn't hear clearly. Please speak again.)",
                voice='alice',
                language='en'
            )
            response.record(
                action='/process_speech',
                method='POST',
                max_length=30,
                transcribe=True
            )
        
        return str(response)
        
    except Exception as e:
        logger.error(f"Error processing speech: {str(e)}")
        response = VoiceResponse()
        response.say("There was an error processing your request. Please try again.")
        return str(response)

@app.route('/transcription', methods=['POST'])
def handle_transcription():
    """
    Handle transcription callbacks from Twilio
    """
    try:
        transcription = request.form.get('TranscriptionText', '')
        caller_number = request.form.get('From', 'Unknown')
        
        logger.info(f"Transcription received from {caller_number}: {transcription}")
        
        # This is just for logging - the main processing happens in /process_speech
        return jsonify({"status": "received"})
        
    except Exception as e:
        logger.error(f"Error handling transcription: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint with database status
    """
    try:
        # Test database connection
        db_connected = db.test_connection()
        db_stats = db.get_database_stats() if db_connected else {}

        return jsonify({
            "status": "healthy" if db_connected else "degraded",
            "service": "HealthConnect SA Voice Assistant",
            "timestamp": datetime.now().isoformat(),
            "database": {
                "connected": db_connected,
                "stats": db_stats
            },
            "features": {
                "voice_triage": True,
                "patient_api": True,
                "frontend_backend_integration": True,
                "database_persistence": db_connected
            },
            "endpoints": {
                "voice": "/voice",
                "health": "/health",
                "logs": "/logs",
                "create_patient": "POST /api/patients",
                "get_patient": "GET /api/patients/<id_number>",
                "get_all_patients": "GET /api/patients",
                "create_ai_interaction": "POST /api/patients/<id>/ai-interactions",
                "get_ai_interactions": "GET /api/patients/<id>/ai-interactions"
            }
        })
    except Exception as e:
        logger.error(f"❌ Health check error: {e}")
        return jsonify({
            "status": "unhealthy",
            "service": "HealthConnect SA Voice Assistant",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }), 500

@app.route('/', methods=['GET'])
def index():
    """
    Basic info endpoint
    """
    return jsonify({
        "service": "HealthConnect SA Voice Assistant",
        "description": "Voice-based health assistance in South African languages",
        "endpoints": {
            "/voice": "Twilio webhook for voice calls",
            "/health": "Health check",
            "/logs": "View conversation logs"
        }
    })

@app.route('/logs', methods=['GET'])
def view_logs():
    """
    View conversation logs (for testing/debugging)
    """
    try:
        log_file = "logs/conversation_log.json"
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                logs = json.load(f)
            return jsonify(logs)
        else:
            return jsonify([])
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# Patient API Endpoints for Frontend-Backend Integration
@app.route('/api/patients', methods=['POST'])
def create_patient():
    """
    Create a new patient record in database
    """
    try:
        data = request.get_json()

        # Log patient registration
        logger.info(f"📝 Registering new patient: {data.get('first_name')} {data.get('last_name')} (ID: {data.get('id_number')})")

        # Prepare patient data for database
        patient_data = {
            'id': data.get('patient_id'),
            'first_name': data.get('first_name'),
            'last_name': data.get('last_name'),
            'id_number': data.get('id_number'),
            'phone_number': data.get('phone_number'),
            'email': data.get('email', ''),
            'date_of_birth': data.get('date_of_birth'),
            'age': data.get('age'),
            'gender': data.get('gender'),
            'address': data.get('address', ''),
            'emergency_contact_name': data.get('emergency_contact_name', ''),
            'emergency_contact_phone': data.get('emergency_contact_phone', ''),
            'emergency_contact_relationship': data.get('emergency_contact_relationship', '')
        }

        # Check if patient already exists
        existing_patient = db.get_patient_by_id_number(patient_data['id_number'])
        if existing_patient:
            # Update existing patient
            updates = {k: v for k, v in patient_data.items() if k != 'id'}
            success = db.update_patient(existing_patient['id'], updates)

            if success:
                logger.info(f"✅ Updated existing patient: {existing_patient['id']}")
                return jsonify({
                    'success': True,
                    'patient_id': existing_patient['id'],
                    'message': 'Patient updated successfully in database',
                    'action': 'updated'
                }), 200
            else:
                raise Exception("Failed to update existing patient")
        else:
            # Create new patient
            success = db.create_patient(patient_data)

            if success:
                logger.info(f"✅ Patient registered in database: {patient_data['id']}")

                # Add medical history if provided
                medical_history = data.get('medical_history', [])
                for condition in medical_history:
                    db.add_medical_condition(patient_data['id'], condition)

                return jsonify({
                    'success': True,
                    'patient_id': patient_data['id'],
                    'message': 'Patient registered successfully in database',
                    'action': 'created'
                }), 201
            else:
                raise Exception("Failed to create patient in database")

    except Exception as e:
        logger.error(f"❌ Error registering patient: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/patients/<id_number>', methods=['GET'])
def get_patient(id_number):
    """
    Get patient by SA ID number from database
    """
    try:
        logger.info(f"🔍 Looking up patient by ID: {id_number}")

        # Get patient from database
        patient_data = db.get_patient_by_id_number(id_number)

        if patient_data:
            # Get additional data
            medical_history = db.get_medical_history(patient_data['id'])
            ai_interactions = db.get_ai_interactions_by_patient(patient_data['id'])

            # Format response
            response_data = {
                'patient_id': patient_data['id'],
                'first_name': patient_data['first_name'],
                'last_name': patient_data['last_name'],
                'id_number': patient_data['id_number'],
                'phone_number': patient_data['phone_number'],
                'email': patient_data['email'],
                'date_of_birth': patient_data['date_of_birth'],
                'age': patient_data['age'],
                'gender': patient_data['gender'],
                'address': patient_data['address'],
                'emergency_contact_name': patient_data['emergency_contact_name'],
                'emergency_contact_phone': patient_data['emergency_contact_phone'],
                'emergency_contact_relationship': patient_data['emergency_contact_relationship'],
                'medical_history': medical_history,
                'ai_interactions': ai_interactions,
                'created_at': patient_data['created_at'],
                'updated_at': patient_data['updated_at'],
                'found_in_backend': True
            }

            logger.info(f"✅ Patient found in database: {patient_data['id']}")
            return jsonify(response_data), 200
        else:
            logger.info(f"❌ Patient not found in database: {id_number}")
            return jsonify({
                'success': False,
                'error': 'Patient not found',
                'found_in_backend': False
            }), 404

    except Exception as e:
        logger.error(f"❌ Error getting patient: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/patients', methods=['GET'])
def get_all_patients():
    """
    Get all patients with optional search
    """
    try:
        search_term = request.args.get('search', '')
        provider_id = request.args.get('provider_id', 'PROV001')

        logger.info(f"🔍 Getting all patients (search: '{search_term}', provider: {provider_id})")

        # Get patients from database
        patients = db.search_patients(search_term, provider_id)

        # Format response
        response_data = []
        for patient in patients:
            # Get additional data for each patient
            medical_history = db.get_medical_history(patient['id'])
            ai_interactions = db.get_ai_interactions_by_patient(patient['id'])

            patient_data = {
                'patient_id': patient['id'],
                'first_name': patient['first_name'],
                'last_name': patient['last_name'],
                'id_number': patient['id_number'],
                'phone_number': patient['phone_number'],
                'email': patient['email'],
                'age': patient['age'],
                'gender': patient['gender'],
                'address': patient['address'],
                'medical_history_count': len(medical_history),
                'ai_interactions_count': len(ai_interactions),
                'created_at': patient['created_at'],
                'updated_at': patient['updated_at']
            }
            response_data.append(patient_data)

        logger.info(f"✅ Found {len(response_data)} patients")

        return jsonify({
            'success': True,
            'patients': response_data,
            'count': len(response_data),
            'search_term': search_term
        }), 200

    except Exception as e:
        logger.error(f"❌ Error getting patients: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/patients/<patient_id>/ai-interactions', methods=['POST'])
def create_ai_interaction():
    """
    Create AI interaction record
    """
    try:
        data = request.get_json()
        patient_id = request.view_args['patient_id']

        logger.info(f"📝 Creating AI interaction for patient: {patient_id}")

        # Prepare interaction data
        interaction_data = {
            'id': str(uuid.uuid4()),
            'patient_id': patient_id,
            'interaction_type': data.get('interaction_type', 'chat'),
            'summary': data.get('summary', ''),
            'full_conversation': data.get('full_conversation', ''),
            'ai_assessment': data.get('ai_assessment', ''),
            'severity': data.get('severity', 'Low'),
            'recommendations': data.get('recommendations', ''),
            'urgent_care': data.get('urgent_care', False)
        }

        # Save to database
        success = db.create_ai_interaction(interaction_data)

        if success:
            logger.info(f"✅ AI interaction created: {interaction_data['id']}")
            return jsonify({
                'success': True,
                'interaction_id': interaction_data['id'],
                'message': 'AI interaction recorded successfully'
            }), 201
        else:
            raise Exception("Failed to create AI interaction")

    except Exception as e:
        logger.error(f"❌ Error creating AI interaction: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/patients/<patient_id>/ai-interactions', methods=['GET'])
def get_ai_interactions(patient_id):
    """
    Get AI interactions for a patient
    """
    try:
        logger.info(f"🔍 Getting AI interactions for patient: {patient_id}")

        interactions = db.get_ai_interactions_by_patient(patient_id)

        logger.info(f"✅ Found {len(interactions)} AI interactions")

        return jsonify({
            'success': True,
            'interactions': interactions,
            'count': len(interactions)
        }), 200

    except Exception as e:
        logger.error(f"❌ Error getting AI interactions: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)
