{"timestamp": "2025-06-20T15:03:30.301737", "results": {"voice_calls": [{"phone_number": "+27123456789", "success": true, "call_id": "mock_call_20250620_150310", "response_time": 2.047191, "data": {"call_id": "mock_call_20250620_150310", "message": "Mock call initiated to +27123456789", "note": "This is a mock call for testing purposes", "phone_number": "+27123456789", "status": "initiated", "success": true, "timestamp": "2025-06-20T15:03:10.352150"}}, {"phone_number": "+27987654321", "success": true, "call_id": "mock_call_20250620_150312", "response_time": 2.11385, "data": {"call_id": "mock_call_20250620_150312", "message": "Mock call initiated to +27987654321", "note": "This is a mock call for testing purposes", "phone_number": "+27987654321", "status": "initiated", "success": true, "timestamp": "2025-06-20T15:03:12.969661"}}, {"phone_number": "+27111222333", "success": true, "call_id": "mock_call_20250620_150315", "response_time": 2.053192, "data": {"call_id": "mock_call_20250620_150315", "message": "Mock call initiated to +27111222333", "note": "This is a mock call for testing purposes", "phone_number": "+27111222333", "status": "initiated", "success": true, "timestamp": "2025-06-20T15:03:15.527902"}}, {"phone_number": "+27444555666", "success": true, "call_id": "mock_call_20250620_150318", "response_time": 2.028731, "data": {"call_id": "mock_call_20250620_150318", "message": "Mock call initiated to +27444555666", "note": "This is a mock call for testing purposes", "phone_number": "+27444555666", "status": "initiated", "success": true, "timestamp": "2025-06-20T15:03:18.060410"}}], "callback": {"success": false, "error": "HTTP 404", "response_time": 2.041923}, "recording": {"success": false, "error": "HTTP 404", "response_time": 2.043624}, "dtmf_responses": [{"digits": "1", "success": false, "error": "HTTP 404", "response_time": 2.048029}, {"digits": "2", "success": false, "error": "HTTP 404", "response_time": 2.034095}, {"digits": "9", "success": false, "error": "HTTP 404", "response_time": 2.030165}]}}