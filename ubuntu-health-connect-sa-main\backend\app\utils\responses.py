"""
Standardized API response utilities
"""

from flask import jsonify
from typing import Any, Dict, List, Union

def success_response(data: Any = None, status_code: int = 200) -> tuple:
    """
    Create standardized success response
    """
    response = {
        'success': True,
        'data': data,
        'timestamp': None  # Will be set by <PERSON>lask
    }
    
    return jsonify(response), status_code

def error_response(message: Union[str, List[str]], status_code: int = 400) -> tuple:
    """
    Create standardized error response
    """
    response = {
        'success': False,
        'error': message,
        'timestamp': None  # Will be set by Flask
    }
    
    return jsonify(response), status_code

def paginated_response(data: List[Any], page: int, per_page: int, total: int) -> Dict[str, Any]:
    """
    Create paginated response
    """
    return {
        'success': True,
        'data': data,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': (total + per_page - 1) // per_page
        }
    }
