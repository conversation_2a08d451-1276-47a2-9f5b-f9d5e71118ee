#!/usr/bin/env python3
"""
Integration Testing Script for Voice Triage System
Tests the complete workflow from voice triage to provider dashboard
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# Test configuration
VOICE_TRIAGE_URL = 'http://localhost:5000'
PATIENT_PORTAL_URL = 'http://localhost:8082'

def test_voice_triage_backend():
    """Test voice triage backend connectivity"""
    try:
        response = requests.get(f"{VOICE_TRIAGE_URL}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Voice Triage Backend: Online")
            print(f"   Status: {data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ Voice Triage Backend: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Voice Triage Backend: {str(e)}")
        return False

def test_patient_portal_frontend():
    """Test patient portal frontend connectivity"""
    try:
        response = requests.get(PATIENT_PORTAL_URL, timeout=5)
        if response.status_code == 200:
            print("✅ Patient Portal Frontend: Online")
            print(f"   Content length: {len(response.text)} characters")
            return True
        else:
            print(f"❌ Patient Portal Frontend: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Patient Portal Frontend: {str(e)}")
        return False

def simulate_urgent_case_workflow():
    """Simulate complete urgent case workflow"""
    print("\n🚨 Simulating Urgent Case Workflow")
    print("-" * 50)
    
    # Step 1: Patient calls voice triage
    print("📞 Step 1: Patient initiates voice triage call")
    
    call_result = requests.post(
        f"{VOICE_TRIAGE_URL}/api/voice-triage/mock-call",
        json={'phone_number': '+27727803582'},  # Your actual number
        timeout=10
    )
    
    if call_result.status_code == 200:
        call_data = call_result.json()
        print(f"   ✅ Call initiated: {call_data.get('call_id', 'unknown')}")
    else:
        print(f"   ❌ Call failed: HTTP {call_result.status_code}")
        return False
    
    # Step 2: AI assessment of critical symptoms
    print("\n🧠 Step 2: AI assessment of critical symptoms")
    
    critical_symptoms = ['chest pain', 'difficulty breathing', 'sweating']
    assessment_result = requests.post(
        f"{VOICE_TRIAGE_URL}/api/voice-triage/mock-assessment",
        json={
            'symptoms': critical_symptoms,
            'patient_id': 'urgent-patient-001'
        },
        timeout=10
    )
    
    if assessment_result.status_code == 200:
        assessment_data = assessment_result.json()
        assessment = assessment_data.get('assessment', {})
        risk_level = assessment.get('risk_level', 'unknown')
        urgency_score = assessment.get('urgency_score', 0)
        
        print(f"   ✅ Assessment completed")
        print(f"   Risk Level: {risk_level}")
        print(f"   Urgency Score: {urgency_score}/10")
        print(f"   Symptoms: {', '.join(critical_symptoms)}")
        
        if risk_level == 'critical':
            print("   🚨 CRITICAL CASE DETECTED - Should trigger provider alerts")
            return True
        else:
            print(f"   ⚠️ Expected critical, got {risk_level}")
            return False
    else:
        print(f"   ❌ Assessment failed: HTTP {assessment_result.status_code}")
        return False

def test_multiple_risk_scenarios():
    """Test multiple risk level scenarios"""
    print("\n📊 Testing Multiple Risk Level Scenarios")
    print("-" * 50)
    
    scenarios = [
        {
            'name': 'Low Risk - Cold',
            'symptoms': ['runny nose', 'mild headache'],
            'expected_risk': 'low'
        },
        {
            'name': 'Medium Risk - Flu',
            'symptoms': ['fever', 'body aches'],
            'expected_risk': 'medium'
        },
        {
            'name': 'High Risk - Severe Infection',
            'symptoms': ['high fever', 'severe headache'],
            'expected_risk': 'high'
        },
        {
            'name': 'Critical Risk - Heart Attack',
            'symptoms': ['chest pain', 'difficulty breathing'],
            'expected_risk': 'critical'
        }
    ]
    
    results = []
    
    for scenario in scenarios:
        print(f"\n   Testing: {scenario['name']}")
        
        response = requests.post(
            f"{VOICE_TRIAGE_URL}/api/voice-triage/mock-assessment",
            json={
                'symptoms': scenario['symptoms'],
                'patient_id': f"test-{scenario['name'].lower().replace(' ', '-')}"
            },
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            assessment = data.get('assessment', {})
            actual_risk = assessment.get('risk_level', 'unknown')
            
            success = actual_risk == scenario['expected_risk']
            print(f"   {'✅' if success else '❌'} Risk: {actual_risk} (expected: {scenario['expected_risk']})")
            
            results.append({
                'scenario': scenario['name'],
                'success': success,
                'actual_risk': actual_risk,
                'expected_risk': scenario['expected_risk']
            })
        else:
            print(f"   ❌ Failed: HTTP {response.status_code}")
            results.append({
                'scenario': scenario['name'],
                'success': False,
                'error': f"HTTP {response.status_code}"
            })
        
        time.sleep(0.5)
    
    success_rate = sum(1 for r in results if r.get('success', False)) / len(results) * 100
    print(f"\n   📊 Risk Assessment Accuracy: {success_rate:.1f}%")
    
    return results

def test_system_performance():
    """Test system performance under load"""
    print("\n⚡ Testing System Performance")
    print("-" * 50)
    
    # Test concurrent requests
    print("   Testing concurrent assessment requests...")
    
    start_time = time.time()
    
    # Simulate 5 concurrent assessments
    import threading
    results = []
    
    def make_assessment_request(thread_id):
        try:
            response = requests.post(
                f"{VOICE_TRIAGE_URL}/api/voice-triage/mock-assessment",
                json={
                    'symptoms': ['headache', 'fever'],
                    'patient_id': f'perf-test-{thread_id}'
                },
                timeout=15
            )
            results.append({
                'thread_id': thread_id,
                'success': response.status_code == 200,
                'response_time': response.elapsed.total_seconds()
            })
        except Exception as e:
            results.append({
                'thread_id': thread_id,
                'success': False,
                'error': str(e)
            })
    
    threads = []
    for i in range(5):
        thread = threading.Thread(target=make_assessment_request, args=(i,))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    successful_requests = sum(1 for r in results if r.get('success', False))
    avg_response_time = sum(r.get('response_time', 0) for r in results if 'response_time' in r) / len(results)
    
    print(f"   ✅ Concurrent Requests: {successful_requests}/5 successful")
    print(f"   ⏱️ Total Time: {total_time:.2f} seconds")
    print(f"   📊 Average Response Time: {avg_response_time:.2f} seconds")
    
    return {
        'concurrent_success_rate': successful_requests / 5 * 100,
        'total_time': total_time,
        'avg_response_time': avg_response_time
    }

def test_error_handling():
    """Test error handling scenarios"""
    print("\n🛡️ Testing Error Handling")
    print("-" * 50)
    
    error_scenarios = [
        {
            'name': 'Missing phone number',
            'endpoint': '/api/voice-triage/mock-call',
            'data': {},
            'expected_status': 400
        },
        {
            'name': 'Invalid symptoms format',
            'endpoint': '/api/voice-triage/mock-assessment',
            'data': {'symptoms': 'not-a-list', 'patient_id': 'test'},
            'expected_status': 200  # Mock service might handle this gracefully
        },
        {
            'name': 'Empty symptoms',
            'endpoint': '/api/voice-triage/mock-assessment',
            'data': {'symptoms': [], 'patient_id': 'test'},
            'expected_status': 200
        }
    ]
    
    results = []
    
    for scenario in error_scenarios:
        print(f"   Testing: {scenario['name']}")
        
        try:
            response = requests.post(
                f"{VOICE_TRIAGE_URL}{scenario['endpoint']}",
                json=scenario['data'],
                timeout=10
            )
            
            status_ok = response.status_code in [scenario['expected_status'], 200]
            print(f"   {'✅' if status_ok else '❌'} Status: {response.status_code}")
            
            results.append({
                'scenario': scenario['name'],
                'success': status_ok,
                'status_code': response.status_code
            })
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            results.append({
                'scenario': scenario['name'],
                'success': False,
                'error': str(e)
            })
    
    return results

def run_integration_tests():
    """Run complete integration test suite"""
    print("🔗 Voice Triage Integration Testing Suite")
    print("🌍 Ubuntu Philosophy: 'I am because we are'")
    print("=" * 70)
    print(f"📅 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # Test 1: System Connectivity
    print("\n🔌 Testing System Connectivity")
    print("-" * 50)
    
    backend_online = test_voice_triage_backend()
    frontend_online = test_patient_portal_frontend()
    
    if not backend_online:
        print("\n❌ Cannot proceed without voice triage backend")
        print("💡 Start the backend with: python test_simple.py --server-only")
        return
    
    # Test 2: Urgent Case Workflow
    urgent_workflow_success = simulate_urgent_case_workflow()
    
    # Test 3: Multiple Risk Scenarios
    risk_scenario_results = test_multiple_risk_scenarios()
    
    # Test 4: System Performance
    performance_results = test_system_performance()
    
    # Test 5: Error Handling
    error_handling_results = test_error_handling()
    
    # Calculate overall results
    risk_success_rate = sum(1 for r in risk_scenario_results if r.get('success', False)) / len(risk_scenario_results) * 100
    error_handling_rate = sum(1 for r in error_handling_results if r.get('success', False)) / len(error_handling_results) * 100
    
    # Print final summary
    print("\n" + "=" * 70)
    print("📊 INTEGRATION TEST RESULTS")
    print("=" * 70)
    
    print(f"🔌 System Connectivity:")
    print(f"   Voice Triage Backend: {'✅ Online' if backend_online else '❌ Offline'}")
    print(f"   Patient Portal Frontend: {'✅ Online' if frontend_online else '❌ Offline'}")
    
    print(f"\n🚨 Urgent Case Workflow:")
    print(f"   Status: {'✅ Success' if urgent_workflow_success else '❌ Failed'}")
    
    print(f"\n📊 Risk Assessment Accuracy:")
    print(f"   Success Rate: {risk_success_rate:.1f}%")
    
    print(f"\n⚡ System Performance:")
    print(f"   Concurrent Requests: {performance_results['concurrent_success_rate']:.1f}% success")
    print(f"   Average Response Time: {performance_results['avg_response_time']:.2f}s")
    
    print(f"\n🛡️ Error Handling:")
    print(f"   Robustness: {error_handling_rate:.1f}%")
    
    # Overall assessment
    overall_score = (
        (100 if backend_online else 0) * 0.3 +
        (100 if urgent_workflow_success else 0) * 0.3 +
        risk_success_rate * 0.2 +
        performance_results['concurrent_success_rate'] * 0.1 +
        error_handling_rate * 0.1
    )
    
    print(f"\n🎯 Overall Integration Score: {overall_score:.1f}%")
    
    print("\n" + "=" * 70)
    
    if overall_score >= 80:
        print("🎉 Integration tests PASSED! System is ready for production.")
    elif overall_score >= 60:
        print("✅ Integration tests mostly successful. Minor issues to address.")
    else:
        print("⚠️ Integration tests show significant issues. Review required.")
    
    print("=" * 70)
    
    return {
        'backend_online': backend_online,
        'frontend_online': frontend_online,
        'urgent_workflow_success': urgent_workflow_success,
        'risk_success_rate': risk_success_rate,
        'performance_results': performance_results,
        'error_handling_rate': error_handling_rate,
        'overall_score': overall_score
    }

if __name__ == '__main__':
    try:
        results = run_integration_tests()
        
        # Save results to file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"integration_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'results': results
            }, f, indent=2)
        
        print(f"📄 Detailed results saved to: {filename}")
        
    except KeyboardInterrupt:
        print("\n🛑 Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Testing failed: {e}")
