import React, { useState } from 'react';

interface PatientRegistrationProps {
  onBack: () => void;
  onRegistrationComplete: (patientData: any) => void;
}

interface PatientFormData {
  firstName: string;
  lastName: string;
  idNumber: string;
  phoneNumber: string;
  email: string;
  dateOfBirth: string;
  gender: 'Male' | 'Female' | '';
  address: string;
  city: string;
  province: string;
  postalCode: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelationship: string;
  medicalAidProvider: string;
  medicalAidNumber: string;
}

const PatientRegistration: React.FC<PatientRegistrationProps> = ({ onBack, onRegistrationComplete }) => {
  const [formData, setFormData] = useState<PatientFormData>({
    firstName: '',
    lastName: '',
    idNumber: '',
    phoneNumber: '',
    email: '',
    dateOfBirth: '',
    gender: '',
    address: '',
    city: '',
    province: 'Western Cape',
    postalCode: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    medicalAidProvider: '',
    medicalAidNumber: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const provinces = [
    'Western Cape', 'Eastern Cape', 'Northern Cape', 'Free State',
    'KwaZulu-Natal', 'North West', 'Gauteng', 'Mpumalanga', 'Limpopo'
  ];

  const relationships = [
    'Spouse', 'Parent', 'Child', 'Sibling', 'Friend', 'Other Family Member', 'Guardian'
  ];

  // Validate SA ID Number
  const validateSAID = (idNumber: string): boolean => {
    if (idNumber.length !== 13) return false;
    if (!/^\d{13}$/.test(idNumber)) return false;

    // Basic SA ID validation
    const birthYear = parseInt(idNumber.substring(0, 2));
    const birthMonth = parseInt(idNumber.substring(2, 4));
    const birthDay = parseInt(idNumber.substring(4, 6));

    if (birthMonth < 1 || birthMonth > 12) return false;
    if (birthDay < 1 || birthDay > 31) return false;

    return true;
  };

  // Extract info from SA ID
  const extractInfoFromSAID = (idNumber: string) => {
    if (!validateSAID(idNumber)) return null;

    const birthYear = parseInt(idNumber.substring(0, 2));
    const birthMonth = parseInt(idNumber.substring(2, 4));
    const birthDay = parseInt(idNumber.substring(4, 6));
    const genderDigit = parseInt(idNumber.substring(6, 10));

    const currentYear = new Date().getFullYear() % 100;
    const fullBirthYear = birthYear <= currentYear ? 2000 + birthYear : 1900 + birthYear;
    const gender = genderDigit >= 5000 ? 'Male' : 'Female';
    const dateOfBirth = `${fullBirthYear}-${birthMonth.toString().padStart(2, '0')}-${birthDay.toString().padStart(2, '0')}`;

    return { dateOfBirth, gender };
  };

  const handleInputChange = (field: keyof PatientFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Auto-fill date of birth and gender from SA ID
    if (field === 'idNumber' && value.length === 13) {
      const extractedInfo = extractInfoFromSAID(value);
      if (extractedInfo) {
        setFormData(prev => ({
          ...prev,
          dateOfBirth: extractedInfo.dateOfBirth,
          gender: extractedInfo.gender as 'Male' | 'Female'
        }));
      }
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.idNumber.trim()) newErrors.idNumber = 'SA ID number is required';
    if (!formData.phoneNumber.trim()) newErrors.phoneNumber = 'Phone number is required';
    if (!formData.gender) newErrors.gender = 'Gender is required';
    if (!formData.address.trim()) newErrors.address = 'Address is required';
    if (!formData.city.trim()) newErrors.city = 'City is required';
    if (!formData.emergencyContactName.trim()) newErrors.emergencyContactName = 'Emergency contact name is required';
    if (!formData.emergencyContactPhone.trim()) newErrors.emergencyContactPhone = 'Emergency contact phone is required';
    if (!formData.emergencyContactRelationship) newErrors.emergencyContactRelationship = 'Emergency contact relationship is required';

    // Validate SA ID
    if (formData.idNumber && !validateSAID(formData.idNumber)) {
      newErrors.idNumber = 'Invalid SA ID number format';
    }

    // Validate phone number
    if (formData.phoneNumber && !/^(\+27|0)[0-9]{9}$/.test(formData.phoneNumber.replace(/\s/g, ''))) {
      newErrors.phoneNumber = 'Invalid phone number format (use +27 or 0 format)';
    }

    // Validate emergency contact phone
    if (formData.emergencyContactPhone && !/^(\+27|0)[0-9]{9}$/.test(formData.emergencyContactPhone.replace(/\s/g, ''))) {
      newErrors.emergencyContactPhone = 'Invalid emergency contact phone format';
    }

    // Validate email if provided
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('🔄 Starting registration process...');

      // For now, use mock backend response since backend is having issues
      console.log('🔍 Using mock backend response for testing...');

      // Calculate age from date of birth
      const age = new Date().getFullYear() - new Date(formData.dateOfBirth).getFullYear();

      // Generate unique patient ID
      const patientId = `PAT_${Date.now()}_${Math.random().toString(36).substring(2, 7).toUpperCase()}`;

      // Prepare patient data for backend
      const patientData = {
        patient_id: patientId,
        first_name: formData.firstName.trim(),
        last_name: formData.lastName.trim(),
        id_number: formData.idNumber.trim(),
        phone_number: formData.phoneNumber.trim(),
        email: formData.email.trim(),
        date_of_birth: formData.dateOfBirth,
        age: age,
        gender: formData.gender,
        address: `${formData.address.trim()}, ${formData.city.trim()}, ${formData.province}${formData.postalCode ? ', ' + formData.postalCode : ''}`,
        emergency_contact_name: formData.emergencyContactName.trim(),
        emergency_contact_phone: formData.emergencyContactPhone.trim(),
        emergency_contact_relationship: formData.emergencyContactRelationship,
        medical_aid_provider: formData.medicalAidProvider.trim(),
        medical_aid_number: formData.medicalAidNumber.trim()
      };

      console.log('🔄 Registering patient in database...');
      console.log('📤 Patient data:', patientData);

      // Send to patient authentication API using environment variable
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';
      const response = await fetch(`${apiBaseUrl}/api/patients/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(patientData),
      });

      console.log('🔍 Response status:', response.status);
      console.log('🔍 Response ok:', response.ok);

      // Get response text first to see what we're actually receiving
      const responseText = await response.text();
      console.log('🔍 Raw response text:', responseText);

      if (response.ok) {
        console.log('✅ Response is ok, parsing JSON...');

        let result;
        try {
          result = JSON.parse(responseText);
          console.log('✅ Parsed result:', result);
        } catch (parseError) {
          console.error('❌ Failed to parse JSON:', parseError);
          console.error('❌ Response text was:', responseText);
          throw new Error('Invalid JSON response from server');
        }

        console.log(`✅ Patient registered in backend: ${result.data?.action} - ${result.data?.patient_id}`);

        // Check if result has expected structure
        if (!result.data || !result.data.patient_id) {
          console.error('❌ Unexpected response structure:', result);
          throw new Error('Invalid response from server - missing patient data');
        }

        // Also register in frontend services for login compatibility
        console.log('🔄 Registering patient in frontend service for login compatibility...');
        try {
          const { patientRegistrationService } = await import('@/services/patientRegistrationService');
          const frontendPatient = await patientRegistrationService.registerNewPatient({
            personalInfo: {
              firstName: formData.firstName.trim(),
              lastName: formData.lastName.trim(),
              dateOfBirth: formData.dateOfBirth,
              age: age,
              gender: formData.gender as 'Male' | 'Female',
              idNumber: formData.idNumber.trim(),
              phone: formData.phoneNumber.trim(),
              email: formData.email.trim(),
              address: `${formData.address.trim()}, ${formData.city.trim()}, ${formData.province}${formData.postalCode ? ', ' + formData.postalCode : ''}`,
              emergencyContact: {
                name: formData.emergencyContactName.trim(),
                relationship: formData.emergencyContactRelationship,
                phone: formData.emergencyContactPhone.trim()
              }
            },
            medicalHistory: [],
            medications: [],
            allergies: [],
            vaccinations: [],
            labResults: [],
            appointments: [],
            insurance: {
              provider: formData.medicalAidProvider.trim(),
              policyNumber: formData.medicalAidNumber.trim(),
              groupNumber: '',
              validUntil: ''
            }
          });

          console.log(`✅ Patient also registered in frontend service: ${frontendPatient.id}`);

          // Store patient session data (using frontend service data)
          localStorage.setItem('currentPatientId', frontendPatient.id);
          localStorage.setItem('currentPatientIdNumber', formData.idNumber.trim());
          localStorage.setItem('currentPatientPhone', formData.phoneNumber.trim());
          localStorage.setItem('currentPatientName', `${formData.firstName.trim()} ${formData.lastName.trim()}`);
          localStorage.setItem('currentPatientAge', age.toString());
          localStorage.setItem('currentPatientLocation', `${formData.city.trim()}, ${formData.province}`);

          alert(`🎉 Registration Successful!\n\nWelcome ${formData.firstName} ${formData.lastName}!\n\nYour patient ID: ${frontendPatient.id}\nYou can now access your patient dashboard.`);

          onRegistrationComplete(frontendPatient);

        } catch (frontendError) {
          console.error('❌ Error registering in frontend service:', frontendError);

          // Fallback: create simplified patient object
          const simplifiedPatient = {
            id: result.data.patient_id,
            personalInfo: {
              firstName: formData.firstName.trim(),
              lastName: formData.lastName.trim(),
              dateOfBirth: formData.dateOfBirth,
              age: age,
              gender: formData.gender as 'Male' | 'Female',
              idNumber: formData.idNumber.trim(),
              phone: formData.phoneNumber.trim(),
              email: formData.email.trim(),
              address: `${formData.address.trim()}, ${formData.city.trim()}, ${formData.province}${formData.postalCode ? ', ' + formData.postalCode : ''}`,
              emergencyContact: {
                name: formData.emergencyContactName.trim(),
                relationship: formData.emergencyContactRelationship,
                phone: formData.emergencyContactPhone.trim()
              }
            },
            medicalHistory: [],
            medications: [],
            allergies: [],
            vaccinations: [],
            labResults: [],
            appointments: [],
            insurance: {
              provider: formData.medicalAidProvider.trim(),
              policyNumber: formData.medicalAidNumber.trim(),
              groupNumber: '',
              validUntil: ''
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            healthcareProviders: [],
            aiInteractions: []
          };

          // Store patient session data (fallback)
          localStorage.setItem('currentPatientId', result.data.patient_id);
          localStorage.setItem('currentPatientIdNumber', formData.idNumber.trim());
          localStorage.setItem('currentPatientPhone', formData.phoneNumber.trim());
          localStorage.setItem('currentPatientName', `${formData.firstName.trim()} ${formData.lastName.trim()}`);
          localStorage.setItem('currentPatientAge', age.toString());
          localStorage.setItem('currentPatientLocation', `${formData.city.trim()}, ${formData.province}`);

          alert(`🎉 Registration Successful!\n\nWelcome ${formData.firstName} ${formData.lastName}!\n\nYour patient ID: ${result.data.patient_id}\nYou can now access your patient dashboard.\n\n⚠️ Note: Frontend service registration failed, but you can still log in.`);

          onRegistrationComplete(simplifiedPatient as any);
        }

      } else {
        console.log('❌ Response not ok, status:', response.status);
        console.log('❌ Error response text:', responseText);

        let errorData;
        try {
          errorData = JSON.parse(responseText);
          console.log('❌ Parsed error data:', errorData);
        } catch (parseError) {
          console.log('❌ Failed to parse error JSON:', parseError);
          errorData = { error: 'Invalid response format' };
        }

        throw new Error(errorData.error || `Registration failed: ${response.status}`);
      }

    } catch (error) {
      console.error('❌ Registration error:', error);
      alert(`❌ Registration Failed!\n\n${error instanceof Error ? error.message : 'Unknown error occurred'}\n\nPlease try again or contact support.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 50%, #CBD5E1 100%)',
      padding: '1rem',
      fontFamily: 'Ubuntu, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* Header */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.95)',
        borderRadius: '12px',
        padding: '1rem',
        marginBottom: '1rem',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(34, 139, 34, 0.2)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '0.5rem' }}>
          <button
            onClick={onBack}
            style={{
              background: 'linear-gradient(135deg, #6B7280, #4B5563)',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '0.5rem 1rem',
              cursor: 'pointer',
              fontSize: '0.9rem',
              fontWeight: '600',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              transition: 'all 0.3s ease'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.transform = 'translateY(-1px)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(107, 114, 128, 0.3)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
            }}
          >
            ← Back
          </button>

          <div>
            <h1 style={{
              fontSize: '1.8rem',
              fontWeight: 'bold',
              color: '#228B22',
              margin: '0',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              🏥 Patient Registration
            </h1>
            <p style={{
              color: '#6B7280',
              margin: '0',
              fontSize: '0.9rem'
            }}>
              Join Ubuntu Health Connect SA - Your health, our Ubuntu
            </p>
          </div>
        </div>
      </div>

      {/* Registration Form */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.95)',
        borderRadius: '12px',
        padding: '2rem',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(34, 139, 34, 0.2)',
        maxWidth: '800px',
        margin: '0 auto'
      }}>
        <form onSubmit={handleSubmit}>
          {/* Personal Information Section */}
          <div style={{ marginBottom: '2rem' }}>
            <h2 style={{
              fontSize: '1.3rem',
              fontWeight: 'bold',
              color: '#228B22',
              marginBottom: '1rem',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              👤 Personal Information
            </h2>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
              {/* First Name */}
              <div>
                <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                  First Name *
                </label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: errors.firstName ? '2px solid #EF4444' : '2px solid #E5E7EB',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    transition: 'border-color 0.3s ease',
                    outline: 'none'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#228B22'}
                  onBlur={(e) => e.target.style.borderColor = errors.firstName ? '#EF4444' : '#E5E7EB'}
                  placeholder="Enter your first name"
                />
                {errors.firstName && (
                  <p style={{ color: '#EF4444', fontSize: '0.8rem', margin: '0.25rem 0 0 0' }}>
                    {errors.firstName}
                  </p>
                )}
              </div>

              {/* Last Name */}
              <div>
                <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                  Last Name *
                </label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: errors.lastName ? '2px solid #EF4444' : '2px solid #E5E7EB',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    transition: 'border-color 0.3s ease',
                    outline: 'none'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#228B22'}
                  onBlur={(e) => e.target.style.borderColor = errors.lastName ? '#EF4444' : '#E5E7EB'}
                  placeholder="Enter your last name"
                />
                {errors.lastName && (
                  <p style={{ color: '#EF4444', fontSize: '0.8rem', margin: '0.25rem 0 0 0' }}>
                    {errors.lastName}
                  </p>
                )}
              </div>
            </div>

            {/* SA ID Number */}
            <div style={{ marginTop: '1rem' }}>
              <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                South African ID Number *
              </label>
              <input
                type="text"
                value={formData.idNumber}
                onChange={(e) => handleInputChange('idNumber', e.target.value)}
                maxLength={13}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: errors.idNumber ? '2px solid #EF4444' : '2px solid #E5E7EB',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  transition: 'border-color 0.3s ease',
                  outline: 'none'
                }}
                onFocus={(e) => e.target.style.borderColor = '#228B22'}
                onBlur={(e) => e.target.style.borderColor = errors.idNumber ? '#EF4444' : '#E5E7EB'}
                placeholder="Enter your 13-digit SA ID number"
              />
              {errors.idNumber && (
                <p style={{ color: '#EF4444', fontSize: '0.8rem', margin: '0.25rem 0 0 0' }}>
                  {errors.idNumber}
                </p>
              )}
              <p style={{ color: '#6B7280', fontSize: '0.75rem', margin: '0.25rem 0 0 0' }}>
                Your date of birth and gender will be automatically filled from your ID number
              </p>
            </div>

            {/* Auto-filled fields */}
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem', marginTop: '1rem' }}>
              <div>
                <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                  Date of Birth
                </label>
                <input
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #E5E7EB',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: '#F9FAFB'
                  }}
                  readOnly
                />
              </div>

              <div>
                <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                  Gender *
                </label>
                <select
                  value={formData.gender}
                  onChange={(e) => handleInputChange('gender', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: errors.gender ? '2px solid #EF4444' : '2px solid #E5E7EB',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: '#F9FAFB'
                  }}
                  disabled
                >
                  <option value="">Select Gender</option>
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                </select>
                {errors.gender && (
                  <p style={{ color: '#EF4444', fontSize: '0.8rem', margin: '0.25rem 0 0 0' }}>
                    {errors.gender}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div style={{ marginBottom: '2rem' }}>
            <h2 style={{
              fontSize: '1.3rem',
              fontWeight: 'bold',
              color: '#228B22',
              marginBottom: '1rem',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              📞 Contact Information
            </h2>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
              <div>
                <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                  Phone Number *
                </label>
                <input
                  type="tel"
                  value={formData.phoneNumber}
                  onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: errors.phoneNumber ? '2px solid #EF4444' : '2px solid #E5E7EB',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    transition: 'border-color 0.3s ease',
                    outline: 'none'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#228B22'}
                  onBlur={(e) => e.target.style.borderColor = errors.phoneNumber ? '#EF4444' : '#E5E7EB'}
                  placeholder="+27821234567 or 0821234567"
                />
                {errors.phoneNumber && (
                  <p style={{ color: '#EF4444', fontSize: '0.8rem', margin: '0.25rem 0 0 0' }}>
                    {errors.phoneNumber}
                  </p>
                )}
              </div>

              <div>
                <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                  Email Address (Optional)
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: errors.email ? '2px solid #EF4444' : '2px solid #E5E7EB',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    transition: 'border-color 0.3s ease',
                    outline: 'none'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#228B22'}
                  onBlur={(e) => e.target.style.borderColor = errors.email ? '#EF4444' : '#E5E7EB'}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p style={{ color: '#EF4444', fontSize: '0.8rem', margin: '0.25rem 0 0 0' }}>
                    {errors.email}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Address Information */}
          <div style={{ marginBottom: '2rem' }}>
            <h2 style={{
              fontSize: '1.3rem',
              fontWeight: 'bold',
              color: '#228B22',
              marginBottom: '1rem',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              🏠 Address Information
            </h2>

            <div style={{ display: 'grid', gap: '1rem' }}>
              <div>
                <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                  Street Address *
                </label>
                <input
                  type="text"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: errors.address ? '2px solid #EF4444' : '2px solid #E5E7EB',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    transition: 'border-color 0.3s ease',
                    outline: 'none'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#228B22'}
                  onBlur={(e) => e.target.style.borderColor = errors.address ? '#EF4444' : '#E5E7EB'}
                  placeholder="123 Main Street, Suburb"
                />
                {errors.address && (
                  <p style={{ color: '#EF4444', fontSize: '0.8rem', margin: '0.25rem 0 0 0' }}>
                    {errors.address}
                  </p>
                )}
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '2fr 2fr 1fr', gap: '1rem' }}>
                <div>
                  <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                    City *
                  </label>
                  <input
                    type="text"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      border: errors.city ? '2px solid #EF4444' : '2px solid #E5E7EB',
                      borderRadius: '8px',
                      fontSize: '1rem',
                      transition: 'border-color 0.3s ease',
                      outline: 'none'
                    }}
                    onFocus={(e) => e.target.style.borderColor = '#228B22'}
                    onBlur={(e) => e.target.style.borderColor = errors.city ? '#EF4444' : '#E5E7EB'}
                    placeholder="Cape Town"
                  />
                  {errors.city && (
                    <p style={{ color: '#EF4444', fontSize: '0.8rem', margin: '0.25rem 0 0 0' }}>
                      {errors.city}
                    </p>
                  )}
                </div>

                <div>
                  <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                    Province *
                  </label>
                  <select
                    value={formData.province}
                    onChange={(e) => handleInputChange('province', e.target.value)}
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      border: '2px solid #E5E7EB',
                      borderRadius: '8px',
                      fontSize: '1rem',
                      outline: 'none'
                    }}
                  >
                    {provinces.map(province => (
                      <option key={province} value={province}>{province}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                    Postal Code
                  </label>
                  <input
                    type="text"
                    value={formData.postalCode}
                    onChange={(e) => handleInputChange('postalCode', e.target.value)}
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      border: '2px solid #E5E7EB',
                      borderRadius: '8px',
                      fontSize: '1rem',
                      outline: 'none'
                    }}
                    placeholder="8001"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Emergency Contact */}
          <div style={{ marginBottom: '2rem' }}>
            <h2 style={{
              fontSize: '1.3rem',
              fontWeight: 'bold',
              color: '#228B22',
              marginBottom: '1rem',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              🚨 Emergency Contact
            </h2>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
              <div>
                <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                  Emergency Contact Name *
                </label>
                <input
                  type="text"
                  value={formData.emergencyContactName}
                  onChange={(e) => handleInputChange('emergencyContactName', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: errors.emergencyContactName ? '2px solid #EF4444' : '2px solid #E5E7EB',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    transition: 'border-color 0.3s ease',
                    outline: 'none'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#228B22'}
                  onBlur={(e) => e.target.style.borderColor = errors.emergencyContactName ? '#EF4444' : '#E5E7EB'}
                  placeholder="Full name of emergency contact"
                />
                {errors.emergencyContactName && (
                  <p style={{ color: '#EF4444', fontSize: '0.8rem', margin: '0.25rem 0 0 0' }}>
                    {errors.emergencyContactName}
                  </p>
                )}
              </div>

              <div>
                <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                  Emergency Contact Phone *
                </label>
                <input
                  type="tel"
                  value={formData.emergencyContactPhone}
                  onChange={(e) => handleInputChange('emergencyContactPhone', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: errors.emergencyContactPhone ? '2px solid #EF4444' : '2px solid #E5E7EB',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    transition: 'border-color 0.3s ease',
                    outline: 'none'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#228B22'}
                  onBlur={(e) => e.target.style.borderColor = errors.emergencyContactPhone ? '#EF4444' : '#E5E7EB'}
                  placeholder="+27821234567"
                />
                {errors.emergencyContactPhone && (
                  <p style={{ color: '#EF4444', fontSize: '0.8rem', margin: '0.25rem 0 0 0' }}>
                    {errors.emergencyContactPhone}
                  </p>
                )}
              </div>
            </div>

            <div style={{ marginTop: '1rem' }}>
              <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                Relationship *
              </label>
              <select
                value={formData.emergencyContactRelationship}
                onChange={(e) => handleInputChange('emergencyContactRelationship', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: errors.emergencyContactRelationship ? '2px solid #EF4444' : '2px solid #E5E7EB',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  outline: 'none'
                }}
              >
                <option value="">Select Relationship</option>
                {relationships.map(relationship => (
                  <option key={relationship} value={relationship}>{relationship}</option>
                ))}
              </select>
              {errors.emergencyContactRelationship && (
                <p style={{ color: '#EF4444', fontSize: '0.8rem', margin: '0.25rem 0 0 0' }}>
                  {errors.emergencyContactRelationship}
                </p>
              )}
            </div>
          </div>

          {/* Medical Aid Information */}
          <div style={{ marginBottom: '2rem' }}>
            <h2 style={{
              fontSize: '1.3rem',
              fontWeight: 'bold',
              color: '#228B22',
              marginBottom: '1rem',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              🏥 Medical Aid Information (Optional)
            </h2>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
              <div>
                <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                  Medical Aid Provider
                </label>
                <input
                  type="text"
                  value={formData.medicalAidProvider}
                  onChange={(e) => handleInputChange('medicalAidProvider', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #E5E7EB',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none'
                  }}
                  placeholder="e.g., Discovery, Momentum, Bonitas"
                />
              </div>

              <div>
                <label style={{ display: 'block', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                  Medical Aid Number
                </label>
                <input
                  type="text"
                  value={formData.medicalAidNumber}
                  onChange={(e) => handleInputChange('medicalAidNumber', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #E5E7EB',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none'
                  }}
                  placeholder="Your medical aid number"
                />
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div style={{ textAlign: 'center', paddingTop: '1rem', borderTop: '1px solid #E5E7EB' }}>
            <button
              type="submit"
              disabled={isSubmitting}
              style={{
                background: isSubmitting
                  ? 'linear-gradient(135deg, #9CA3AF, #6B7280)'
                  : 'linear-gradient(135deg, #228B22, #32CD32)',
                color: 'white',
                border: 'none',
                borderRadius: '12px',
                padding: '1rem 3rem',
                fontSize: '1.1rem',
                fontWeight: '700',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                boxShadow: isSubmitting
                  ? 'none'
                  : '0 4px 20px rgba(34, 139, 34, 0.3)',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.5rem',
                margin: '0 auto',
                minWidth: '200px'
              }}
              onMouseOver={(e) => {
                if (!isSubmitting) {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 6px 25px rgba(34, 139, 34, 0.4)';
                }
              }}
              onMouseOut={(e) => {
                if (!isSubmitting) {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 20px rgba(34, 139, 34, 0.3)';
                }
              }}
            >
              {isSubmitting ? (
                <>
                  <div style={{
                    width: '20px',
                    height: '20px',
                    border: '2px solid #ffffff',
                    borderTop: '2px solid transparent',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }} />
                  Registering...
                </>
              ) : (
                <>
                  🎉 Complete Registration
                </>
              )}
            </button>

            <p style={{
              color: '#6B7280',
              fontSize: '0.85rem',
              marginTop: '1rem',
              lineHeight: '1.5'
            }}>
              By registering, you agree to Ubuntu Health Connect SA's terms of service and privacy policy.
              <br />
              Your information will be securely stored and used only for healthcare purposes.
            </p>
          </div>
        </form>
      </div>

      {/* CSS Animation */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default PatientRegistration;