#!/usr/bin/env python3
"""
Test server to verify backend setup
"""

from flask import Flask, jsonify
from flask_cors import CORS
from database.db_manager import DatabaseManager

app = Flask(__name__)
CORS(app)

# Test database
try:
    db = DatabaseManager()
    db_status = "connected" if db.test_connection() else "failed"
    db_stats = db.get_database_stats() if db.test_connection() else {}
except Exception as e:
    db_status = f"error: {e}"
    db_stats = {}

@app.route('/health')
def health():
    return jsonify({
        "status": "healthy",
        "service": "Ubuntu Health Test Server",
        "database": db_status,
        "stats": db_stats
    })

@app.route('/')
def index():
    return jsonify({
        "message": "Ubuntu Health Backend Test Server is running!",
        "database_status": db_status,
        "endpoints": ["/health", "/test"]
    })

@app.route('/test')
def test():
    return jsonify({
        "test": "success",
        "message": "Backend is working correctly!"
    })

if __name__ == '__main__':
    print("🚀 Starting Ubuntu Health Test Server...")
    print("📊 Database status:", db_status)
    print("🌐 Server will be available at: http://localhost:5000")
    print("✅ Starting Flask server...")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
