#!/usr/bin/env python3
"""
Ubuntu Health Connect SA - Working Backend with Database
Simplified version that ensures database connectivity
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import os
import sys
import sqlite3
from datetime import datetime
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables
load_dotenv()

# Create Flask app
app = Flask(__name__)

# Configure CORS - Allow all origins for development
CORS(app,
     origins=['*'],
     allow_headers=['Content-Type', 'Authorization', 'Accept'],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
     supports_credentials=True)

# Database configuration
PROJECT_ROOT = Path(__file__).parent.absolute()
DB_PATH = PROJECT_ROOT / 'database' / 'ubuntu_health.db'

print(f"🗄️ Using database: {DB_PATH}")

def get_db_connection():
    """Get database connection with improved error handling"""
    try:
        conn = sqlite3.connect(str(DB_PATH), timeout=30.0)  # 30 second timeout
        conn.row_factory = sqlite3.Row  # Enable dict-like access
        conn.execute("PRAGMA journal_mode=WAL")  # Enable WAL mode for better concurrency
        conn.execute("PRAGMA synchronous=NORMAL")  # Improve performance
        conn.execute("PRAGMA cache_size=10000")  # Increase cache size
        conn.execute("PRAGMA temp_store=memory")  # Use memory for temp storage
        return conn
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return None

def test_database():
    """Test database connectivity"""
    try:
        conn = get_db_connection()
        if conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM patients")
            count = cursor.fetchone()[0]
            conn.close()
            print(f"✅ Database connected. Patients count: {count}")
            return True
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

@app.route('/health', methods=['GET'])
def health_check():
    """Enhanced health check with database connectivity"""
    db_connected = test_database()
    
    try:
        if db_connected:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Get basic stats
            stats = {}
            tables = ['patients', 'ai_interactions']
            
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    stats[table] = cursor.fetchone()[0]
                except:
                    stats[table] = 0
            
            conn.close()
        else:
            stats = {}
        
        return jsonify({
            "status": "healthy" if db_connected else "degraded",
            "service": "Ubuntu Health Connect SA API - Working",
            "timestamp": datetime.now().isoformat(),
            "database": {
                "connected": db_connected,
                "path": str(DB_PATH),
                "stats": stats
            }
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "service": "Ubuntu Health Connect SA API - Working",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "database": {
                "connected": False,
                "path": str(DB_PATH)
            }
        }), 500

@app.route('/api/patients', methods=['POST', 'OPTIONS'])
def create_patient():
    """Create patient with database storage"""
    if request.method == 'OPTIONS':
        return '', 200
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided',
                'timestamp': datetime.now().isoformat()
            }), 400
        
        conn = get_db_connection()
        if not conn:
            return jsonify({
                'success': False,
                'error': 'Database connection failed',
                'timestamp': datetime.now().isoformat()
            }), 500
        
        cursor = conn.cursor()
        
        # Generate patient ID
        patient_id = f"PAT_{int(datetime.now().timestamp())}"
        
        # Insert patient
        cursor.execute("""
            INSERT INTO patients (
                id, first_name, last_name, id_number, phone_number, 
                email, date_of_birth, age, gender, address,
                emergency_contact_name, emergency_contact_phone, emergency_contact_relationship
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            patient_id,
            data.get('firstName', ''),
            data.get('lastName', ''),
            data.get('idNumber', ''),
            data.get('phone', ''),
            data.get('email', ''),
            data.get('dateOfBirth', ''),
            data.get('age', 0),
            data.get('gender', ''),
            data.get('address', ''),
            data.get('emergencyContact', {}).get('name', ''),
            data.get('emergencyContact', {}).get('phone', ''),
            data.get('emergencyContact', {}).get('relationship', '')
        ))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Patient created in database: {patient_id}")
        
        return jsonify({
            'success': True,
            'data': {
                'patient_id': patient_id,
                'action': 'created',
                'message': 'Patient registered successfully in Ubuntu Health database'
            },
            'timestamp': datetime.now().isoformat()
        }), 201
        
    except Exception as e:
        print(f"❌ Error creating patient: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/patients', methods=['GET'])
def get_patients():
    """Get all patients from database"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({
                'success': False,
                'error': 'Database connection failed',
                'timestamp': datetime.now().isoformat()
            }), 500
        
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM patients ORDER BY last_name, first_name")
        
        rows = cursor.fetchall()
        patients = [dict(row) for row in rows]
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': {
                'patients': patients,
                'count': len(patients)
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        print(f"❌ Error getting patients: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/patients/login', methods=['POST', 'OPTIONS'])
def patient_login():
    """Patient login with database lookup"""
    if request.method == 'OPTIONS':
        return '', 200

    try:
        data = request.get_json()
        patient_id = data.get('patientId') or data.get('id_number') or data.get('sa_id_number')
        phone = data.get('phone') or data.get('phoneNumber') or data.get('phone_number')

        print(f"Patient login attempt: ID {patient_id}, Phone {phone}")
        
        conn = get_db_connection()
        if not conn:
            return jsonify({
                'success': False,
                'error': 'Database connection failed',
                'timestamp': datetime.now().isoformat()
            }), 500
        
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM patients WHERE id_number = ?", (patient_id,))
        patient_row = cursor.fetchone()
        
        conn.close()
        
        if patient_row:
            patient = dict(patient_row)
            print(f"✅ Patient authenticated from database")
            return jsonify({
                'success': True,
                'data': {
                    'patient': patient,
                    'authenticated': True
                },
                'timestamp': datetime.now().isoformat()
            })
        else:
            print(f"❌ Patient not found in database")
            return jsonify({
                'success': False,
                'error': 'Patient not found in Ubuntu Health database. Please register first.',
                'redirect_to_registration': True,
                'timestamp': datetime.now().isoformat()
            }), 404

    except Exception as e:
        print(f"❌ Login error: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/patients/register', methods=['POST', 'OPTIONS'])
def register_patient():
    """Patient registration endpoint (alias for create_patient)"""
    if request.method == 'OPTIONS':
        return '', 200

    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided',
                'timestamp': datetime.now().isoformat()
            }), 400

        print(f"🔄 Patient registration request received")
        print(f"📤 Registration data keys: {list(data.keys())}")

        conn = get_db_connection()
        if not conn:
            return jsonify({
                'success': False,
                'error': 'Database connection failed',
                'timestamp': datetime.now().isoformat()
            }), 500

        cursor = conn.cursor()

        # Generate patient ID
        patient_id = f"PAT_{int(datetime.now().timestamp())}"

        # Map frontend fields to database fields
        first_name = data.get('first_name', '') or data.get('firstName', '')
        last_name = data.get('last_name', '') or data.get('lastName', '')
        id_number = data.get('sa_id_number', '') or data.get('idNumber', '') or data.get('id_number', '')
        phone_number = data.get('phone_number', '') or data.get('phoneNumber', '') or data.get('phone', '')
        email = data.get('email', '')
        date_of_birth = data.get('date_of_birth', '') or data.get('dateOfBirth', '')
        age = data.get('age', 0)
        gender = data.get('gender', '')
        address = data.get('address', '')
        emergency_contact_name = data.get('emergency_contact_name', '') or data.get('emergencyContactName', '')
        emergency_contact_phone = data.get('emergency_contact_phone', '') or data.get('emergencyContactPhone', '')
        emergency_contact_relationship = data.get('emergency_contact_relationship', '') or data.get('emergencyContactRelationship', '')

        print(f"📝 Registering patient: {first_name} {last_name} (ID: {id_number})")

        # Insert patient
        cursor.execute("""
            INSERT INTO patients (
                id, first_name, last_name, id_number, phone_number,
                email, date_of_birth, age, gender, address,
                emergency_contact_name, emergency_contact_phone, emergency_contact_relationship
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            patient_id, first_name, last_name, id_number, phone_number,
            email, date_of_birth, age, gender, address,
            emergency_contact_name, emergency_contact_phone, emergency_contact_relationship
        ))

        conn.commit()
        conn.close()

        print(f"✅ Patient registered successfully: {patient_id}")

        return jsonify({
            'success': True,
            'data': {
                'patient_id': patient_id,
                'action': 'created',
                'message': 'Patient registered successfully in Ubuntu Health database'
            },
            'timestamp': datetime.now().isoformat()
        }), 201

    except Exception as e:
        print(f"❌ Error registering patient: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/patients/lookup/<patient_id>', methods=['GET'])
def patient_lookup(patient_id):
    """Patient lookup by ID from database"""
    try:
        print(f"Patient lookup in database: {patient_id}")

        conn = get_db_connection()
        if not conn:
            return jsonify({
                'success': False,
                'error': 'Database connection failed',
                'timestamp': datetime.now().isoformat()
            }), 500

        cursor = conn.cursor()
        cursor.execute("SELECT * FROM patients WHERE id_number = ?", (patient_id,))
        patient_row = cursor.fetchone()

        conn.close()

        if patient_row:
            patient = dict(patient_row)
            return jsonify({
                'success': True,
                'data': {
                    'patient': patient,
                    'found': True
                },
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Patient not found in Ubuntu Health database',
                'found': False,
                'timestamp': datetime.now().isoformat()
            }), 404

    except Exception as e:
        print(f"Lookup error: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/test', methods=['GET', 'POST', 'OPTIONS'])
def test_endpoint():
    """Test endpoint for debugging frontend connectivity"""
    if request.method == 'OPTIONS':
        return '', 200

    return jsonify({
        'success': True,
        'message': 'Backend is reachable from frontend',
        'method': request.method,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/openai/chat', methods=['POST', 'OPTIONS'])
def openai_chat_proxy():
    """OpenAI chat proxy endpoint for AI health assistant"""
    if request.method == 'OPTIONS':
        return '', 200

    try:
        import openai

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided',
                'timestamp': datetime.now().isoformat()
            }), 400

        print(f"🤖 OpenAI Chat request received")
        print(f"📤 Model: {data.get('model', 'unknown')}")
        print(f"📤 Messages count: {len(data.get('messages', []))}")

        # Get OpenAI API key from environment
        openai_api_key = os.getenv('OPENAI_API_KEY')
        if not openai_api_key:
            return jsonify({
                'success': False,
                'error': 'OpenAI API key not configured. Please set OPENAI_API_KEY environment variable.',
                'timestamp': datetime.now().isoformat()
            }), 500

        # Initialize OpenAI client
        client = openai.OpenAI(api_key=openai_api_key)

        # Make actual OpenAI API call
        response = client.chat.completions.create(
            model=data.get('model', 'gpt-4o-mini'),
            messages=data.get('messages', []),
            max_tokens=data.get('max_tokens', 500),
            temperature=data.get('temperature', 0.7)
        )

        # Convert OpenAI response to our format
        response_data = {
            'choices': [{
                'message': {
                    'role': response.choices[0].message.role,
                    'content': response.choices[0].message.content
                },
                'finish_reason': response.choices[0].finish_reason
            }],
            'usage': {
                'prompt_tokens': response.usage.prompt_tokens,
                'completion_tokens': response.usage.completion_tokens,
                'total_tokens': response.usage.total_tokens
            }
        }

        print(f"✅ OpenAI API response received: {response.choices[0].message.content[:100]}...")

        return jsonify({
            'success': True,
            'data': response_data,
            'timestamp': datetime.now().isoformat()
        })

    except ImportError:
        return jsonify({
            'success': False,
            'error': 'OpenAI library not installed. Please install: pip install openai',
            'timestamp': datetime.now().isoformat()
        }), 500
    except Exception as e:
        print(f"❌ Error in OpenAI API call: {e}")
        return jsonify({
            'success': False,
            'error': f'OpenAI API error: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/ai-interactions', methods=['POST', 'OPTIONS'])
def create_ai_interaction():
    """Create AI interaction with database storage"""
    if request.method == 'OPTIONS':
        return '', 200

    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided',
                'timestamp': datetime.now().isoformat()
            }), 400

        print(f"🤖 AI interaction creation request received")
        print(f"📤 Patient ID: {data.get('patient_id', 'Unknown')}")

        conn = get_db_connection()
        if not conn:
            return jsonify({
                'success': False,
                'error': 'Database connection failed',
                'timestamp': datetime.now().isoformat()
            }), 500

        cursor = conn.cursor()

        # Generate interaction ID
        interaction_id = f"AI_{int(datetime.now().timestamp())}"

        # Insert AI interaction
        cursor.execute("""
            INSERT INTO ai_interactions (
                id, patient_id, interaction_type, summary, full_conversation,
                ai_assessment, severity, recommendations, urgent_care
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            interaction_id,
            data.get('patient_id', ''),
            data.get('interaction_type', 'chat'),
            data.get('summary', ''),
            data.get('full_conversation', ''),
            data.get('ai_assessment', ''),
            data.get('severity', 'Low'),
            data.get('recommendations', ''),
            data.get('urgent_care', False)
        ))

        conn.commit()
        conn.close()

        print(f"✅ AI interaction created in database: {interaction_id}")

        return jsonify({
            'success': True,
            'data': {
                'interaction_id': interaction_id,
                'message': 'AI interaction saved successfully'
            },
            'timestamp': datetime.now().isoformat()
        }), 201

    except Exception as e:
        print(f"❌ Error creating AI interaction: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/ai-interactions', methods=['GET'])
def get_ai_interactions():
    """Get all AI interactions from database"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({
                'success': False,
                'error': 'Database connection failed',
                'timestamp': datetime.now().isoformat()
            }), 500

        cursor = conn.cursor()
        cursor.execute("""
            SELECT ai.*, p.first_name, p.last_name
            FROM ai_interactions ai
            LEFT JOIN patients p ON ai.patient_id = p.id
            ORDER BY ai.timestamp DESC
        """)

        rows = cursor.fetchall()
        interactions = []

        for row in rows:
            interaction = dict(row)
            # Add patient name if available
            if interaction.get('first_name') and interaction.get('last_name'):
                interaction['patient_name'] = f"{interaction['first_name']} {interaction['last_name']}"
            interactions.append(interaction)

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'interactions': interactions,
                'count': len(interactions)
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ Error getting AI interactions: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/monitoring/sessions', methods=['GET'])
def get_monitoring_sessions():
    """Get all AI monitoring sessions"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({
                'success': False,
                'error': 'Database connection failed',
                'timestamp': datetime.now().isoformat()
            }), 500

        cursor = conn.cursor()
        cursor.execute("""
            SELECT ms.*, p.first_name, p.last_name, p.phone_number
            FROM monitoring_sessions ms
            LEFT JOIN patients p ON ms.patient_id = p.id
            ORDER BY ms.created_at DESC
        """)

        rows = cursor.fetchall()
        sessions = []

        for row in rows:
            session = dict(row)
            # Add patient name if available
            if session.get('first_name') and session.get('last_name'):
                session['patient_name'] = f"{session['first_name']} {session['last_name']}"
            sessions.append(session)

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'sessions': sessions,
                'count': len(sessions)
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ Error getting monitoring sessions: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/monitoring/sessions', methods=['POST'])
def create_monitoring_session():
    """Create new AI monitoring session"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided',
                'timestamp': datetime.now().isoformat()
            }), 400

        print(f"🤖 Creating monitoring session for patient: {data.get('patient_id')}")

        conn = get_db_connection()
        if not conn:
            return jsonify({
                'success': False,
                'error': 'Database connection failed',
                'timestamp': datetime.now().isoformat()
            }), 500

        cursor = conn.cursor()

        # Generate session ID
        session_id = f"MON_{int(datetime.now().timestamp())}"

        # Create monitoring_sessions table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS monitoring_sessions (
                id TEXT PRIMARY KEY,
                patient_id TEXT,
                case_id TEXT,
                status TEXT DEFAULT 'active',
                medical_context TEXT,
                check_in_count INTEGER DEFAULT 0,
                last_check_in TIMESTAMP,
                next_check_in TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Insert monitoring session
        cursor.execute("""
            INSERT INTO monitoring_sessions (
                id, patient_id, case_id, status, medical_context,
                check_in_count, last_check_in, next_check_in
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            session_id,
            data.get('patient_id', ''),
            data.get('case_id', ''),
            data.get('status', 'active'),
            data.get('medical_context', ''),
            data.get('check_in_count', 0),
            data.get('last_check_in'),
            data.get('next_check_in')
        ))

        conn.commit()
        conn.close()

        print(f"✅ Monitoring session created: {session_id}")

        return jsonify({
            'success': True,
            'data': {
                'session_id': session_id,
                'message': 'Monitoring session created successfully'
            },
            'timestamp': datetime.now().isoformat()
        }), 201

    except Exception as e:
        print(f"❌ Error creating monitoring session: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    # Get configuration from environment variables
    backend_port = int(os.getenv('FLASK_PORT', '5000'))
    backend_host = os.getenv('FLASK_HOST', '127.0.0.1')
    frontend_port = os.getenv('VITE_FRONTEND_PORT', '8085')
    debug_mode = os.getenv('FLASK_DEBUG', 'true').lower() == 'true'

    print("🏥 Ubuntu Health Connect SA - Working Backend")
    print("=" * 60)
    print(f"🚀 Starting backend on http://localhost:{backend_port}")
    print(f"🌐 Frontend expected on http://localhost:{frontend_port}")
    print(f"🗄️ Database: {DB_PATH}")
    print(f"🔧 Debug mode: {debug_mode}")
    
    # Test database connection
    if test_database():
        print("✅ Database connection verified")
    else:
        print("⚠️ Database connection issues detected")
    
    print("=" * 60)
    print("✅ Backend ready for frontend connections!")

    app.run(
        host=backend_host,
        port=backend_port,
        debug=debug_mode,
        use_reloader=False
    )
