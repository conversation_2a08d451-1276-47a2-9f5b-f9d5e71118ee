/**
 * Dynamic Environment Configuration
 * Automatically detects and configures frontend/backend communication
 */

interface EnvironmentConfig {
  // Frontend Configuration
  frontendPort: number;
  frontendHost: string;
  frontendUrl: string;
  
  // Backend Configuration
  backendPort: number;
  backendHost: string;
  backendUrl: string;
  
  // API Endpoints
  apiBaseUrl: string;
  healthEndpoint: string;
  patientsEndpoint: string;
  aiInteractionsEndpoint: string;
  
  // Application Settings
  appName: string;
  appVersion: string;
  environment: string;
  
  // Feature Flags
  autoStartBackend: boolean;
  autoOpenBrowser: boolean;
  enableHotReload: boolean;
  enableConsoleLogs: boolean;
  
  // OpenAI Configuration
  openaiApiKey?: string;
  openaiModel: string;
}

class EnvironmentManager {
  private config: EnvironmentConfig;
  
  constructor() {
    this.config = this.loadConfiguration();
    this.validateConfiguration();
    
    if (this.config.enableConsoleLogs) {
      console.log('🔧 Environment Configuration Loaded:', {
        frontend: `${this.config.frontendUrl}`,
        backend: `${this.config.backendUrl}`,
        environment: this.config.environment
      });
    }
  }
  
  private loadConfiguration(): EnvironmentConfig {
    // Get environment variables with fallbacks
    const frontendPort = this.getEnvNumber('VITE_FRONTEND_PORT', 8085);
    const backendPort = this.getEnvNumber('VITE_BACKEND_PORT', 5002);
    const backendHost = this.getEnvString('VITE_BACKEND_HOST', 'localhost') || 'localhost';
    
    return {
      // Frontend Configuration
      frontendPort,
      frontendHost: 'localhost',
      frontendUrl: `http://localhost:${frontendPort}`,
      
      // Backend Configuration
      backendPort,
      backendHost,
      backendUrl: `http://${backendHost}:${backendPort}`,
      
      // API Configuration
      apiBaseUrl: this.getEnvString('VITE_API_BASE_URL', `http://${backendHost}:${backendPort}`),
      healthEndpoint: this.getEnvString('VITE_API_HEALTH_ENDPOINT', '/health'),
      patientsEndpoint: this.getEnvString('VITE_API_PATIENTS_ENDPOINT', '/api/patients'),
      aiInteractionsEndpoint: this.getEnvString('VITE_API_AI_INTERACTIONS_ENDPOINT', '/api/ai-interactions'),
      
      // Application Settings
      appName: this.getEnvString('VITE_APP_NAME', 'Ubuntu Health Connect SA'),
      appVersion: this.getEnvString('VITE_APP_VERSION', '1.0.0'),
      environment: this.getEnvString('VITE_APP_ENVIRONMENT', 'development'),
      
      // Feature Flags
      autoStartBackend: this.getEnvBoolean('AUTO_START_BACKEND', true),
      autoOpenBrowser: this.getEnvBoolean('AUTO_OPEN_BROWSER', true),
      enableHotReload: this.getEnvBoolean('ENABLE_HOT_RELOAD', true),
      enableConsoleLogs: this.getEnvBoolean('ENABLE_CONSOLE_LOGS', true),
      
      // OpenAI Configuration
      openaiApiKey: this.getEnvString('VITE_OPENAI_API_KEY'),
      openaiModel: this.getEnvString('OPENAI_MODEL', 'gpt-4o-mini'),
    };
  }
  
  private getEnvString(key: string, defaultValue?: string): string | undefined {
    try {
      const value = import.meta.env?.[key] || (typeof process !== 'undefined' ? process.env?.[key] : undefined);
      return value || defaultValue;
    } catch (error) {
      return defaultValue;
    }
  }

  private getEnvNumber(key: string, defaultValue: number): number {
    try {
      const value = import.meta.env?.[key] || (typeof process !== 'undefined' ? process.env?.[key] : undefined);
      return value ? parseInt(value, 10) : defaultValue;
    } catch (error) {
      return defaultValue;
    }
  }

  private getEnvBoolean(key: string, defaultValue: boolean): boolean {
    try {
      const value = import.meta.env?.[key] || (typeof process !== 'undefined' ? process.env?.[key] : undefined);
      if (value === undefined) return defaultValue;
      return value.toLowerCase() === 'true';
    } catch (error) {
      return defaultValue;
    }
  }
  
  private validateConfiguration(): void {
    const errors: string[] = [];
    
    if (!this.config.frontendPort || this.config.frontendPort < 1000) {
      errors.push('Invalid frontend port');
    }
    
    if (!this.config.backendPort || this.config.backendPort < 1000) {
      errors.push('Invalid backend port');
    }
    
    if (this.config.frontendPort === this.config.backendPort) {
      errors.push('Frontend and backend ports cannot be the same');
    }
    
    if (errors.length > 0) {
      console.error('❌ Environment Configuration Errors:', errors);
      throw new Error(`Configuration validation failed: ${errors.join(', ')}`);
    }
  }
  
  // Public API
  getConfig(): EnvironmentConfig {
    return { ...this.config };
  }
  
  getFrontendUrl(): string {
    return this.config.frontendUrl;
  }
  
  getBackendUrl(): string {
    return this.config.backendUrl;
  }
  
  getApiUrl(endpoint?: string): string {
    const base = this.config.apiBaseUrl;
    return endpoint ? `${base}${endpoint}` : base;
  }
  
  getHealthCheckUrl(): string {
    return this.getApiUrl(this.config.healthEndpoint);
  }
  
  getPatientsUrl(): string {
    return this.getApiUrl(this.config.patientsEndpoint);
  }
  
  getAiInteractionsUrl(): string {
    return this.getApiUrl(this.config.aiInteractionsEndpoint);
  }
  
  isDevelopment(): boolean {
    return this.config.environment === 'development';
  }
  
  isProduction(): boolean {
    return this.config.environment === 'production';
  }
  
  // Backend Health Check
  async checkBackendHealth(): Promise<boolean> {
    try {
      const response = await fetch(this.getHealthCheckUrl(), {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.ok) {
        const data = await response.json();
        if (this.config.enableConsoleLogs) {
          console.log('✅ Backend health check passed:', data);
        }
        return true;
      }
      
      return false;
    } catch (error) {
      if (this.config.enableConsoleLogs) {
        console.warn('⚠️ Backend health check failed:', error);
      }
      return false;
    }
  }
  
  // Auto-detect backend URL if not responding
  async autoDetectBackend(): Promise<string | null> {
    const commonPorts = [5001, 5000, 3001, 8000, 8001];
    const host = this.config.backendHost;
    
    for (const port of commonPorts) {
      try {
        const testUrl = `http://${host}:${port}/health`;
        const response = await fetch(testUrl, {
          method: 'GET',
          signal: AbortSignal.timeout(2000)
        });
        
        if (response.ok) {
          const newBackendUrl = `http://${host}:${port}`;
          if (this.config.enableConsoleLogs) {
            console.log(`🔍 Auto-detected backend at: ${newBackendUrl}`);
          }
          
          // Update configuration
          this.config.backendUrl = newBackendUrl;
          this.config.apiBaseUrl = newBackendUrl;
          this.config.backendPort = port;
          
          return newBackendUrl;
        }
      } catch (error) {
        // Continue to next port
      }
    }
    
    return null;
  }
}

// Create singleton instance
export const env = new EnvironmentManager();

// Export configuration for direct access
export const config = env.getConfig();

// Export commonly used URLs
export const FRONTEND_URL = env.getFrontendUrl();
export const BACKEND_URL = env.getBackendUrl();
export const API_BASE_URL = env.getApiUrl();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).env = env;
  (window as any).envConfig = config;
}
