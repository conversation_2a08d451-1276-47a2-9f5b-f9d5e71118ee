"""
HealthConnect SA - Respond.io WhatsApp Integration Backend
Real-time WhatsApp communication via Respond.io API for healthcare providers
"""

import os
import json
import logging
import sqlite3
import hashlib
import hmac
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from flask import Flask, request, jsonify, abort
from flask_cors import CORS
import requests
from openai import OpenAI
import threading
import time
from config.respondio_settings import (
    RESPONDIO_API_KEY, RESPONDIO_WORKSPACE_ID, RESPONDIO_CHANNEL_ID,
    RESPONDIO_WEBHOOK_SECRET, RESPONDIO_BASE_URL, WHATSAPP_BUSINESS_NUMBER,
    OPENAI_API_KEY, OPENAI_MODEL, OPENAI_MAX_TOKENS, OPENAI_TEMPERATURE,
    HEALTHCARE_SYSTEM_PROMPT, AI_EMERGENCY_KEYWORDS, AI_MEDICATION_KEYWORDS,
    SUPPORTED_LANGUAGES, EMERGENCY_PHONE_NUMBER, HEALTHCARE_PROVIDER_NAME,
    DATABASE_PATH, LOG_FILE, WEBHOOK_VERIFY_TOKEN, validate_configuration
)

# Initialize Flask app
app = Flask(__name__)
CORS(app, origins=["http://localhost:8082", "http://127.0.0.1:8082"])

# Configure logging
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize OpenAI client
openai_client = None
try:
    if OPENAI_API_KEY and not OPENAI_API_KEY.startswith('PUT_YOUR_'):
        openai_client = OpenAI(api_key=OPENAI_API_KEY)
        logger.info("✅ OpenAI client initialized successfully")
    else:
        logger.warning("⚠️ OpenAI API key not configured")
except Exception as e:
    logger.error(f"❌ Failed to initialize OpenAI client: {e}")

@dataclass
class Patient:
    """Patient data model"""
    id: str
    name: str
    phone: str
    age: int = 0
    condition: str = ""
    medications: List[str] = None
    language: str = "en"
    status: str = "offline"
    severity: str = "mild"
    last_message: str = ""
    last_message_time: str = ""
    unread_count: int = 0
    medical_history: List[Dict] = None
    
    def __post_init__(self):
        if self.medications is None:
            self.medications = []
        if self.medical_history is None:
            self.medical_history = []

@dataclass
class Message:
    """Message data model"""
    id: str
    patient_id: str
    sender: str  # 'doctor', 'patient', 'system'
    content: str
    timestamp: str
    type: str = 'text'
    status: str = 'sent'
    metadata: Dict = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class DatabaseManager:
    """Database operations for patient and message management"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize database tables"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create patients table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS patients (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    phone TEXT UNIQUE NOT NULL,
                    age INTEGER DEFAULT 0,
                    condition TEXT DEFAULT '',
                    medications TEXT DEFAULT '[]',
                    language TEXT DEFAULT 'en',
                    status TEXT DEFAULT 'offline',
                    severity TEXT DEFAULT 'mild',
                    last_message TEXT DEFAULT '',
                    last_message_time TEXT DEFAULT '',
                    unread_count INTEGER DEFAULT 0,
                    medical_history TEXT DEFAULT '[]',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create messages table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS messages (
                    id TEXT PRIMARY KEY,
                    patient_id TEXT NOT NULL,
                    sender TEXT NOT NULL,
                    content TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    type TEXT DEFAULT 'text',
                    status TEXT DEFAULT 'sent',
                    metadata TEXT DEFAULT '{}',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (patient_id) REFERENCES patients (id)
                )
            ''')
            
            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_patient_id ON messages(patient_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_patients_phone ON patients(phone)')
            
            conn.commit()
            conn.close()
            logger.info("✅ Database initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            raise

class RespondIOAPI:
    """Respond.io API client for WhatsApp communication"""
    
    def __init__(self):
        self.api_key = RESPONDIO_API_KEY
        self.workspace_id = RESPONDIO_WORKSPACE_ID
        self.channel_id = RESPONDIO_CHANNEL_ID
        self.base_url = RESPONDIO_BASE_URL
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    def send_message(self, phone_number: str, message: str, message_type: str = 'text') -> Dict[str, Any]:
        """Send message via Respond.io WhatsApp API"""
        try:
            # Clean phone number format
            clean_phone = phone_number.replace('+', '').replace('-', '').replace(' ', '')
            if not clean_phone.startswith('27'):
                clean_phone = '27' + clean_phone.lstrip('0')
            
            payload = {
                'channelId': self.channel_id,
                'recipient': {
                    'phone': f'+{clean_phone}'
                },
                'message': {
                    'type': message_type,
                    'text': message
                }
            }
            
            response = requests.post(
                f'{self.base_url}/messages',
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ Message sent successfully to {phone_number}")
                return {'success': True, 'message_id': result.get('id'), 'data': result}
            else:
                logger.error(f"❌ Failed to send message: {response.status_code} - {response.text}")
                return {'success': False, 'error': f'API error: {response.status_code}'}
                
        except Exception as e:
            logger.error(f"❌ Error sending message via Respond.io: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_contacts(self) -> List[Dict]:
        """Get contacts from Respond.io"""
        try:
            response = requests.get(
                f'{self.base_url}/contacts',
                headers=self.headers,
                params={'workspaceId': self.workspace_id},
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json().get('data', [])
            else:
                logger.error(f"❌ Failed to get contacts: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"❌ Error getting contacts: {e}")
            return []

# Initialize components
db = DatabaseManager(DATABASE_PATH)
respondio_api = RespondIOAPI()

def verify_webhook_signature(payload: bytes, signature: str) -> bool:
    """Verify webhook signature from Respond.io"""
    if not RESPONDIO_WEBHOOK_SECRET:
        return True  # Skip verification if no secret configured
    
    expected_signature = hmac.new(
        RESPONDIO_WEBHOOK_SECRET.encode(),
        payload,
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(f'sha256={expected_signature}', signature)

def process_ai_response(message: str, patient_phone: str, patient_language: str = 'en') -> str:
    """Process message through OpenAI for healthcare response"""
    try:
        if not openai_client:
            return "I apologize, but I'm currently unable to process your message. Please try again later or contact our support team."
        
        # Check for emergency keywords
        message_lower = message.lower()
        is_emergency = any(keyword in message_lower for keyword in AI_EMERGENCY_KEYWORDS)
        
        if is_emergency:
            return f"""🚨 EMERGENCY DETECTED 🚨

For immediate medical emergency:
📞 Call {EMERGENCY_PHONE_NUMBER} (Emergency Services)
🏥 Go to nearest hospital immediately

This is an automated response. A healthcare provider has been notified and will contact you shortly.

{HEALTHCARE_PROVIDER_NAME}"""
        
        # Create healthcare context
        system_message = HEALTHCARE_SYSTEM_PROMPT
        if patient_language != 'en' and patient_language in SUPPORTED_LANGUAGES:
            system_message += f"\n\nPlease respond in {SUPPORTED_LANGUAGES[patient_language]} when appropriate."
        
        response = openai_client.chat.completions.create(
            model=OPENAI_MODEL,
            messages=[
                {"role": "system", "content": system_message},
                {"role": "user", "content": message}
            ],
            max_tokens=OPENAI_MAX_TOKENS,
            temperature=OPENAI_TEMPERATURE
        )
        
        ai_response = response.choices[0].message.content.strip()
        
        # Add healthcare provider signature
        ai_response += f"\n\n---\n{HEALTHCARE_PROVIDER_NAME}\n📞 Emergency: {EMERGENCY_PHONE_NUMBER}"
        
        return ai_response
        
    except Exception as e:
        logger.error(f"❌ AI processing error: {e}")
        return "I apologize, but I'm experiencing technical difficulties. Please try again or contact our support team if this persists."

# Initialize database and create sample patient
def setup_test_patient():
    """No hardcoded test patient setup - patients come from real registrations"""
    logger.info("ℹ️ No test patient setup needed - using real patient data")

# API Routes

@app.route('/webhook/respondio', methods=['GET', 'POST'])
def respondio_webhook():
    """Respond.io webhook endpoint for receiving WhatsApp messages"""
    if request.method == 'GET':
        # Webhook verification
        verify_token = request.args.get('hub.verify_token')
        challenge = request.args.get('hub.challenge')

        if verify_token == WEBHOOK_VERIFY_TOKEN:
            logger.info("✅ Respond.io webhook verified successfully")
            return challenge
        else:
            logger.error("❌ Webhook verification failed")
            return 'Verification failed', 403

    elif request.method == 'POST':
        # Process incoming message
        try:
            # Verify signature if configured
            signature = request.headers.get('X-Hub-Signature-256', '')
            if RESPONDIO_WEBHOOK_SECRET and not verify_webhook_signature(request.data, signature):
                logger.error("❌ Invalid webhook signature")
                return 'Invalid signature', 403

            data = request.get_json()
            logger.info(f"📨 Received webhook: {json.dumps(data, indent=2)}")

            # Process the webhook data
            if 'type' in data and data['type'] == 'message':
                message_data = data.get('data', {})

                # Extract message information
                contact = message_data.get('contact', {})
                message = message_data.get('message', {})

                phone_number = contact.get('phone', '')
                message_content = message.get('text', '')
                message_id = message_data.get('id', '')

                if phone_number and message_content:
                    # Process the incoming message
                    threading.Thread(
                        target=process_incoming_message,
                        args=(phone_number, message_content, message_id)
                    ).start()

            return 'OK', 200

        except Exception as e:
            logger.error(f"❌ Error processing webhook: {e}")
            return 'Error', 500

def process_incoming_message(phone_number: str, message_content: str, message_id: str):
    """Process incoming WhatsApp message from patient"""
    try:
        logger.info(f"📱 Processing message from {phone_number}: {message_content}")

        # Get or create patient
        patient = get_or_create_patient(phone_number)

        # Save incoming message to database
        save_message(Message(
            id=message_id,
            patient_id=patient.id,
            sender='patient',
            content=message_content,
            timestamp=datetime.now().isoformat(),
            type='text',
            status='received'
        ))

        # Generate AI response
        ai_response = process_ai_response(message_content, phone_number, patient.language)

        # Send AI response back to patient
        result = respondio_api.send_message(phone_number, ai_response)

        if result['success']:
            # Save AI response to database
            save_message(Message(
                id=f"ai_{int(time.time())}",
                patient_id=patient.id,
                sender='system',
                content=ai_response,
                timestamp=datetime.now().isoformat(),
                type='text',
                status='sent'
            ))
            logger.info(f"✅ AI response sent to {phone_number}")
        else:
            logger.error(f"❌ Failed to send AI response: {result.get('error')}")

    except Exception as e:
        logger.error(f"❌ Error processing incoming message: {e}")

def get_or_create_patient(phone_number: str) -> Patient:
    """Get existing patient or create new one"""
    try:
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()

        # Clean phone number
        clean_phone = phone_number.replace('+', '').replace('-', '').replace(' ', '')
        if not clean_phone.startswith('27'):
            clean_phone = '27' + clean_phone.lstrip('0')
        clean_phone = '+' + clean_phone

        # Try to get existing patient
        cursor.execute('SELECT * FROM patients WHERE phone = ?', (clean_phone,))
        row = cursor.fetchone()

        if row:
            # Convert row to Patient object
            patient = Patient(
                id=row[0], name=row[1], phone=row[2], age=row[3], condition=row[4],
                medications=json.loads(row[5]), language=row[6], status=row[7],
                severity=row[8], last_message=row[9], last_message_time=row[10],
                unread_count=row[11], medical_history=json.loads(row[12])
            )
        else:
            # Create new patient
            patient = Patient(
                id=f"patient_{int(time.time())}",
                name=f"Patient {clean_phone[-4:]}",
                phone=clean_phone,
                age=0,
                condition="New Patient",
                medications=[],
                language="en",
                status="online",
                severity="mild",
                last_message="",
                last_message_time="",
                unread_count=0,
                medical_history=[]
            )

            # Save new patient to database
            cursor.execute('''
                INSERT INTO patients
                (id, name, phone, age, condition, medications, language, status, severity,
                 last_message, last_message_time, unread_count, medical_history)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                patient.id, patient.name, patient.phone, patient.age, patient.condition,
                json.dumps(patient.medications), patient.language, patient.status,
                patient.severity, patient.last_message, patient.last_message_time,
                patient.unread_count, json.dumps(patient.medical_history)
            ))
            conn.commit()
            logger.info(f"✅ Created new patient: {patient.name} ({patient.phone})")

        conn.close()
        return patient

    except Exception as e:
        logger.error(f"❌ Error getting/creating patient: {e}")
        raise

def save_message(message: Message):
    """Save message to database"""
    try:
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO messages
            (id, patient_id, sender, content, timestamp, type, status, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            message.id, message.patient_id, message.sender, message.content,
            message.timestamp, message.type, message.status, json.dumps(message.metadata)
        ))

        # Update patient's last message info
        cursor.execute('''
            UPDATE patients
            SET last_message = ?, last_message_time = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (message.content[:100], datetime.now().strftime("%H:%M"), message.patient_id))

        conn.commit()
        conn.close()

    except Exception as e:
        logger.error(f"❌ Error saving message: {e}")

@app.route('/api/patients', methods=['GET'])
def get_patients():
    """Get all patients for frontend"""
    try:
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, name, phone, age, condition, medications, language, status,
                   severity, last_message, last_message_time, unread_count, medical_history
            FROM patients
            ORDER BY updated_at DESC
        ''')

        patients = []
        for row in cursor.fetchall():
            patient_data = {
                'id': row[0],
                'name': row[1],
                'phone': row[2],
                'age': row[3],
                'condition': row[4],
                'medications': json.loads(row[5]),
                'language': row[6],
                'status': row[7],
                'severity': row[8],
                'lastMessage': row[9],
                'lastMessageTime': row[10],
                'unreadCount': row[11],
                'medicalHistory': json.loads(row[12])
            }
            patients.append(patient_data)

        conn.close()
        return jsonify(patients)

    except Exception as e:
        logger.error(f"❌ Error getting patients: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/messages/<patient_id>', methods=['GET'])
def get_messages(patient_id):
    """Get messages for a specific patient"""
    try:
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, patient_id, sender, content, timestamp, type, status, metadata
            FROM messages
            WHERE patient_id = ?
            ORDER BY timestamp ASC
        ''', (patient_id,))

        messages = []
        for row in cursor.fetchall():
            message_data = {
                'id': row[0],
                'patientId': row[1],
                'sender': row[2],
                'content': row[3],
                'timestamp': row[4],
                'type': row[5],
                'status': row[6],
                'metadata': json.loads(row[7])
            }
            messages.append(message_data)

        conn.close()
        return jsonify(messages)

    except Exception as e:
        logger.error(f"❌ Error getting messages: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/send-doctor-message', methods=['POST'])
def send_doctor_message():
    """Send message from doctor to patient via Respond.io"""
    try:
        data = request.get_json()
        patient_phone = data.get('patient_phone')
        message = data.get('message')
        doctor_id = data.get('doctor_id', 'doctor_001')

        if not patient_phone or not message:
            return jsonify({'error': 'Missing patient_phone or message'}), 400

        # Send message via Respond.io
        result = respondio_api.send_message(patient_phone, message)

        if result['success']:
            # Get patient
            patient = get_or_create_patient(patient_phone)

            # Save message to database
            save_message(Message(
                id=result.get('message_id', f"doc_{int(time.time())}"),
                patient_id=patient.id,
                sender='doctor',
                content=message,
                timestamp=datetime.now().isoformat(),
                type='text',
                status='sent'
            ))

            logger.info(f"✅ Doctor message sent to {patient_phone}")
            return jsonify({
                'success': True,
                'message_id': result.get('message_id'),
                'message': f'Message sent successfully to {patient_phone}'
            })
        else:
            logger.error(f"❌ Failed to send doctor message: {result.get('error')}")
            return jsonify({
                'success': False,
                'error': result.get('error')
            }), 500

    except Exception as e:
        logger.error(f"❌ Error sending doctor message: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/send-reminder', methods=['POST'])
def send_medication_reminder():
    """Send medication reminder to patient"""
    try:
        data = request.get_json()
        patient_phone = data.get('patient_phone')
        medication_name = data.get('medication_name', 'your medication')

        if not patient_phone:
            return jsonify({'error': 'Missing patient_phone'}), 400

        reminder_message = f"""💊 Medication Reminder

Time to take your {medication_name}. Please confirm when taken.

Medication: {medication_name}
Time: {datetime.now().strftime('%H:%M')}

---
{HEALTHCARE_PROVIDER_NAME}
📞 Emergency: {EMERGENCY_PHONE_NUMBER}"""

        # Send reminder via Respond.io
        result = respondio_api.send_message(patient_phone, reminder_message)

        if result['success']:
            # Get patient and save message
            patient = get_or_create_patient(patient_phone)
            save_message(Message(
                id=result.get('message_id', f"reminder_{int(time.time())}"),
                patient_id=patient.id,
                sender='system',
                content=reminder_message,
                timestamp=datetime.now().isoformat(),
                type='medication_reminder',
                status='sent',
                metadata={'medicationName': medication_name}
            ))

            return jsonify({
                'success': True,
                'message_id': result.get('message_id'),
                'message': f'Medication reminder sent to {patient_phone}'
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error')
            }), 500

    except Exception as e:
        logger.error(f"❌ Error sending medication reminder: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/send-checkin', methods=['POST'])
def send_health_checkin():
    """Send health check-in to patient"""
    try:
        data = request.get_json()
        patient_phone = data.get('patient_phone')

        if not patient_phone:
            return jsonify({'error': 'Missing patient_phone'}), 400

        checkin_message = f"""🏥 Health Check-in

How are you feeling today? Any symptoms or concerns you'd like to discuss?

Please reply with:
- How you're feeling
- Any symptoms
- Questions about your health

---
{HEALTHCARE_PROVIDER_NAME}
📞 Emergency: {EMERGENCY_PHONE_NUMBER}"""

        # Send check-in via Respond.io
        result = respondio_api.send_message(patient_phone, checkin_message)

        if result['success']:
            # Get patient and save message
            patient = get_or_create_patient(patient_phone)
            save_message(Message(
                id=result.get('message_id', f"checkin_{int(time.time())}"),
                patient_id=patient.id,
                sender='system',
                content=checkin_message,
                timestamp=datetime.now().isoformat(),
                type='health_checkin',
                status='sent'
            ))

            return jsonify({
                'success': True,
                'message_id': result.get('message_id'),
                'message': f'Health check-in sent to {patient_phone}'
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error')
            }), 500

    except Exception as e:
        logger.error(f"❌ Error sending health check-in: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """System health check"""
    try:
        # Test database connection
        db_status = True
        try:
            conn = sqlite3.connect(db.db_path)
            conn.execute("SELECT 1")
            conn.close()
        except:
            db_status = False

        # Test OpenAI connection
        openai_status = openai_client is not None

        # Test Respond.io API configuration
        respondio_status = bool(RESPONDIO_API_KEY and not RESPONDIO_API_KEY.startswith('PUT_YOUR_'))

        return jsonify({
            'status': 'healthy' if all([db_status, openai_status, respondio_status]) else 'degraded',
            'timestamp': datetime.now().isoformat(),
            'services': {
                'database': db_status,
                'openai': openai_status,
                'respondio_api': respondio_status,
                'whatsapp_status': respondio_status
            },
            'version': '2.0.0-respondio',
            'features': [
                'Respond.io WhatsApp Business API integration',
                'AI-powered patient communication',
                'Real-time webhook processing',
                'Comprehensive patient data management',
                'Advanced message logging and analytics',
                'Emergency detection and escalation',
                'Multi-language support'
            ]
        })

    except Exception as e:
        logger.error(f"❌ Health check error: {e}")
        return jsonify({'error': str(e)}), 500

# Setup test patient on startup
setup_test_patient()

if __name__ == '__main__':
    try:
        validate_configuration()
        logger.info("🚀 Starting HealthConnect SA Respond.io Backend...")
        logger.info(f"📱 WhatsApp Business Number: {WHATSAPP_BUSINESS_NUMBER}")
        logger.info(f"🏥 Healthcare Provider: {HEALTHCARE_PROVIDER_NAME}")
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        logger.error(f"❌ Failed to start server: {e}")
        exit(1)
