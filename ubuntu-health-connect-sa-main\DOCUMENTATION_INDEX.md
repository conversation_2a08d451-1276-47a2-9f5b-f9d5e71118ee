# 📚 Ubuntu Health Connect SA - Documentation Index

## 📖 **Main Documentation**

### **🏠 Primary Documentation**
- **[README.md](README.md)** - Complete project overview, setup, and usage guide
- **[QUICK_REFERENCE.md](QUICK_REFERENCE.md)** - Quick commands and project structure reference

## 🔧 **Technical Documentation**

### **🏥 Backend Documentation**
Located in `Health Agent Voice/` directory:
- **Configuration files**: `.env.example` for environment setup
- **Database schema**: `database/schema.sql` for database structure
- **API endpoints**: Documented in main README.md

### **📞 Voice Triage Documentation**
Located in `voice-triage/` directory:
- **Configuration**: `config.py` for voice system setup
- **API integration**: Voice triage APIs integrated with main backend
- **Testing**: `run_tests.py` for comprehensive testing

### **🎨 Frontend Documentation**
Located in `src/` directory:
- **Components**: React components in `src/components/`
- **Services**: API services in `src/services/`
- **Types**: TypeScript definitions in `src/types/`

## 🚀 **Quick Start Guide**

### **1. Setup**
```bash
# Install dependencies
npm install
cd "Health Agent Voice"
pip install -r requirements.txt
```

### **2. Configuration**
```bash
# Copy and edit environment files
cp "Health Agent Voice/.env.example" "Health Agent Voice/.env"
# Add your API keys to .env
```

### **3. Start Applications**
```bash
# Enhanced Backend
cd "Health Agent Voice"
python app_enhanced.py

# Frontend
npm run dev
```

### **4. Access Applications**
- **Frontend**: http://localhost:8081
- **Backend API**: http://localhost:5000
- **Health Check**: http://localhost:5000/health

## 🧪 **Testing**

### **System Tests**
```bash
# Test enhanced backend
python test_restructured_system.py

# Test integration
node test_enhanced_integration.js

# Test voice triage
cd voice-triage
python run_tests.py
```

## 📁 **Project Structure**

```
ubuntu-health-connect-sa/
├── 📱 Frontend (React + TypeScript)
│   ├── src/components/          # UI Components
│   ├── src/services/           # API Services
│   └── src/types/              # TypeScript Types
├── 🏥 Enhanced Backend (Python Flask)
│   ├── Health Agent Voice/     # Main Backend
│   └── voice-triage/          # Voice Triage System
├── 🔗 Shared Resources
│   ├── shared/types/          # Shared Types
│   └── database/              # Database Files
└── 📚 Documentation
    ├── README.md              # Main Documentation
    ├── QUICK_REFERENCE.md     # Quick Reference
    └── DOCUMENTATION_INDEX.md # This File
```

## 🔗 **Key Features**

- ✅ **AI-Powered Healthcare**: OpenAI GPT-4 integration
- ✅ **Voice Triage**: 24/7 phone-based health assessments
- ✅ **WhatsApp Integration**: Real-time patient communication
- ✅ **Multilingual Support**: All 11 South African languages
- ✅ **Provider Dashboard**: Complete patient management
- ✅ **Emergency Detection**: Automatic routing to emergency services
- ✅ **POPIA Compliant**: South African data protection standards

## 🆘 **Support**

### **Getting Help**
1. **Check README.md** for comprehensive documentation
2. **Use QUICK_REFERENCE.md** for quick commands
3. **Run health checks** to verify system status
4. **Check logs** in `Health Agent Voice/logs/` for debugging

### **Common Issues**
- **Backend not starting**: Check `.env` file configuration
- **Frontend connection issues**: Verify backend is running on port 5000
- **API errors**: Check OpenAI API key and Twilio credentials
- **Database issues**: Run `python init_database.py` to reset database

---

**This documentation index replaces multiple scattered README files and provides a single source of truth for project navigation.**
