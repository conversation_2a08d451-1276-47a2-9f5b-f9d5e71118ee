import React, { useEffect, useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AnimatedBackground, FloatingElements, PremiumGlassContainer } from '@/components/ui/animated-background';
import { useAuth, AuthProvider, LoginForm } from '@/hooks/useAuth';
import { AuditLog, SearchFilters } from '@/types/medical';
import { patientApiService, PatientMedicalRecord } from '@/services/patientApiService';
import { PatientLookupResult } from '@/services/patientRegistrationService';
import { DatabasePatient, ChatInteraction } from '@/services/healthcareDatabase';
import { authDatabaseService, AuthDatabasePatient } from '@/services/authDatabaseService';
import { HealthcareStats } from '@/components/HealthcareStats';
import {
  Search,
  ArrowLeft,
  FileText,
  Calendar,
  Phone,
  MessageSquare,
  Download,
  Printer,
  Shield,
  Clock,
  User,
  Heart,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Filter,
  Eye,
  Lock,
  Database
} from 'lucide-react';

interface HealthStatisticsProps {
  onBack?: () => void;
}

const PatientMedicalRecordsSystem: React.FC<HealthStatisticsProps> = ({ onBack }) => {
  const { user, isAuthorized, logout, checkPermission } = useAuth();
  const [isVisible, setIsVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<PatientLookupResult[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<DatabasePatient | null>(null);
  const [selectedPatientAIInteractions, setSelectedPatientAIInteractions] = useState<ChatInteraction[]>([]);
  const [selectedPatientAISummary, setSelectedPatientAISummary] = useState<string>('');
  const [activeTab, setActiveTab] = useState('dashboard');
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingPatient, setIsLoadingPatient] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    if (isAuthorized) {
      // Load all registered patients
      loadAllPatients();
      // Test specific patient connections
      testPatientConnections();
    }
  }, [isAuthorized]);

  // Auto-reload patients when search tab becomes active
  useEffect(() => {
    if (activeTab === 'search' && isAuthorized) {
      console.log('🔄 Search tab activated - reloading patients...');
      loadAllPatients();
    }
  }, [activeTab, isAuthorized]);

  // Auto-reload patients when search tab becomes active
  useEffect(() => {
    if (activeTab === 'search' && isAuthorized) {
      console.log('🔄 Search tab activated - reloading patients...');
      loadAllPatients();
    }
  }, [activeTab, isAuthorized]);

  const loadAllPatients = async () => {
    try {
      console.log('🔗 Loading all registered patients from authentication database...');

      // Load from authentication database using our service
      const authPatients = await authDatabaseService.getAllPatientsForFrontend();

      if (authPatients.length > 0) {
        console.log(`✅ Loaded ${authPatients.length} patients from authentication database`);
        setSearchResults(authPatients);
        return;
      }

      console.log('ℹ️ No patients found in authentication database');
      setSearchResults([]);
    } catch (error) {
      console.error('❌ Error loading registered patients:', error);
      setSearchResults([]);
    }
  };

  const testPatientConnections = async () => {
    try {
      console.log('🧪 Testing patient connections...');

      // First, let's see what patients are available
      console.log('📋 Checking all available patients...');
      const allPatients = await patientApiService.getAllPatients('PROV001');
      console.log(`Found ${allPatients.length} patients in system:`);
      allPatients.forEach((patientResult, index) => {
        const patient = patientResult.patient;
        console.log(`   ${index + 1}. ${patient.personalInfo.firstName} ${patient.personalInfo.lastName} (${patient.id}) - SA ID: ${patient.personalInfo.idNumber}`);
      });

      // Now test Nomsa specifically
      const nomsaTest = await patientApiService.testPatientConnection('Nomsa Mthembu');
      if (nomsaTest) {
        console.log('✅ Nomsa Mthembu frontend-backend connection verified');
      } else {
        console.error('❌ Nomsa Mthembu connection test failed');
      }
    } catch (error) {
      console.error('❌ Error testing patient connections:', error);
    }
  };

  const testNomsaConnection = async () => {
    try {
      console.log('🧪 Testing Nomsa Mthembu specific connection...');

      // Test by SA ID number
      const nomsaRecord = await patientApiService.getPatientByIdNumber('8503155678901', 'PROV001');

      if (nomsaRecord) {
        console.log('✅ Nomsa Mthembu medical record successfully retrieved:');
        console.log(`   - Name: ${nomsaRecord.patient.personalInfo.firstName} ${nomsaRecord.patient.personalInfo.lastName}`);
        console.log(`   - Patient ID: ${nomsaRecord.patient.id}`);
        console.log(`   - SA ID: ${nomsaRecord.patient.personalInfo.idNumber}`);
        console.log(`   - Medical conditions: ${nomsaRecord.patient.medicalHistory.length}`);
        console.log(`   - Medications: ${nomsaRecord.patient.medications.length}`);
        console.log(`   - Lab results: ${nomsaRecord.patient.labResults.length}`);
        console.log(`   - Allergies: ${nomsaRecord.patient.allergies.length}`);
        console.log(`   - AI interactions: ${nomsaRecord.aiInteractions.length}`);

        // Show detailed medical conditions
        let conditionsText = '';
        nomsaRecord.patient.medicalHistory.forEach((condition, index) => {
          conditionsText += `${index + 1}. ${condition.condition} (${condition.severity})\n`;
        });

        // Show medications
        let medicationsText = '';
        nomsaRecord.patient.medications.forEach((med, index) => {
          medicationsText += `${index + 1}. ${med.name} ${med.dosage}\n`;
        });

        // Display success message to user
        alert(`✅ Nomsa Mthembu Connection Test PASSED!\n\n` +
              `Patient: ${nomsaRecord.patient.personalInfo.firstName} ${nomsaRecord.patient.personalInfo.lastName}\n` +
              `Patient ID: ${nomsaRecord.patient.id}\n` +
              `SA ID: ${nomsaRecord.patient.personalInfo.idNumber}\n\n` +
              `Medical Conditions (${nomsaRecord.patient.medicalHistory.length}):\n${conditionsText}\n` +
              `Medications (${nomsaRecord.patient.medications.length}):\n${medicationsText}\n` +
              `Lab Results: ${nomsaRecord.patient.labResults.length}\n` +
              `Allergies: ${nomsaRecord.patient.allergies.length}\n` +
              `AI Interactions: ${nomsaRecord.aiInteractions.length}\n\n` +
              `Frontend-Backend connection is working correctly!`);
      } else {
        console.error('❌ Nomsa Mthembu medical record not found');
        alert('❌ Nomsa Mthembu Connection Test FAILED!\n\nMedical record not found. Check frontend-backend connection.');
      }
    } catch (error) {
      console.error('❌ Error testing Nomsa connection:', error);
      alert(`❌ Nomsa Mthembu Connection Test ERROR!\n\n${error}`);
    }
  };

  const testCurrentPatientLookup = async () => {
    console.log('🧪 Testing current logged-in patient lookup...');

    try {
      const currentPatientIdNumber = localStorage.getItem('currentPatientIdNumber');
      const currentPatientName = localStorage.getItem('currentPatientName');

      if (!currentPatientIdNumber) {
        console.log('❌ No current patient logged in');
        alert('❌ No Patient Logged In!\n\nPlease log in through the Patient Portal first to test patient lookup.');
        return;
      }

      console.log(`🔍 Looking up current patient: ${currentPatientName} (ID: ${currentPatientIdNumber})`);

      // Check authentication database
      const registrationResult = await authDatabaseService.getPatientByIdNumberForFrontend(currentPatientIdNumber);

      if (registrationResult) {
        console.log('✅ Patient found in Authentication Database');
        console.log(`   - Name: ${registrationResult.patient.personalInfo.firstName} ${registrationResult.patient.personalInfo.lastName}`);
        console.log(`   - Patient ID: ${registrationResult.patient.id}`);

        // Force reload the patient list
        await loadAllPatients();

        // Display the patient record
        setSelectedPatient(registrationResult.patient);

        // Get AI interactions
        const { healthcareDatabase } = await import('@/services/healthcareDatabase');
        const aiInteractions = await healthcareDatabase.getAIInteractionsByPatient(registrationResult.patient.id);
        setSelectedPatientAIInteractions(aiInteractions);
        setSelectedPatientAISummary(aiInteractions.length > 0 ?
          `Patient has ${aiInteractions.length} AI interaction${aiInteractions.length !== 1 ? 's' : ''} on record.` :
          'No AI interactions recorded for this patient.'
        );
        setActiveTab('patient-record');

        alert(`✅ CURRENT PATIENT LOOKUP SUCCESS!\n\n` +
              `Patient: ${registrationResult.patient.personalInfo.firstName} ${registrationResult.patient.personalInfo.lastName}\n` +
              `Patient ID: ${registrationResult.patient.id}\n` +
              `SA ID: ${registrationResult.patient.personalInfo.idNumber}\n` +
              `AI Interactions: ${aiInteractions.length}\n\n` +
              `✅ Patient found in system\n` +
              `✅ Medical record loaded\n` +
              `✅ Switched to Medical Record tab`);
      } else {
        console.log('❌ Current patient not found in registration service');
        alert(`❌ Current Patient Lookup Test FAILED!\n\n` +
              `Patient: ${currentPatientName}\n` +
              `SA ID: ${currentPatientIdNumber}\n\n` +
              `Patient not found in registration service.\n` +
              `Run ultimatePatientFix() to register the patient properly.`);
      }
    } catch (error) {
      console.error('❌ Error testing current patient lookup:', error);
      alert(`❌ Current Patient Lookup Test ERROR!\n\n${error}`);
    }
  };

  const performSearch = async (query: string) => {
    setIsSearching(true);

    // Simulate API delay for better UX
    await new Promise(resolve => setTimeout(resolve, 300));

    try {
      if (!query.trim()) {
        // Load all patients from authentication database
        console.log('🔍 Loading all registered patients from auth database...');
        await loadAllPatients();
      } else {
        // Search through authentication database
        console.log(`🔍 Searching auth database for: "${query}"`);
        const searchResults = await authDatabaseService.getAllPatientsForFrontend(query);

        setSearchResults(searchResults);
        console.log(`✅ Found ${searchResults.length} patients matching "${query}" in auth database`);
      }
    } catch (error) {
      console.error('❌ Error searching patients in auth database:', error);
      setSearchResults([]);
    }

    setIsSearching(false);

    // Log search action
    if (query.trim()) {
      logAuditAction('Search', '', `Searched for: "${query}"`);
    }
  };

  // Debounced search to avoid excessive API calls
  const debouncedSearch = useMemo(
    () => {
      let timeoutId: NodeJS.Timeout;
      return (query: string) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => performSearch(query), 500);
      };
    },
    []
  );

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      debouncedSearch(query);
    } else {
      // Load all patients when search is cleared using API service
      loadAllPatients();
      setIsSearching(false);
    }
  };

  const handlePatientSelect = async (patientResult: PatientLookupResult) => {
    setIsLoadingPatient(true);

    try {
      const patient = patientResult.patient;
      console.log(`👤 Loading medical record for: ${patient.personalInfo.firstName} ${patient.personalInfo.lastName} (${patient.id})`);

      // Get AI interactions from healthcare database
      const { healthcareDatabase } = await import('@/services/healthcareDatabase');
      const aiInteractions = await healthcareDatabase.getAIInteractionsByPatient(patient.id);

      // Create AI summary from interactions
      let aiSummary = '';
      if (aiInteractions.length > 0) {
        const totalInteractions = aiInteractions.length;
        const urgentInteractions = aiInteractions.filter(i =>
          i.aiAssessment && (i.aiAssessment.severity === 'High' || i.aiAssessment.severity === 'Critical')
        ).length;
        const recentInteraction = aiInteractions[aiInteractions.length - 1];

        aiSummary = `Patient has ${totalInteractions} AI interaction${totalInteractions !== 1 ? 's' : ''} on record. ` +
                   `${urgentInteractions > 0 ? `${urgentInteractions} urgent interaction${urgentInteractions !== 1 ? 's' : ''} requiring attention. ` : ''}` +
                   `Most recent: ${recentInteraction.summary} (${new Date(recentInteraction.timestamp).toLocaleDateString()})`;
      } else {
        aiSummary = 'No AI interactions recorded for this patient.';
      }

      // Set the selected patient data
      setSelectedPatient(patient);
      setSelectedPatientAIInteractions(aiInteractions);
      setSelectedPatientAISummary(aiSummary);
      setActiveTab('patient-record');

      console.log(`✅ Medical record loaded for ${patient.personalInfo.firstName} ${patient.personalInfo.lastName}:`);
      console.log(`   - Patient ID: ${patient.id}`);
      console.log(`   - SA ID: ${patient.personalInfo.idNumber}`);
      console.log(`   - Medical conditions: ${patient.medicalHistory.length}`);
      console.log(`   - Medications: ${patient.medications.length}`);
      console.log(`   - AI interactions: ${aiInteractions.length}`);
      console.log(`   - Lab results: ${patient.labResults.length}`);
      console.log(`   - Vaccinations: ${patient.vaccinations.length}`);
      console.log(`   - Appointments: ${patient.appointments.length}`);

      // Log access action
      logAuditAction('View', patient.id, `Viewed medical record for ${patient.personalInfo.firstName} ${patient.personalInfo.lastName}`);
    } catch (error) {
      console.error('❌ Error loading medical record:', error);
      alert(`❌ Error Loading Medical Record!\n\nPatient: ${patientResult.patient.personalInfo.firstName} ${patientResult.patient.personalInfo.lastName}\nError: ${error}`);
    } finally {
      setIsLoadingPatient(false);
    }
  };

  const logAuditAction = (action: AuditLog['action'], patientId: string, details: string) => {
    if (!user) return;

    const newLog: AuditLog = {
      id: `LOG${Date.now()}`,
      userId: user.id,
      userRole: user.role,
      action,
      patientId,
      timestamp: new Date().toISOString(),
      ipAddress: '*************', // Mock IP
      details
    };

    setAuditLogs(prev => [newLog, ...prev]);
  };

  const handleExport = (patient: DatabasePatient) => {
    if (!checkPermission('export_data')) {
      alert('You do not have permission to export data.');
      return;
    }

    logAuditAction('Export', patient.id, `Exported medical record for ${patient.personalInfo.firstName} ${patient.personalInfo.lastName}`);
    // Mock export functionality
    alert('Medical record exported successfully!');
  };

  const handlePrint = (patient: DatabasePatient) => {
    logAuditAction('Print', patient.id, `Printed medical record for ${patient.personalInfo.firstName} ${patient.personalInfo.lastName}`);
    // Mock print functionality
    window.print();
  };

  // Make loadAllPatients available globally for debugging
  React.useEffect(() => {
    (window as any).forceReloadPatients = loadAllPatients;
    (window as any).debugHealthcareStats = () => {
      console.log('=== Healthcare Statistics Debug ===');
      console.log('Search Results:', searchResults.length);
      console.log('Selected Patient:', selectedPatient?.id);
      console.log('Active Tab:', activeTab);
      console.log('Is Searching:', isSearching);
      console.log('Is Loading Patient:', isLoadingPatient);
      console.log('=====================================');
    };

    // Test complete patient registration flow
    (window as any).testCompletePatientFlow = async () => {
      console.log('🧪 TESTING COMPLETE PATIENT REGISTRATION FLOW');
      console.log('=' * 60);

      try {
        // Step 1: Test backend connection
        console.log('🔍 Step 1: Testing backend connection...');
        const healthResponse = await fetch('http://localhost:5000/health');
        if (healthResponse.ok) {
          const healthData = await healthResponse.json();
          console.log('✅ Backend is running:', healthData.status);
          console.log('   Database connected:', healthData.database?.connected);
        } else {
          throw new Error('Backend not responding');
        }

        // Step 2: Test patient registration
        console.log('🔍 Step 2: Testing patient registration...');
        const testPatient = {
          patient_id: `TEST_FLOW_${Date.now()}`,
          first_name: 'Test',
          last_name: 'FlowPatient',
          id_number: '9001015800099',
          phone_number: '+27821234999',
          email: '<EMAIL>',
          age: 35,
          gender: 'Male',
          address: 'Test Address, Cape Town'
        };

        const registerResponse = await fetch('http://localhost:5000/api/patients', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(testPatient)
        });

        if (registerResponse.ok) {
          const registerData = await registerResponse.json();
          console.log('✅ Patient registered in backend:', registerData.action);
        } else {
          throw new Error('Patient registration failed');
        }

        // Step 3: Reload patients in dashboard
        console.log('🔍 Step 3: Reloading patients in dashboard...');
        await loadAllPatients();
        console.log('✅ Patients reloaded in dashboard');

        // Step 4: Verify patient appears
        console.log('🔍 Step 4: Verifying patient appears in dashboard...');
        const foundPatient = searchResults.find(p => p.patient.personalInfo.idNumber === testPatient.id_number);
        if (foundPatient) {
          console.log('✅ Patient found in dashboard:', foundPatient.patient.personalInfo.firstName);
        } else {
          console.log('❌ Patient NOT found in dashboard');
        }

        console.log('🎉 COMPLETE PATIENT FLOW TEST COMPLETED!');
        alert('✅ Complete Patient Flow Test Completed!\n\nCheck console for detailed results.');

      } catch (error) {
        console.error('❌ Complete patient flow test failed:', error);
        alert('❌ Complete Patient Flow Test Failed!\n\nCheck console for error details.');
      }
    };

    // Clean test function to avoid ReferenceError
    (window as any).testCurrentPatientLookupClean = async () => {
      console.log('🧪 Testing current patient lookup (clean version)...');

      try {
        const currentPatientIdNumber = localStorage.getItem('currentPatientIdNumber');
        const currentPatientName = localStorage.getItem('currentPatientName');

        if (!currentPatientIdNumber) {
          alert('❌ No Patient Logged In!\n\nPlease log in through the Patient Portal first.');
          return { success: false, error: 'No patient logged in' };
        }

        console.log(`🔍 Looking up: ${currentPatientName} (${currentPatientIdNumber})`);

        // Check registration service
        const { patientRegistrationService } = await import('@/services/patientRegistrationService');
        const registrationResult = patientRegistrationService.getPatientByIdNumber(currentPatientIdNumber);

        if (registrationResult) {
          console.log('✅ Patient found in Registration Service');

          // Force reload patients
          await loadAllPatients();

          // Set selected patient
          setSelectedPatient(registrationResult.patient);
          setActiveTab('patient-record');

          alert(`✅ PATIENT LOOKUP SUCCESS!\n\n` +
                `Patient: ${registrationResult.patient.personalInfo.firstName} ${registrationResult.patient.personalInfo.lastName}\n` +
                `Patient ID: ${registrationResult.patient.id}\n` +
                `SA ID: ${registrationResult.patient.personalInfo.idNumber}\n\n` +
                `✅ Patient found and medical record loaded`);

          return { success: true, patient: registrationResult.patient };
        } else {
          alert(`❌ Patient Not Found!\n\n` +
                `Patient: ${currentPatientName}\n` +
                `SA ID: ${currentPatientIdNumber}\n\n` +
                `Run ultimatePatientFix() to register the patient.`);

          return { success: false, error: 'Patient not found' };
        }
      } catch (error) {
        console.error('❌ Test error:', error);
        alert(`❌ Test Error!\n\n${error}`);
        return { success: false, error: error.message };
      }
    };
  }, [searchResults, selectedPatient, activeTab, isSearching, isLoadingPatient]);

  if (!isAuthorized) {
    return <LoginForm onLogin={() => {}} />;
  }

  return (
    <AnimatedBackground variant="particles" className="min-h-screen">
      <FloatingElements />

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-6">
              {onBack && (
                <Button
                  variant="ghost"
                  size="lg"
                  onClick={onBack}
                  className="text-white hover:bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl px-6 py-3 transition-all duration-300 hover:scale-105"
                >
                  <ArrowLeft className="w-5 h-5 mr-2" />
                  Back
                </Button>
              )}
              <div>
                <div className="flex items-center gap-3 mb-3">
                  <Database className="w-8 h-8 text-white animate-pulse-slow" />
                  <h1 className="text-4xl font-black text-white mb-0 bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent">
                    Ubuntu Health Statistics Dashboard
                  </h1>
                </div>
                <p className="text-white/90 text-lg font-medium">
                  🔒 Provider-Only Access • Comprehensive health analytics across Mzansi communities
                </p>
              </div>
            </div>
            <PremiumGlassContainer className="p-4">
              <div className="flex items-center gap-4">
                <div className="text-white/90">
                  <p className="text-sm font-bold">{user?.name}</p>
                  <p className="text-xs">{user?.role} - {user?.hospital}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={logout}
                  className="text-white border-white/50 hover:bg-white/10"
                >
                  <Lock className="w-4 h-4 mr-2" />
                  Logout
                </Button>
              </div>
            </PremiumGlassContainer>
          </div>
        </div>

        {/* Main Content Tabs */}
        <div className={`transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-5 bg-white/10 backdrop-blur-sm">
              <TabsTrigger value="dashboard" className="text-white data-[state=active]:bg-white data-[state=active]:text-gray-900">
                <Activity className="w-4 h-4 mr-2" />
                🏥 Health Overview
              </TabsTrigger>
              <TabsTrigger value="search" className="text-white data-[state=active]:bg-white data-[state=active]:text-gray-900">
                <Search className="w-4 h-4 mr-2" />
                🔍 Patient Lookup
              </TabsTrigger>
              <TabsTrigger value="patient-record" className="text-white data-[state=active]:bg-white data-[state=active]:text-gray-900" disabled={!selectedPatient}>
                <FileText className="w-4 h-4 mr-2" />
                📋 Medical Record
              </TabsTrigger>
              <TabsTrigger value="audit-logs" className="text-white data-[state=active]:bg-white data-[state=active]:text-gray-900">
                <Shield className="w-4 h-4 mr-2" />
                🛡️ Security Logs
              </TabsTrigger>
              <TabsTrigger value="compliance" className="text-white data-[state=active]:bg-white data-[state=active]:text-gray-900">
                <Lock className="w-4 h-4 mr-2" />
                🔒 POPIA Compliance
              </TabsTrigger>
            </TabsList>

            {/* Dashboard Tab */}
            <TabsContent value="dashboard" className="space-y-6">
              <HealthcareStats />
            </TabsContent>

            {/* Patient Search Tab */}
            <TabsContent value="search" className="space-y-6">
              <PremiumGlassContainer className="p-6">
                <div className="flex items-center gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600 w-5 h-5" />
                    <Input
                      placeholder="Search by Patient ID, Name, or ID Number..."
                      value={searchQuery}
                      onChange={(e) => handleSearch(e.target.value)}
                      className="pl-12 h-12 text-lg rounded-2xl border-2 focus:border-healthcare-blue bg-white/90 text-gray-900 placeholder:text-gray-600"
                    />
                  </div>
                  <Button
                    onClick={testNomsaConnection}
                    variant="outline"
                    className="bg-white/10 border-white/20 text-white hover:bg-white/20 h-12 px-6"
                  >
                    🧪 Test Nomsa
                  </Button>
                  <Button
                    onClick={() => (window as any).testCurrentPatientLookupClean()}
                    variant="outline"
                    className="bg-white/10 border-white/20 text-white hover:bg-white/20 h-12 px-6"
                  >
                    🔍 Test Current Patient
                  </Button>
                </div>

                {/* Advanced Search Filters - Coming Soon */}
                <div className="mb-6">
                  <div className="text-center py-4 text-white/60">
                    <Filter className="w-6 h-6 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Advanced search filters coming soon</p>
                  </div>
                </div>

                {isSearching ? (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                    <p className="text-white/80">Searching registered patients...</p>
                  </div>
                ) : (
                  <>
                    {/* Results Header */}
                    {searchResults.length > 0 && (
                      <div className="mb-6 p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <User className="w-5 h-5 text-white" />
                            <span className="text-white font-semibold">
                              {searchResults.length} Registered Patient{searchResults.length !== 1 ? 's' : ''} Found
                            </span>
                          </div>
                          <div className="text-white/80 text-sm">
                            📋 Ordered by Patient ID • 🔍 Click to view medical record
                          </div>
                        </div>
                        {searchQuery && (
                          <div className="mt-2 text-white/70 text-sm">
                            Search results for: "<span className="font-mono bg-white/10 px-2 py-1 rounded">{searchQuery}</span>"
                          </div>
                        )}
                      </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {searchResults.map((patientResult) => {
                      const patient = patientResult.patient;
                      return (
                        <Card
                          key={patient.id}
                          className="cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105"
                          onClick={() => handlePatientSelect(patientResult)}
                          style={{
                            background: 'linear-gradient(135deg, #E0F7FA 0%, #B2EBF2 50%, #80DEEA 100%)',
                            border: '1px solid rgba(0, 188, 212, 0.3)',
                            borderRadius: '12px'
                          }}
                        >
                        <CardHeader className="pb-3">
                          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                            {/* Patient Name and Status */}
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                              <h3 style={{
                                fontSize: '1.25rem',
                                fontWeight: 'bold',
                                color: '#1F2937',
                                margin: 0
                              }}>
                                {patient.personalInfo.firstName} {patient.personalInfo.lastName}
                              </h3>
                              <div style={{
                                background: 'linear-gradient(135deg, #10B981, #059669)',
                                color: 'white',
                                padding: '0.25rem 0.75rem',
                                borderRadius: '20px',
                                fontSize: '0.75rem',
                                fontWeight: '600',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.25rem'
                              }}>
                                <CheckCircle style={{ width: '12px', height: '12px' }} />
                                Registered
                              </div>
                            </div>

                            {/* Patient ID */}
                            <p style={{
                              fontSize: '0.9rem',
                              color: '#3B82F6',
                              fontFamily: 'monospace',
                              fontWeight: '600',
                              margin: 0
                            }}>
                              {patient.id}
                            </p>

                            {/* AI Chats Badge */}
                            {patientResult.totalInteractions > 0 && (
                              <div style={{
                                background: 'rgba(59, 130, 246, 0.1)',
                                border: '1px solid rgba(59, 130, 246, 0.3)',
                                color: '#3B82F6',
                                padding: '0.25rem 0.5rem',
                                borderRadius: '8px',
                                fontSize: '0.75rem',
                                fontWeight: '500',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.25rem',
                                width: 'fit-content'
                              }}>
                                <MessageSquare style={{ width: '12px', height: '12px' }} />
                                {patientResult.totalInteractions} AI chats
                              </div>
                            )}
                          </div>
                        </CardHeader>
                        <CardContent style={{ padding: '1rem' }}>
                          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                            {/* SA ID */}
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                              <Shield style={{ width: '16px', height: '16px', color: '#3B82F6' }} />
                              <span style={{ fontSize: '0.875rem', color: '#374151' }}>
                                <strong>SA ID:</strong> {patient.personalInfo.idNumber}
                              </span>
                            </div>

                            {/* Age and Gender */}
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                              <Calendar style={{ width: '16px', height: '16px', color: '#10B981' }} />
                              <span style={{ fontSize: '0.875rem', color: '#374151' }}>
                                <strong>Age:</strong> {patient.personalInfo.age} • <strong>Gender:</strong> {patient.personalInfo.gender}
                              </span>
                            </div>

                            {/* Phone */}
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                              <Phone style={{ width: '16px', height: '16px', color: '#8B5CF6' }} />
                              <span style={{ fontSize: '0.875rem', color: '#374151' }}>
                                {patient.personalInfo.phone}
                              </span>
                            </div>

                            {/* Medical Conditions */}
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                              <Heart style={{ width: '16px', height: '16px', color: '#EF4444' }} />
                              <span style={{ fontSize: '0.875rem', color: '#374151' }}>
                                {patient.medicalHistory.length} medical condition{patient.medicalHistory.length !== 1 ? 's' : ''}
                              </span>
                            </div>

                            {/* AI Interactions */}
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                              <MessageSquare style={{ width: '16px', height: '16px', color: '#3B82F6' }} />
                              <span style={{ fontSize: '0.875rem', color: '#374151' }}>
                                {patientResult.totalInteractions} AI interaction{patientResult.totalInteractions !== 1 ? 's' : ''}
                              </span>
                            </div>

                            {/* Registration Info */}
                            <div style={{
                              fontSize: '0.75rem',
                              color: '#6B7280',
                              marginTop: '0.75rem',
                              paddingTop: '0.75rem',
                              borderTop: '1px solid rgba(229, 231, 235, 0.5)',
                              display: 'flex',
                              flexDirection: 'column',
                              gap: '0.25rem'
                            }}>
                              <div>
                                <strong>Registered:</strong> {new Date(patientResult.registrationDate).toLocaleDateString('en-ZA', {
                                  day: '2-digit',
                                  month: '2-digit',
                                  year: 'numeric'
                                })}
                              </div>
                              <div>
                                <strong>Last Activity:</strong> {new Date(patientResult.lastActivity).toLocaleDateString('en-ZA', {
                                  day: '2-digit',
                                  month: '2-digit',
                                  year: 'numeric'
                                })}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                      );
                    })}
                    </div>
                  </>
                )}

                {searchResults.length === 0 && !isSearching && (
                  <div className="text-center py-12 text-white/80">
                    <User className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    {searchQuery ? (
                      <>
                        <p className="text-xl font-semibold mb-2">No registered patients found</p>
                        <p>No patients match your search criteria: "<span className="font-mono">{searchQuery}</span>"</p>
                        <p className="text-sm mt-2">Try searching by Patient ID, Name, SA ID Number, or Phone Number</p>
                      </>
                    ) : (
                      <>
                        <p className="text-xl font-semibold mb-2">No registered patients</p>
                        <p>No patients have registered through the Patient Portal yet</p>
                        <p className="text-sm mt-2">Patients will appear here after they log in and use the AI Health Assistant</p>
                      </>
                    )}
                  </div>
                )}
              </PremiumGlassContainer>
            </TabsContent>

            {/* Patient Medical Record Tab */}
            <TabsContent value="patient-record" className="space-y-6">
              {isLoadingPatient ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
                  <p className="text-white/80 text-lg">Loading patient medical records...</p>
                </div>
              ) : selectedPatient && (
                <div className="space-y-6">
                  {/* Patient Header */}
                  <PremiumGlassContainer className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-4">
                        <div className="bg-healthcare-blue p-3 rounded-full">
                          <User className="w-8 h-8 text-white" />
                        </div>
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900">
                            {selectedPatient.personalInfo.firstName} {selectedPatient.personalInfo.lastName}
                          </h2>
                          <p className="text-gray-600">Patient ID: {selectedPatient.id}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          onClick={() => handleExport(selectedPatient)}
                          className="bg-healthcare-green hover:bg-healthcare-green/90 text-white"
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Export PDF
                        </Button>
                        <Button
                          onClick={() => handlePrint(selectedPatient)}
                          variant="outline"
                          className="border-gray-300 text-gray-700 hover:bg-gray-50"
                        >
                          <Printer className="w-4 h-4 mr-2" />
                          Print
                        </Button>
                      </div>
                    </div>

                    {/* Basic Information Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <Card className="bg-white/50">
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm font-medium text-gray-600">Personal Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2 text-sm">
                          <div><strong>Age:</strong> {selectedPatient.personalInfo.age}</div>
                          <div><strong>Gender:</strong> {selectedPatient.personalInfo.gender}</div>
                          <div><strong>SA ID Number:</strong> {selectedPatient.personalInfo.idNumber}</div>
                          <div><strong>Phone:</strong> {selectedPatient.personalInfo.phone}</div>
                          <div><strong>Email:</strong> {selectedPatient.personalInfo.email}</div>
                          <div><strong>Address:</strong> {selectedPatient.personalInfo.address}</div>
                        </CardContent>
                      </Card>

                      <Card className="bg-white/50">
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm font-medium text-gray-600">Health Status</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2 text-sm">
                          <div className="flex items-center gap-2">
                            <Badge variant="default">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Active Patient
                            </Badge>
                          </div>
                          <div><strong>Medical Conditions:</strong> {selectedPatient.medicalHistory.length}</div>
                          <div><strong>AI Interactions:</strong> {selectedPatientAIInteractions.length}</div>
                          <div><strong>Last Updated:</strong> {new Date(selectedPatient.updatedAt).toLocaleDateString()}</div>
                        </CardContent>
                      </Card>

                      <Card className="bg-white/50">
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm font-medium text-gray-600">Emergency Contact</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2 text-sm">
                          <div><strong>Name:</strong> {selectedPatient.personalInfo.emergencyContact.name}</div>
                          <div><strong>Relationship:</strong> {selectedPatient.personalInfo.emergencyContact.relationship}</div>
                          <div><strong>Phone:</strong> {selectedPatient.personalInfo.emergencyContact.phone}</div>
                        </CardContent>
                      </Card>
                    </div>
                  </PremiumGlassContainer>

                  {/* Medical History Timeline */}
                  <PremiumGlassContainer className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                      <Activity className="w-5 h-5" />
                      Medical History Timeline
                    </h3>
                    <div className="space-y-4">
                      {selectedPatient.medicalHistory.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                          <Activity className="w-12 h-12 mx-auto mb-3 opacity-50" />
                          <p>No medical history recorded</p>
                        </div>
                      ) : (
                        selectedPatient.medicalHistory.map((condition, index) => (
                          <div key={condition.id} className="flex items-start gap-4 p-4 bg-white/50 rounded-lg">
                            <div className="flex-shrink-0">
                              <div className={`w-3 h-3 rounded-full mt-2 ${
                                condition.status === 'Active' ? 'bg-red-500' :
                                condition.status === 'Chronic' ? 'bg-orange-500' :
                                condition.status === 'Under Treatment' ? 'bg-blue-500' :
                                'bg-green-500'
                              }`}></div>
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="font-semibold text-gray-900">{condition.condition}</h4>
                                <Badge variant={
                                  condition.severity === 'Critical' ? 'destructive' :
                                  condition.severity === 'Severe' ? 'destructive' :
                                  condition.severity === 'Moderate' ? 'default' : 'secondary'
                                }>
                                  {condition.severity}
                                </Badge>
                              </div>
                              <div className="text-sm text-gray-600 space-y-1">
                                <div><strong>Diagnosed:</strong> {new Date(condition.diagnosedDate).toLocaleDateString()}</div>
                                <div><strong>Status:</strong> {condition.status}</div>
                                <div><strong>Diagnosed by:</strong> {condition.diagnosedBy}</div>
                                {condition.notes && <div><strong>Notes:</strong> {condition.notes}</div>}
                                {condition.medications && condition.medications.length > 0 && (
                                  <div><strong>Medications:</strong> {condition.medications.join(', ')}</div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </PremiumGlassContainer>

                  {/* Comprehensive AI Interactions */}
                  <PremiumGlassContainer className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                      <MessageSquare className="w-5 h-5" />
                      AI Assistant Interactions ({selectedPatientAIInteractions.length})
                    </h3>

                    {/* AI Summary */}
                    {selectedPatientAISummary && (
                      <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <h4 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
                          <Activity className="w-4 h-4" />
                          AI Interactions Summary
                        </h4>
                        <pre className="text-sm text-blue-800 whitespace-pre-wrap font-mono">
                          {selectedPatientAISummary}
                        </pre>
                      </div>
                    )}

                    <div className="space-y-4">
                      {selectedPatientAIInteractions.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                          <MessageSquare className="w-12 h-12 mx-auto mb-3 opacity-50" />
                          <p>No AI interactions recorded</p>
                        </div>
                      ) : (
                        selectedPatientAIInteractions.map((interaction) => (
                          <div key={interaction.id} className="p-4 bg-white/50 rounded-lg border-l-4 border-blue-500">
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className="border-blue-300 text-blue-700">
                                  {interaction.type === 'AI Health Assistant Chat' ? <MessageSquare className="w-3 h-3 mr-1" /> :
                                   interaction.type === 'Voice Triage' ? <Phone className="w-3 h-3 mr-1" /> :
                                   interaction.type === 'Chat Monitor' ? <MessageSquare className="w-3 h-3 mr-1" /> :
                                   <Activity className="w-3 h-3 mr-1" />}
                                  {interaction.type}
                                </Badge>
                                <span className="text-sm text-gray-600">
                                  {new Date(interaction.timestamp).toLocaleDateString()} at {new Date(interaction.timestamp).toLocaleTimeString()}
                                </span>
                                <Badge variant="outline" className="text-xs">
                                  {interaction.language}
                                </Badge>
                              </div>
                              <div className="flex items-center gap-2">
                                {interaction.aiAssessment && (
                                  <Badge variant={
                                    interaction.aiAssessment.severity === 'Critical' ? 'destructive' :
                                    interaction.aiAssessment.severity === 'High' ? 'destructive' :
                                    interaction.aiAssessment.severity === 'Medium' ? 'default' : 'secondary'
                                  }>
                                    {interaction.aiAssessment.severity}
                                  </Badge>
                                )}
                                <Badge variant={
                                  interaction.status === 'escalated' ? 'destructive' :
                                  interaction.status === 'completed' ? 'default' : 'secondary'
                                }>
                                  {interaction.status === 'escalated' ? <AlertTriangle className="w-3 h-3 mr-1" /> :
                                   interaction.status === 'completed' ? <CheckCircle className="w-3 h-3 mr-1" /> :
                                   <Clock className="w-3 h-3 mr-1" />}
                                  {interaction.status}
                                </Badge>
                              </div>
                            </div>

                            <div className="text-sm text-gray-700 space-y-2">
                              <div><strong>Duration:</strong> {interaction.duration || 'N/A'}</div>
                              <div><strong>Summary:</strong> {interaction.summary}</div>

                              {interaction.aiAssessment && (
                                <>
                                  <div><strong>Symptoms:</strong> {interaction.aiAssessment.symptoms.join(', ')}</div>
                                  <div><strong>AI Decision:</strong> {interaction.aiAssessment.triageDecision}</div>
                                  <div><strong>Recommendations:</strong> {interaction.aiAssessment.recommendations.join(', ')}</div>
                                </>
                              )}

                              <div><strong>Messages:</strong> {interaction.messages.length} messages exchanged</div>
                            </div>

                            {/* Show recent messages */}
                            {interaction.messages.length > 0 && (
                              <div className="mt-3 pt-3 border-t border-gray-200">
                                <h5 className="text-xs font-medium text-gray-600 mb-2">Recent Messages:</h5>
                                <div className="space-y-1 max-h-32 overflow-y-auto">
                                  {interaction.messages.slice(-3).map((message) => (
                                    <div key={message.id} className="text-xs">
                                      <span className={`font-medium ${
                                        message.sender === 'patient' ? 'text-blue-600' :
                                        message.sender === 'ai' ? 'text-green-600' : 'text-purple-600'
                                      }`}>
                                        {message.sender === 'patient' ? '👤 Patient' :
                                         message.sender === 'ai' ? '🤖 AI Assistant' : '👨‍⚕️ Provider'}:
                                      </span>
                                      <span className="text-gray-700 ml-2">{message.content}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ))
                      )}
                    </div>
                  </PremiumGlassContainer>
                </div>
              )}
            </TabsContent>



            {/* Audit Logs Tab */}
            <TabsContent value="audit-logs" className="space-y-6">
              <PremiumGlassContainer className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  System Audit Logs
                </h3>
                <div className="space-y-3">
                  {auditLogs.slice(0, 10).map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-3 bg-white/50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline">
                          {log.action === 'View' ? <Eye className="w-3 h-3 mr-1" /> :
                           log.action === 'Search' ? <Search className="w-3 h-3 mr-1" /> :
                           log.action === 'Export' ? <Download className="w-3 h-3 mr-1" /> :
                           <Printer className="w-3 h-3 mr-1" />}
                          {log.action}
                        </Badge>
                        <div>
                          <div className="text-sm font-medium text-gray-900">{log.details}</div>
                          <div className="text-xs text-gray-600">
                            {log.userRole}: {log.userId} • {new Date(log.timestamp).toLocaleString()}
                          </div>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500">{log.ipAddress}</div>
                    </div>
                  ))}
                  {auditLogs.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <Shield className="w-12 h-12 mx-auto mb-3 opacity-50" />
                      <p>No audit logs available</p>
                    </div>
                  )}
                </div>
              </PremiumGlassContainer>
            </TabsContent>

            {/* Compliance Tab */}
            <TabsContent value="compliance" className="space-y-6">
              <PremiumGlassContainer className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Lock className="w-5 h-5" />
                  HIPAA/POPIA Compliance
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="bg-white/50">
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        Data Encryption
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600">
                        All patient data is encrypted at rest and in transit using AES-256 encryption standards.
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/50">
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        Access Control
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600">
                        Role-based access control ensures only authorized healthcare providers can access patient records.
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/50">
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        Audit Logging
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600">
                        Complete audit trail of all system access and patient record interactions.
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/50">
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        Data Retention
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600">
                        Patient data retention policies comply with South African POPIA regulations.
                      </p>
                    </CardContent>
                  </Card>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-semibold text-blue-900 mb-2">Security Notice</h4>
                  <p className="text-sm text-blue-800">
                    This system is for authorized healthcare providers only. All access is monitored and logged.
                    Unauthorized access is prohibited and may result in legal action.
                  </p>
                </div>
              </PremiumGlassContainer>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </AnimatedBackground>
  );
};

// Wrapper component with AuthProvider
const HealthStatistics: React.FC<HealthStatisticsProps> = (props) => {
  return (
    <AuthProvider>
      <PatientMedicalRecordsSystem {...props} />
    </AuthProvider>
  );
};

export default HealthStatistics;
