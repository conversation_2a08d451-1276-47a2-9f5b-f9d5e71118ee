# Ubuntu Health Connect SA - Backend Dependencies
# Restructured backend requirements

# Core Flask framework
Flask==2.3.3
Werkzeug==2.3.7
flask-cors==4.0.0

# Environment variable management
python-dotenv==1.0.0

# HTTP requests
requests==2.31.0

# OpenAI SDK for AI Assistant
openai>=1.68.2,<2.0.0

# Twilio SDK for voice calls and WhatsApp
twilio==8.10.0

# Database (SQLite is built-in)
# For PostgreSQL support (optional):
# psycopg2-binary==2.9.7

# Data validation and serialization
marshmallow==3.20.1

# Date/time handling
python-dateutil==2.8.2

# UUID generation (built-in, but listed for clarity)
# uuid - built-in Python module

# JSON handling (built-in)
# json - built-in Python module

# Logging (built-in, but enhanced)
# logging - built-in Python module

# Testing dependencies (optional)
pytest==7.4.3
pytest-flask==1.3.0

# Code quality (optional)
black==23.11.0
flake8==6.1.0

# Production dependencies (optional)
gunicorn==21.2.0
