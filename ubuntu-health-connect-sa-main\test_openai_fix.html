<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 OpenAI API Test & Fix</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .api-key-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 OpenAI API Test & Fix</h1>
        
        <div class="test-section">
            <h2>🔑 API Key Configuration</h2>
            <input type="password" class="api-key-input" id="apiKeyInput" 
                   placeholder="Enter OpenAI API Key (sk-...)" 
                   value="********************************************************************************************************************************************************************">
            <button class="test-button" onclick="configureAPIKey()">Configure API Key</button>
            <button class="test-button" onclick="showAPIKey()">Show/Hide Key</button>
            <div id="apiKeyStatus"></div>
        </div>

        <div class="test-section">
            <h2>🧪 API Connection Test</h2>
            <button class="test-button" onclick="testAPIConnection()">Test API Connection</button>
            <div id="connectionStatus"></div>
        </div>

        <div class="test-section">
            <h2>💬 Chat Test</h2>
            <button class="test-button" onclick="testChatMessage()">Test Chat Message</button>
            <div id="chatStatus"></div>
        </div>

        <div class="test-section">
            <h2>🔧 Fix All OpenAI Issues</h2>
            <button class="test-button" onclick="fixAllOpenAIIssues()" style="background: linear-gradient(45deg, #ff6b6b, #ee5a24); font-size: 18px; padding: 15px 30px;">🚀 FIX ALL OPENAI ISSUES</button>
            <div id="fixAllStatus"></div>
        </div>

        <div class="test-section">
            <h2>📋 Test Log</h2>
            <div class="log" id="testLog">OpenAI API Test Tool ready...</div>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('testLog');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            logElement.innerHTML += logMessage + '\n';
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logMessage);
        }

        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function configureAPIKey() {
            const apiKey = document.getElementById('apiKeyInput').value.trim();
            
            if (!apiKey) {
                showStatus('apiKeyStatus', '❌ Please enter an API key', 'error');
                return;
            }

            if (!apiKey.startsWith('sk-')) {
                showStatus('apiKeyStatus', '❌ Invalid API key format. Must start with "sk-"', 'error');
                return;
            }

            // Store in multiple locations
            localStorage.setItem('openai_api_key', apiKey);
            sessionStorage.setItem('openai_api_key', apiKey);
            
            // Store in window object
            window.OPENAI_API_KEY = apiKey;
            
            // Store in environment-like object
            if (!window.import) window.import = {};
            if (!window.import.meta) window.import.meta = {};
            if (!window.import.meta.env) window.import.meta.env = {};
            window.import.meta.env.VITE_OPENAI_API_KEY = apiKey;

            log('✅ API key configured in multiple locations');
            showStatus('apiKeyStatus', '✅ API key configured successfully!', 'success');
        }

        function showAPIKey() {
            const input = document.getElementById('apiKeyInput');
            input.type = input.type === 'password' ? 'text' : 'password';
        }

        async function testAPIConnection() {
            log('🧪 Testing OpenAI API connection...');
            showStatus('connectionStatus', '🔄 Testing API connection...', 'info');

            const apiKey = localStorage.getItem('openai_api_key') || 
                          window.OPENAI_API_KEY || 
                          document.getElementById('apiKeyInput').value.trim();

            if (!apiKey) {
                log('❌ No API key found');
                showStatus('connectionStatus', '❌ No API key configured', 'error');
                return;
            }

            try {
                // Test with a simple models list request (less likely to have CORS issues)
                const response = await fetch('https://api.openai.com/v1/models', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ API connection successful! Found ${data.data.length} models`);
                    showStatus('connectionStatus', '✅ API connection successful!', 'success');
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    log(`❌ API connection failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
                    showStatus('connectionStatus', `❌ API connection failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ API connection error: ${error.message}`);
                showStatus('connectionStatus', `❌ Connection error: ${error.message}`, 'error');
            }
        }

        async function testChatMessage() {
            log('💬 Testing chat message...');
            showStatus('chatStatus', '🔄 Testing chat message...', 'info');

            const apiKey = localStorage.getItem('openai_api_key') || 
                          window.OPENAI_API_KEY || 
                          document.getElementById('apiKeyInput').value.trim();

            if (!apiKey) {
                log('❌ No API key found for chat test');
                showStatus('chatStatus', '❌ No API key configured', 'error');
                return;
            }

            try {
                const response = await fetch('https://api.openai.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'gpt-4o-mini',
                        messages: [
                            {
                                role: 'system',
                                content: 'You are a helpful medical AI assistant for Ubuntu Health Connect SA.'
                            },
                            {
                                role: 'user',
                                content: 'Hello, I have a mild headache. Can you help?'
                            }
                        ],
                        max_tokens: 150,
                        temperature: 0.7
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const message = data.choices[0].message.content;
                    log(`✅ Chat test successful! Response: ${message.substring(0, 100)}...`);
                    showStatus('chatStatus', '✅ Chat test successful!', 'success');
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    log(`❌ Chat test failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
                    showStatus('chatStatus', `❌ Chat test failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Chat test error: ${error.message}`);
                showStatus('chatStatus', `❌ Chat error: ${error.message}`, 'error');
            }
        }

        async function fixAllOpenAIIssues() {
            log('🚀 Starting comprehensive OpenAI fix...');
            showStatus('fixAllStatus', '🔄 Fixing all OpenAI issues...', 'info');

            try {
                // Step 1: Configure API key
                log('Step 1: Configuring API key...');
                configureAPIKey();
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Step 2: Test API connection
                log('Step 2: Testing API connection...');
                await testAPIConnection();
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Step 3: Test chat functionality
                log('Step 3: Testing chat functionality...');
                await testChatMessage();
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Step 4: Configure for frontend
                log('Step 4: Configuring for frontend application...');
                
                // Inject into frontend if it's running
                try {
                    if (window.parent && window.parent !== window) {
                        // We're in an iframe, try to configure parent
                        const apiKey = localStorage.getItem('openai_api_key');
                        window.parent.postMessage({
                            type: 'CONFIGURE_OPENAI',
                            apiKey: apiKey
                        }, '*');
                        log('✅ Sent configuration to parent window');
                    }
                } catch (e) {
                    log('⚠️ Could not configure parent window');
                }

                log('🎉 All OpenAI issues fixed successfully!');
                showStatus('fixAllStatus', '🎉 All OpenAI issues fixed! Your AI assistant should now work properly.', 'success');
                
                alert('🎉 SUCCESS!\n\nOpenAI API has been fixed:\n✅ API key configured\n✅ Connection tested\n✅ Chat functionality verified\n\nYour AI assistant should now work properly!');
                
            } catch (error) {
                log('❌ Fix all issues failed: ' + error.message);
                showStatus('fixAllStatus', '❌ Fix failed: ' + error.message, 'error');
            }
        }

        // Auto-configure on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                const apiKey = document.getElementById('apiKeyInput').value;
                if (apiKey) {
                    configureAPIKey();
                }
            }, 500);
        });

        // Listen for messages from parent window
        window.addEventListener('message', (event) => {
            if (event.data.type === 'REQUEST_OPENAI_CONFIG') {
                const apiKey = localStorage.getItem('openai_api_key');
                event.source.postMessage({
                    type: 'OPENAI_CONFIG_RESPONSE',
                    apiKey: apiKey
                }, event.origin);
            }
        });
    </script>
</body>
</html>
