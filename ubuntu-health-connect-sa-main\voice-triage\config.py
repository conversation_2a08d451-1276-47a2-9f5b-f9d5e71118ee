"""
Voice Triage System Configuration
Loads configuration from environment variables
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class VoiceTriageConfig:
    """Configuration class for Voice Triage System"""
    
    # Phone Numbers
    TRIAGE_PHONE_NUMBER = os.getenv('TRIAGE_PHONE_NUMBER', '+27727803582')
    USER_PHONE_NUMBER = os.getenv('USER_PHONE_NUMBER', '0727803582')
    TEST_CALLER_NUMBER = os.getenv('TEST_CALLER_NUMBER', '+27727803582')
    PRIMARY_CONTACT_NUMBER = os.getenv('PRIMARY_CONTACT_NUMBER', '27727803582')
    
    # API Configuration
    AFRICAS_TALKING_USERNAME = os.getenv('AFRICAS_TALKING_USERNAME', 'sandbox')
    AFRICAS_TALKING_API_KEY = os.getenv('AFRICAS_TALKING_API_KEY', '')
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
    GOOGLE_APPLICATION_CREDENTIALS = os.getenv('GOOGLE_APPLICATION_CREDENTIALS', '')
    
    # Notification Settings
    NURSE_NOTIFICATION_NUMBERS = [
        PRIMARY_CONTACT_NUMBER,
        "27987654321"  # Additional backup number
    ]
    EMERGENCY_ESCALATION_NUMBERS = [PRIMARY_CONTACT_NUMBER]
    
    # Flask Configuration
    FLASK_ENV = os.getenv('FLASK_ENV', 'development')
    PORT = int(os.getenv('PORT', 5000))
    SECRET_KEY = os.getenv('SECRET_KEY', 'ubuntu-health-voice-triage-secret')
    
    # Voice Triage Settings
    HIGH_RISK_THRESHOLD = int(os.getenv('HIGH_RISK_THRESHOLD', 7))
    CRITICAL_RISK_THRESHOLD = int(os.getenv('CRITICAL_RISK_THRESHOLD', 9))
    MAX_CALL_DURATION = int(os.getenv('MAX_CALL_DURATION', 1800))
    
    # Testing Configuration
    TESTING_MODE = os.getenv('TESTING_MODE', 'true').lower() == 'true'
    MOCK_SERVICES = os.getenv('MOCK_SERVICES', 'true').lower() == 'true'
    
    # Webhook Configuration
    WEBHOOK_BASE_URL = os.getenv('WEBHOOK_BASE_URL', 'https://your-domain.com')
    
    # Language Support
    SUPPORTED_LANGUAGES = [
        "en-ZA", "af-ZA", "zu-ZA", "xh-ZA", "st-ZA", 
        "tn-ZA", "ss-ZA", "ve-ZA", "ts-ZA", "nr-ZA", "nd-ZA"
    ]
    DEFAULT_LANGUAGE = "en-ZA"
    
    # Database Configuration
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = int(os.getenv('DB_PORT', 5432))
    DB_NAME = os.getenv('DB_NAME', 'ubuntu_health')
    DB_USER = os.getenv('DB_USER', 'postgres')
    DB_PASSWORD = os.getenv('DB_PASSWORD', '')
    
    @classmethod
    def get_formatted_phone_number(cls, format_type='display'):
        """Get phone number in different formats"""
        if format_type == 'display':
            # Format: ************
            number = cls.USER_PHONE_NUMBER
            if len(number) == 10:
                return f"{number[:3]} {number[3:6]} {number[6:]}"
            return number
        elif format_type == 'international':
            # Format: +27 72 780 3582
            number = cls.TRIAGE_PHONE_NUMBER
            if number.startswith('+27'):
                clean_number = number[3:]
                if len(clean_number) == 9:
                    return f"+27 {clean_number[:2]} {clean_number[2:5]} {clean_number[5:]}"
            return number
        elif format_type == 'tel_link':
            # Format for tel: links
            return cls.TRIAGE_PHONE_NUMBER
        else:
            return cls.TRIAGE_PHONE_NUMBER
    
    @classmethod
    def get_notification_numbers(cls):
        """Get list of notification numbers"""
        return cls.NURSE_NOTIFICATION_NUMBERS
    
    @classmethod
    def is_testing_mode(cls):
        """Check if system is in testing mode"""
        return cls.TESTING_MODE
    
    @classmethod
    def is_mock_services(cls):
        """Check if using mock services"""
        return cls.MOCK_SERVICES

# Create global config instance
config = VoiceTriageConfig()

# Export commonly used values
TRIAGE_PHONE_NUMBER = config.TRIAGE_PHONE_NUMBER
USER_PHONE_NUMBER = config.USER_PHONE_NUMBER
DISPLAY_PHONE_NUMBER = config.get_formatted_phone_number('display')
INTERNATIONAL_PHONE_NUMBER = config.get_formatted_phone_number('international')
TEL_LINK_NUMBER = config.get_formatted_phone_number('tel_link')
