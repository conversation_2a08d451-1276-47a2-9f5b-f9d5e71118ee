/**
 * API Services Index
 * Central export point for all API services
 */

// Export API client
export { apiClient, ApiError } from './client';
export type { ApiResponse, PaginatedResponse } from './client';

// Export patient services
export { patientApiService } from './patients';
export type { 
  Patient, 
  MedicalCondition, 
  AIInteraction, 
  CreatePatientRequest, 
  PatientSearchResponse 
} from './patients';

// Health check utility
export const healthCheck = async () => {
  try {
    const response = await apiClient.healthCheck();
    return response;
  } catch (error) {
    console.error('❌ Health check failed:', error);
    throw error;
  }
};

// Connection status utility
export const getConnectionStatus = () => {
  return apiClient.getConnectionStatus();
};

// Reconnect utility
export const reconnectToBackend = async () => {
  try {
    const connected = await apiClient.reconnect();
    if (connected) {
      console.log('✅ Reconnected to backend');
    } else {
      console.warn('⚠️ Failed to reconnect to backend');
    }
    return connected;
  } catch (error) {
    console.error('❌ Reconnection failed:', error);
    return false;
  }
};

// Make utilities available globally for debugging
(window as any).healthCheck = healthCheck;
(window as any).getConnectionStatus = getConnectionStatus;
(window as any).reconnectToBackend = reconnectToBackend;
