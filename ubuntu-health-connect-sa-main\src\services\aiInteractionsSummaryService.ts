// AI Interactions Summary Service
// Analyzes and summarizes AI interactions for healthcare providers

import { ChatInteraction } from './healthcareDatabase';

export interface AIInteractionSummary {
  patientId: string;
  totalInteractions: number;
  dateRange: {
    earliest: string;
    latest: string;
  };
  interactionTypes: {
    [key: string]: number;
  };
  severityDistribution: {
    [key: string]: number;
  };
  commonSymptoms: string[];
  triageDecisions: {
    [key: string]: number;
  };
  escalatedCases: number;
  averageDuration: string;
  summary: string;
}

class AIInteractionsSummaryService {
  /**
   * Generate comprehensive summary of AI interactions for a patient
   */
  generateSummary(interactions: ChatInteraction[]): AIInteractionSummary {
    if (interactions.length === 0) {
      return this.getEmptySummary();
    }

    const patientId = interactions[0].patientId;
    const sortedInteractions = interactions.sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    const summary: AIInteractionSummary = {
      patientId,
      totalInteractions: interactions.length,
      dateRange: {
        earliest: sortedInteractions[0].timestamp,
        latest: sortedInteractions[sortedInteractions.length - 1].timestamp
      },
      interactionTypes: this.analyzeInteractionTypes(interactions),
      severityDistribution: this.analyzeSeverityDistribution(interactions),
      commonSymptoms: this.extractCommonSymptoms(interactions),
      triageDecisions: this.analyzeTriageDecisions(interactions),
      escalatedCases: interactions.filter(i => i.status === 'escalated').length,
      averageDuration: this.calculateAverageDuration(interactions),
      summary: this.generateTextSummary(interactions)
    };

    return summary;
  }

  /**
   * Generate formatted text summary for healthcare providers
   */
  generateProviderReport(interactions: ChatInteraction[]): string {
    if (interactions.length === 0) {
      return 'No AI interactions recorded for this patient.';
    }

    const summary = this.generateSummary(interactions);
    const sortedInteractions = interactions.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    let report = `AI INTERACTIONS SUMMARY\n`;
    report += `========================\n\n`;
    
    report += `Patient ID: ${summary.patientId}\n`;
    report += `Total Interactions: ${summary.totalInteractions}\n`;
    report += `Date Range: ${this.formatDate(summary.dateRange.earliest)} to ${this.formatDate(summary.dateRange.latest)}\n`;
    report += `Escalated Cases: ${summary.escalatedCases}\n\n`;

    // Interaction Types
    report += `INTERACTION TYPES:\n`;
    Object.entries(summary.interactionTypes).forEach(([type, count]) => {
      report += `• ${type}: ${count}\n`;
    });
    report += '\n';

    // Severity Distribution
    report += `SEVERITY DISTRIBUTION:\n`;
    Object.entries(summary.severityDistribution).forEach(([severity, count]) => {
      report += `• ${severity}: ${count}\n`;
    });
    report += '\n';

    // Common Symptoms
    if (summary.commonSymptoms.length > 0) {
      report += `COMMON SYMPTOMS:\n`;
      summary.commonSymptoms.slice(0, 10).forEach(symptom => {
        report += `• ${symptom}\n`;
      });
      report += '\n';
    }

    // Triage Decisions
    report += `TRIAGE DECISIONS:\n`;
    Object.entries(summary.triageDecisions).forEach(([decision, count]) => {
      report += `• ${decision}: ${count}\n`;
    });
    report += '\n';

    // Recent Interactions
    report += `RECENT INTERACTIONS (Latest 5):\n`;
    report += `=====================================\n`;
    sortedInteractions.slice(0, 5).forEach((interaction, index) => {
      report += `${index + 1}. ${interaction.type} - ${this.formatDateTime(interaction.timestamp)}\n`;
      report += `   Status: ${interaction.status.toUpperCase()}\n`;
      report += `   Duration: ${interaction.duration || 'N/A'}\n`;
      report += `   Language: ${interaction.language}\n`;
      
      if (interaction.aiAssessment) {
        report += `   Severity: ${interaction.aiAssessment.severity}\n`;
        report += `   Symptoms: ${interaction.aiAssessment.symptoms.join(', ')}\n`;
        report += `   Triage Decision: ${interaction.aiAssessment.triageDecision}\n`;
        report += `   Recommendations: ${interaction.aiAssessment.recommendations.slice(0, 2).join(', ')}\n`;
      }
      
      report += `   Summary: ${interaction.summary}\n`;
      report += '\n';
    });

    // Overall Assessment
    report += `CLINICAL NOTES:\n`;
    report += `==============\n`;
    report += summary.summary;

    return report;
  }

  /**
   * Analyze interaction types distribution
   */
  private analyzeInteractionTypes(interactions: ChatInteraction[]): { [key: string]: number } {
    const types: { [key: string]: number } = {};
    
    interactions.forEach(interaction => {
      types[interaction.type] = (types[interaction.type] || 0) + 1;
    });
    
    return types;
  }

  /**
   * Analyze severity distribution
   */
  private analyzeSeverityDistribution(interactions: ChatInteraction[]): { [key: string]: number } {
    const severities: { [key: string]: number } = {};
    
    interactions.forEach(interaction => {
      if (interaction.aiAssessment?.severity) {
        const severity = interaction.aiAssessment.severity;
        severities[severity] = (severities[severity] || 0) + 1;
      }
    });
    
    return severities;
  }

  /**
   * Extract common symptoms across interactions
   */
  private extractCommonSymptoms(interactions: ChatInteraction[]): string[] {
    const symptomCounts: { [key: string]: number } = {};
    
    interactions.forEach(interaction => {
      if (interaction.aiAssessment?.symptoms) {
        interaction.aiAssessment.symptoms.forEach(symptom => {
          const normalizedSymptom = symptom.toLowerCase().trim();
          symptomCounts[normalizedSymptom] = (symptomCounts[normalizedSymptom] || 0) + 1;
        });
      }
    });
    
    // Sort by frequency and return top symptoms
    return Object.entries(symptomCounts)
      .sort(([, a], [, b]) => b - a)
      .map(([symptom]) => symptom);
  }

  /**
   * Analyze triage decisions distribution
   */
  private analyzeTriageDecisions(interactions: ChatInteraction[]): { [key: string]: number } {
    const decisions: { [key: string]: number } = {};
    
    interactions.forEach(interaction => {
      if (interaction.aiAssessment?.triageDecision) {
        const decision = interaction.aiAssessment.triageDecision;
        decisions[decision] = (decisions[decision] || 0) + 1;
      }
    });
    
    return decisions;
  }

  /**
   * Calculate average duration of interactions
   */
  private calculateAverageDuration(interactions: ChatInteraction[]): string {
    const durationsInMinutes: number[] = [];
    
    interactions.forEach(interaction => {
      if (interaction.duration) {
        const minutes = this.parseDurationToMinutes(interaction.duration);
        if (minutes > 0) {
          durationsInMinutes.push(minutes);
        }
      }
    });
    
    if (durationsInMinutes.length === 0) {
      return 'N/A';
    }
    
    const average = durationsInMinutes.reduce((sum, duration) => sum + duration, 0) / durationsInMinutes.length;
    return `${Math.round(average)} minutes`;
  }

  /**
   * Parse duration string to minutes
   */
  private parseDurationToMinutes(duration: string): number {
    const match = duration.match(/(\d+)\s*(min|minute|minutes|m)/i);
    if (match) {
      return parseInt(match[1]);
    }
    
    const secondsMatch = duration.match(/(\d+)\s*(sec|second|seconds|s)/i);
    if (secondsMatch) {
      return Math.round(parseInt(secondsMatch[1]) / 60);
    }
    
    return 0;
  }

  /**
   * Generate comprehensive text summary
   */
  private generateTextSummary(interactions: ChatInteraction[]): string {
    const summary = this.generateSummary(interactions);
    
    let text = `This patient has had ${summary.totalInteractions} AI interactions over `;
    text += `${this.calculateDaysBetween(summary.dateRange.earliest, summary.dateRange.latest)} days. `;
    
    // Most common interaction type
    const mostCommonType = Object.entries(summary.interactionTypes)
      .sort(([, a], [, b]) => b - a)[0];
    if (mostCommonType) {
      text += `Most interactions were ${mostCommonType[0]} (${mostCommonType[1]} times). `;
    }
    
    // Severity trends
    const severities = Object.entries(summary.severityDistribution);
    if (severities.length > 0) {
      const highSeverity = severities.filter(([severity]) => 
        severity === 'High' || severity === 'Critical'
      ).reduce((sum, [, count]) => sum + count, 0);
      
      if (highSeverity > 0) {
        text += `${highSeverity} interactions involved high or critical severity symptoms. `;
      }
    }
    
    // Common symptoms
    if (summary.commonSymptoms.length > 0) {
      text += `Most frequently reported symptoms include: ${summary.commonSymptoms.slice(0, 3).join(', ')}. `;
    }
    
    // Escalations
    if (summary.escalatedCases > 0) {
      text += `${summary.escalatedCases} cases required escalation to healthcare providers. `;
    }
    
    // Triage patterns
    const urgentCare = summary.triageDecisions['Urgent Care'] || 0;
    const emergency = summary.triageDecisions['Emergency'] || 0;
    if (urgentCare + emergency > 0) {
      text += `${urgentCare + emergency} interactions resulted in urgent care or emergency recommendations. `;
    }
    
    text += `Average interaction duration: ${summary.averageDuration}.`;
    
    return text;
  }

  /**
   * Calculate days between two dates
   */
  private calculateDaysBetween(startDate: string, endDate: string): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }

  /**
   * Format date for display
   */
  private formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  /**
   * Format date and time for display
   */
  private formatDateTime(dateString: string): string {
    return new Date(dateString).toLocaleString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Get empty summary for patients with no interactions
   */
  private getEmptySummary(): AIInteractionSummary {
    return {
      patientId: '',
      totalInteractions: 0,
      dateRange: {
        earliest: '',
        latest: ''
      },
      interactionTypes: {},
      severityDistribution: {},
      commonSymptoms: [],
      triageDecisions: {},
      escalatedCases: 0,
      averageDuration: 'N/A',
      summary: 'No AI interactions recorded for this patient.'
    };
  }
}

export const aiInteractionsSummaryService = new AIInteractionsSummaryService();
export default aiInteractionsSummaryService;
