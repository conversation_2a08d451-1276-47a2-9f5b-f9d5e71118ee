#!/usr/bin/env python3
"""
Ubuntu Health Connect SA - Centralized Database Configuration
Ensures all components use the same Ubuntu Health database
"""

import os
from pathlib import Path

# Get the project root directory
PROJECT_ROOT = Path(__file__).parent.absolute()

# Centralized database configuration
DATABASE_CONFIG = {
    # Main Ubuntu Health database path
    'UBUNTU_HEALTH_DB_PATH': PROJECT_ROOT / 'database' / 'ubuntu_health.db',
    
    # Alternative paths for different components
    'BACKEND_DB_PATH': PROJECT_ROOT / 'backend' / 'database' / 'ubuntu_health.db',
    'VOICE_AGENT_DB_PATH': PROJECT_ROOT / 'Health Agent Voice' / 'database' / 'ubuntu_health.db',
    'VOICE_TRIAGE_DB_PATH': PROJECT_ROOT / 'voice-triage' / 'database' / 'ubuntu_health.db',
    
    # Database connection settings
    'CONNECTION_TIMEOUT': 30,
    'MAX_RETRIES': 3,
    'BACKUP_ENABLED': True,
    'BACKUP_INTERVAL_HOURS': 24,
}

def get_primary_database_path():
    """Get the primary Ubuntu Health database path"""
    return str(DATABASE_CONFIG['UBUNTU_HEALTH_DB_PATH'])

def get_database_path_for_component(component_name):
    """Get database path for specific component"""
    component_paths = {
        'main': DATABASE_CONFIG['UBUNTU_HEALTH_DB_PATH'],
        'backend': DATABASE_CONFIG['BACKEND_DB_PATH'],
        'voice_agent': DATABASE_CONFIG['VOICE_AGENT_DB_PATH'],
        'voice_triage': DATABASE_CONFIG['VOICE_TRIAGE_DB_PATH'],
    }
    
    return str(component_paths.get(component_name, DATABASE_CONFIG['UBUNTU_HEALTH_DB_PATH']))

def ensure_database_directories():
    """Ensure all database directories exist"""
    for key, path in DATABASE_CONFIG.items():
        if key.endswith('_PATH'):
            path = Path(path)
            path.parent.mkdir(parents=True, exist_ok=True)
            print(f"✅ Ensured directory exists: {path.parent}")

def get_database_info():
    """Get comprehensive database information"""
    primary_db = DATABASE_CONFIG['UBUNTU_HEALTH_DB_PATH']
    
    info = {
        'primary_database': str(primary_db),
        'exists': primary_db.exists(),
        'size_mb': round(primary_db.stat().st_size / (1024 * 1024), 2) if primary_db.exists() else 0,
        'directory': str(primary_db.parent),
        'all_paths': {k: str(v) for k, v in DATABASE_CONFIG.items() if k.endswith('_PATH')}
    }
    
    return info

def sync_database_files():
    """Sync database files to ensure all components use the same data"""
    primary_db = DATABASE_CONFIG['UBUNTU_HEALTH_DB_PATH']
    
    if not primary_db.exists():
        print(f"⚠️ Primary database not found at {primary_db}")
        return False
    
    # Copy primary database to all component locations
    import shutil
    
    for key, path in DATABASE_CONFIG.items():
        if key.endswith('_PATH') and key != 'UBUNTU_HEALTH_DB_PATH':
            target_path = Path(path)
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            try:
                shutil.copy2(primary_db, target_path)
                print(f"✅ Synced database to: {target_path}")
            except Exception as e:
                print(f"❌ Failed to sync to {target_path}: {e}")
    
    return True

def create_database_symlinks():
    """Create symbolic links to ensure all components use the same database file"""
    primary_db = DATABASE_CONFIG['UBUNTU_HEALTH_DB_PATH']
    
    if not primary_db.exists():
        print(f"⚠️ Primary database not found at {primary_db}")
        return False
    
    for key, path in DATABASE_CONFIG.items():
        if key.endswith('_PATH') and key != 'UBUNTU_HEALTH_DB_PATH':
            target_path = Path(path)
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Remove existing file if it exists
            if target_path.exists():
                target_path.unlink()
            
            try:
                # Create symbolic link (Windows may require admin privileges)
                target_path.symlink_to(primary_db.resolve())
                print(f"✅ Created symlink: {target_path} -> {primary_db}")
            except OSError:
                # Fallback to copying if symlink fails
                import shutil
                shutil.copy2(primary_db, target_path)
                print(f"✅ Copied database to: {target_path} (symlink failed)")
    
    return True

if __name__ == '__main__':
    print("🏥 Ubuntu Health Connect SA - Database Configuration")
    print("=" * 60)
    
    # Ensure directories exist
    ensure_database_directories()
    
    # Show database info
    info = get_database_info()
    print(f"📊 Database Information:")
    print(f"   Primary DB: {info['primary_database']}")
    print(f"   Exists: {info['exists']}")
    print(f"   Size: {info['size_mb']} MB")
    print(f"   Directory: {info['directory']}")
    
    # Sync databases
    print("\n🔄 Syncing database files...")
    if sync_database_files():
        print("✅ Database sync completed")
    else:
        print("❌ Database sync failed")
    
    print("=" * 60)
    print("✅ Database configuration complete")
