#!/usr/bin/env python3
"""
Phone Number Configuration Test
Verifies that your phone number (**********) is properly configured throughout the system
"""

import os
import sys
import requests
import json
from datetime import datetime

# Add voice-triage to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from config import config, TRIAGE_PHONE_NUMBER, USER_PHONE_NUMBER
    config_loaded = True
except ImportError:
    config_loaded = False

def test_environment_configuration():
    """Test environment configuration for phone numbers"""
    print("📱 Testing Environment Configuration")
    print("-" * 50)
    
    # Load .env file manually
    env_file = os.path.join(os.path.dirname(__file__), '.env')
    env_vars = {}
    
    if os.path.exists(env_file):
        print("✅ .env file found")
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
    else:
        print("❌ .env file not found")
        return False
    
    # Check phone number configuration
    expected_numbers = {
        'TRIAGE_PHONE_NUMBER': '+27727803582',
        'USER_PHONE_NUMBER': '**********',
        'TEST_CALLER_NUMBER': '+27727803582',
        'PRIMARY_CONTACT_NUMBER': '27727803582'
    }
    
    all_correct = True
    
    for key, expected in expected_numbers.items():
        actual = env_vars.get(key, 'NOT_FOUND')
        if expected in actual:
            print(f"✅ {key}: {actual}")
        else:
            print(f"❌ {key}: {actual} (expected: {expected})")
            all_correct = False
    
    return all_correct

def test_config_module():
    """Test the config module"""
    print("\n⚙️ Testing Config Module")
    print("-" * 50)
    
    if not config_loaded:
        print("❌ Config module not loaded")
        return False
    
    print(f"✅ Config module loaded successfully")
    print(f"   Triage Number: {TRIAGE_PHONE_NUMBER}")
    print(f"   User Number: {USER_PHONE_NUMBER}")
    print(f"   Display Format: {config.get_formatted_phone_number('display')}")
    print(f"   International Format: {config.get_formatted_phone_number('international')}")
    print(f"   Tel Link Format: {config.get_formatted_phone_number('tel_link')}")
    
    # Verify your number is in the configuration
    expected_number = '727803582'
    
    checks = [
        ('Triage Number', expected_number in TRIAGE_PHONE_NUMBER),
        ('User Number', expected_number in USER_PHONE_NUMBER),
        ('Notification Numbers', any(expected_number in str(num) for num in config.get_notification_numbers()))
    ]
    
    all_passed = True
    for check_name, passed in checks:
        print(f"   {'✅' if passed else '❌'} {check_name}: {'Configured' if passed else 'Missing'}")
        if not passed:
            all_passed = False
    
    return all_passed

def test_backend_api_configuration():
    """Test backend API with your phone number"""
    print("\n🔗 Testing Backend API Configuration")
    print("-" * 50)
    
    base_url = 'http://localhost:5000'
    
    # Test 1: Health check
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend API is running")
        else:
            print(f"❌ Backend API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to backend API: {e}")
        return False
    
    # Test 2: Mock call with your number
    try:
        call_data = {
            'phone_number': '+27727803582'
        }
        
        response = requests.post(
            f"{base_url}/api/voice-triage/mock-call",
            json=call_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Mock call test successful")
            print(f"   Call ID: {data.get('call_id', 'unknown')}")
            print(f"   Phone Number: {data.get('phone_number', 'unknown')}")
            
            # Verify your number is in the response
            if '727803582' in str(data):
                print("✅ Your phone number correctly processed")
                return True
            else:
                print("⚠️ Your phone number not found in response")
                return False
        else:
            print(f"❌ Mock call test failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Mock call test error: {e}")
        return False

def test_assessment_with_your_number():
    """Test AI assessment with your phone number as patient ID"""
    print("\n🧠 Testing AI Assessment with Your Number")
    print("-" * 50)
    
    base_url = 'http://localhost:5000'
    
    try:
        assessment_data = {
            'symptoms': ['chest pain', 'difficulty breathing'],
            'patient_id': 'patient-727803582'  # Using your number as patient ID
        }
        
        response = requests.post(
            f"{base_url}/api/voice-triage/mock-assessment",
            json=assessment_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            assessment = data.get('assessment', {})
            
            print(f"✅ Assessment successful")
            print(f"   Patient ID: {data.get('patient_id', 'unknown')}")
            print(f"   Risk Level: {assessment.get('risk_level', 'unknown')}")
            print(f"   Urgency Score: {assessment.get('urgency_score', 0)}/10")
            print(f"   Care Level: {assessment.get('recommended_care_level', 'unknown')}")
            
            # For critical symptoms, should be critical risk
            if assessment.get('risk_level') == 'critical':
                print("✅ Critical symptoms correctly identified")
                print("🚨 This would trigger notifications to your number")
                return True
            else:
                print(f"⚠️ Expected critical risk, got {assessment.get('risk_level')}")
                return False
        else:
            print(f"❌ Assessment failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Assessment error: {e}")
        return False

def test_frontend_integration():
    """Test frontend integration"""
    print("\n🌐 Testing Frontend Integration")
    print("-" * 50)
    
    frontend_url = 'http://localhost:8082'
    
    try:
        response = requests.get(frontend_url, timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is running")
            
            # Check if your number appears in the HTML
            html_content = response.text
            if '************' in html_content or '727803582' in html_content:
                print("✅ Your phone number found in frontend")
                return True
            else:
                print("⚠️ Your phone number not found in frontend HTML")
                print("   (This is normal - number is loaded by React component)")
                return True
        else:
            print(f"❌ Frontend not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Frontend connection error: {e}")
        print("   Make sure frontend is running: npm run dev")
        return False

def run_phone_configuration_tests():
    """Run all phone configuration tests"""
    print("📱 Phone Number Configuration Test Suite")
    print("🌍 Ubuntu Philosophy: 'I am because we are'")
    print("=" * 70)
    print(f"📅 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📞 Testing phone number: ********** / +27727803582")
    print("=" * 70)
    
    tests = [
        ("Environment Configuration", test_environment_configuration),
        ("Config Module", test_config_module),
        ("Backend API Configuration", test_backend_api_configuration),
        ("AI Assessment with Your Number", test_assessment_with_your_number),
        ("Frontend Integration", test_frontend_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 PHONE CONFIGURATION TEST RESULTS")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    success_rate = (passed / total) * 100
    print(f"\n📈 Overall Success Rate: {success_rate:.1f}% ({passed}/{total})")
    
    if success_rate == 100:
        print("🎉 All tests passed! Your phone number is properly configured.")
        print("📞 You can now:")
        print("   1. Call ************ for voice triage (when real APIs are connected)")
        print("   2. Use the Voice Triage button in the patient portal")
        print("   3. Receive notifications for urgent cases")
    elif success_rate >= 80:
        print("✅ Most tests passed! Minor configuration issues detected.")
    else:
        print("⚠️ Several configuration issues detected. Please review the failed tests.")
    
    print("\n📱 Your Phone Number Configuration:")
    print(f"   Display Format: ************")
    print(f"   International: +27 72 780 3582")
    print(f"   Tel Link: +27727803582")
    print(f"   SMS Format: 27727803582")
    
    print("=" * 70)
    
    return results

if __name__ == '__main__':
    try:
        results = run_phone_configuration_tests()
        
        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"phone_config_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'phone_number': '**********',
                'international_format': '+27727803582',
                'test_results': [{'test': name, 'passed': result} for name, result in results],
                'success_rate': (sum(1 for _, result in results if result) / len(results)) * 100
            }, f, indent=2)
        
        print(f"📄 Test results saved to: {filename}")
        
    except KeyboardInterrupt:
        print("\n🛑 Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Testing failed: {e}")
