# Ubuntu Health Connect SA - Project Framework Structure

## 🏗️ **Complete Project Structure**

```
ubuntu-health-connect-sa-main/
├── 📁 ROOT CONFIGURATION FILES
│   ├── package.json                    # Node.js dependencies & scripts
│   ├── package-lock.json               # Dependency lock file
│   ├── vite.config.ts                  # Vite build configuration
│   ├── tsconfig.json                   # TypeScript configuration
│   ├── tsconfig.app.json               # App-specific TypeScript config
│   ├── tsconfig.node.json              # Node.js TypeScript config
│   ├── tailwind.config.ts              # Tailwind CSS configuration
│   ├── postcss.config.js               # PostCSS configuration
│   ├── eslint.config.js                # ESLint configuration
│   ├── components.json                 # Shadcn/ui components config
│   ├── index.html                      # Main HTML entry point
│   └── .env                           # Environment variables
│
├── 📁 FRONTEND SOURCE CODE (src/)
│   ├── 📄 main.tsx                     # React application entry point
│   ├── 📄 App.tsx                      # Main App component
│   ├── 📄 App.css                      # App-specific styles
│   ├── 📄 index.css                    # Global styles
│   ├── 📄 vite-env.d.ts               # Vite environment types
│   │
│   ├── 📁 components/                  # React Components
│   │   ├── 🔧 CORE COMPONENTS
│   │   │   ├── SymptomChecker.tsx      # AI Health Assistant
│   │   │   ├── PatientRegistration.tsx # Patient Registration Form
│   │   │   ├── PatientDetailModal.tsx  # Patient Details View
│   │   │   ├── MonitoringDashboard.tsx # Real-time Monitoring
│   │   │   ├── LanguageSelector.tsx    # Multi-language Support
│   │   │   └── ErrorBoundary.tsx       # Error Handling
│   │   │
│   │   ├── 🎨 UI COMPONENTS
│   │   │   ├── ColorfulDropletsBackground.tsx
│   │   │   ├── SouthAfricanFlagBackground.tsx
│   │   │   ├── LoadingSpinner.tsx
│   │   │   └── HealthcareStats.tsx
│   │   │
│   │   ├── 📞 COMMUNICATION COMPONENTS
│   │   │   ├── WhatsAppCommunicationBoard.tsx
│   │   │   ├── VoiceTriageButton.tsx
│   │   │   ├── VoiceTriageHistory.tsx
│   │   │   └── CommunicationTest.tsx
│   │   │
│   │   ├── 🤖 MONITORING COMPONENTS
│   │   │   ├── MonitoringChatInterface.tsx
│   │   │   ├── MonitoringChatbotDemo.tsx
│   │   │   ├── MedicalDetailsPanel.tsx
│   │   │   └── PatientSearchFilters.tsx
│   │   │
│   │   ├── 🧪 TEST COMPONENTS
│   │   │   ├── ChatInteractionTest.tsx
│   │   │   ├── OpenAITest.tsx
│   │   │   └── TestPatientCaseFlow.tsx
│   │   │
│   │   └── 📁 ui/                      # Shadcn/ui Components (60+ components)
│   │       ├── button.tsx              # Button component
│   │       ├── card.tsx                # Card component
│   │       ├── dialog.tsx              # Dialog component
│   │       ├── form.tsx                # Form components
│   │       ├── input.tsx               # Input component
│   │       ├── table.tsx               # Table component
│   │       ├── tabs.tsx                # Tabs component
│   │       ├── toast.tsx               # Toast notifications
│   │       ├── chart.tsx               # Chart components
│   │       └── ... (50+ more UI components)
│   │
│   ├── 📁 pages/                       # Page Components
│   │   ├── Index.tsx                   # Landing page
│   │   ├── PatientLogin.tsx            # Patient login page
│   │   ├── PatientDashboard.tsx        # Patient dashboard
│   │   ├── ProviderLogin.tsx           # Provider login page
│   │   ├── ProviderDashboard.tsx       # Provider dashboard
│   │   ├── HealthStatistics.tsx        # Health analytics page
│   │   ├── HealthStatisticsSimple.tsx  # Simplified analytics
│   │   ├── TestPage.tsx                # Testing page
│   │   └── NotFound.tsx                # 404 error page
│   │
│   ├── 📁 services/                    # Business Logic Services
│   │   ├── 🤖 AI SERVICES
│   │   │   ├── openaiService.ts        # OpenAI integration
│   │   │   ├── medicalReportService.ts # Medical report generation
│   │   │   ├── intelligentMonitoringAgent.ts # AI monitoring
│   │   │   └── aiInteractionsSummaryService.ts
│   │   │
│   │   ├── 👥 PATIENT SERVICES
│   │   │   ├── patientRegistrationService.ts # Patient registration
│   │   │   ├── patientCaseService.ts   # Case management
│   │   │   ├── patientApiService.ts    # Patient API calls
│   │   │   └── authDatabaseService.ts  # Authentication
│   │   │
│   │   ├── 🏥 HEALTHCARE SERVICES
│   │   │   ├── healthcareDatabase.ts   # Healthcare data
│   │   │   ├── healthcareMonitoringService.ts # Provider monitoring
│   │   │   ├── chatInteractionService.ts # Chat logging
│   │   │   └── monitoringChatbotService.ts
│   │   │
│   │   ├── 📞 COMMUNICATION SERVICES
│   │   │   ├── whatsappApi.ts          # WhatsApp integration
│   │   │   ├── voiceTriageIntegration.ts # Voice triage
│   │   │   ├── voiceTriageWebhook.ts   # Voice webhooks
│   │   │   └── enhancedIntegration.ts  # Enhanced integrations
│   │   │
│   │   └── 📁 api/                     # API Client Services
│   │       ├── apiClient.ts            # Base API client
│   │       └── patientService.ts       # Patient API service
│   │
│   ├── 📁 hooks/                       # Custom React Hooks
│   │   ├── useAuth.tsx                 # Authentication hook
│   │   ├── use-toast.ts                # Toast notifications hook
│   │   └── use-mobile.tsx              # Mobile detection hook
│   │
│   ├── 📁 types/                       # TypeScript Type Definitions
│   │   └── medical.ts                  # Medical data types
│   │
│   ├── 📁 utils/                       # Utility Functions
│   │   ├── translations.ts             # Multi-language translations
│   │   ├── envValidation.ts            # Environment validation
│   │   └── performance.ts              # Performance utilities
│   │
│   ├── 📁 config/                      # Configuration Files
│   │   ├── environment.ts              # Environment configuration
│   │   └── simple-config.ts            # Simple configuration
│   │
│   ├── 📁 lib/                         # Library Functions
│   │   └── utils.ts                    # General utilities
│   │
│   └── 📁 styles/                      # Styling Files
│       └── ubuntu-theme.css            # Ubuntu theme styles
│
├── 📁 BACKEND (backend/)
│   ├── 📄 app.py                       # Main Flask application
│   ├── 📄 requirements.txt             # Python dependencies
│   │
│   ├── 📁 app/                         # Flask Application Structure
│   │   ├── 📄 __init__.py              # App initialization
│   │   ├── 📄 config.py                # Flask configuration
│   │   │
│   │   ├── 📁 api/                     # API Endpoints
│   │   │   ├── __init__.py
│   │   │   ├── health.py               # Health check endpoint
│   │   │   ├── patients.py             # Patient API endpoints
│   │   │   └── ai_interactions.py      # AI interaction endpoints
│   │   │
│   │   ├── 📁 services/                # Business Logic Services
│   │   │   ├── __init__.py
│   │   │   ├── ai_service.py           # AI service logic
│   │   │   └── patient_service.py      # Patient service logic
│   │   │
│   │   ├── 📁 database/                # Database Management
│   │   │   ├── __init__.py
│   │   │   ├── db_manager.py           # Database operations
│   │   │   └── schema.sql              # Database schema
│   │   │
│   │   └── 📁 utils/                   # Utility Functions
│   │       ├── __init__.py
│   │       ├── responses.py            # API response formatting
│   │       └── validators.py           # Input validation
│   │
│   ├── 📁 database/                    # Database Files
│   │   └── ubuntu_health.db            # SQLite database
│   │
│   └── 📁 logs/                        # Log Files
│       └── (application logs)
│
├── 📁 VOICE TRIAGE SYSTEM (voice-triage/)
│   ├── 📄 config.py                    # Voice triage configuration
│   ├── 📄 requirements.txt             # Python dependencies
│   ├── 📄 mock_voice_triage.py         # Mock voice triage
│   ├── 📄 run_tests.py                 # Test runner
│   │
│   ├── 📁 api/                         # Voice API endpoints
│   ├── 📁 services/                    # Voice services
│   ├── 📁 database/                    # Voice database
│   │
│   └── 📁 TEST FILES
│       ├── test_africas_talking_integration.py
│       ├── test_ai_assessment.py
│       ├── test_integration.py
│       ├── test_phone_configuration.py
│       └── (various test result JSON files)
│
├── 📁 HEALTH AGENT VOICE (Health Agent Voice/)
│   ├── 📄 app.py                       # Main voice agent app
│   ├── 📄 app_enhanced.py              # Enhanced voice agent
│   ├── 📄 app_simple.py                # Simple voice agent
│   ├── 📄 backend_minimal.py           # Minimal backend
│   ├── 📄 respondio_backend.py         # Respondio integration
│   ├── 📄 requirements.txt             # Python dependencies
│   │
│   ├── 📁 config/                      # Voice agent configuration
│   ├── 📁 database/                    # Voice agent database
│   ├── 📁 templates/                   # HTML templates
│   ├── 📁 logs/                        # Voice agent logs
│   │
│   └── 📁 STARTUP SCRIPTS
│       ├── start_all.py
│       ├── start_backend_api.py
│       ├── start_respondio_backend.py
│       ├── start_simple.py
│       ├── start_tunnel.py
│       └── start_with_localtunnel.py
│
├── 📁 DATABASE (database/)
│   ├── ubuntu_health.db                # Main SQLite database
│   ├── ubuntu_health.db-shm            # Shared memory file
│   └── ubuntu_health.db-wal            # Write-ahead log
│
├── 📁 SHARED RESOURCES (shared/)
│   └── 📁 types/                       # Shared type definitions
│
├── 📁 SCRIPTS (scripts/)
│   ├── env-check.js                    # Environment check script
│   ├── env-check.cjs                   # CommonJS env check
│   ├── health-check.js                 # Health check script
│   └── health-check.cjs                # CommonJS health check
│
├── 📁 PUBLIC ASSETS (public/)
│   ├── favicon.ico                     # Website favicon
│   ├── placeholder.svg                 # Placeholder image
│   └── robots.txt                      # SEO robots file
│
├── 📁 BUILD OUTPUT (dist/)
│   ├── index.html                      # Built HTML
│   ├── favicon.ico                     # Built favicon
│   └── 📁 assets/                      # Built CSS/JS assets
│
├── 📁 NODE MODULES (node_modules/)
│   └── (60+ npm packages for React, TypeScript, Tailwind, etc.)
│
├── 📁 DOCUMENTATION
│   ├── README.md                       # Project overview
│   ├── SYSTEM_ARCHITECTURE.md          # System architecture
│   ├── ARCHITECTURE_SUMMARY.md         # Architecture summary
│   ├── COMPREHENSIVE_PROJECT_ANALYSIS.md
│   ├── PROVIDER_DASHBOARD_IMPROVEMENTS.md
│   ├── DOCUMENTATION_INDEX.md          # Documentation index
│   └── (20+ other documentation files)
│
├── 📁 TEST FILES
│   ├── test-urgent-case-flow.html
│   ├── test-monitoring-workflow.html
│   ├── test_api_connection.html
│   ├── test_chat_in_browser.html
│   └── (10+ other test files)
│
├── 📁 STARTUP SCRIPTS
│   ├── start-dev.bat                   # Windows development startup
│   ├── start-dev.cmd                   # Windows command startup
│   ├── start_ubuntu_health.py          # Python startup script
│   └── start_ubuntu_health_final.py    # Final startup script
│
└── 📁 CONFIGURATION FILES
    ├── patients_db.json                # Patient database JSON
    ├── database_config.py              # Database configuration
    ├── simple_auth_server.cjs          # Simple auth server
    ├── backend_working.py              # Working backend script
    └── healthcare-sa-f2b2dcb8b99d.json # Google Cloud credentials

## 🛠️ **Framework Technology Stack**

### **Frontend Framework**
```json
{
  "framework": "React 18.3.1",
  "language": "TypeScript 5.6.2",
  "build_tool": "Vite 5.4.2",
  "package_manager": "npm",
  "styling": {
    "css_framework": "Tailwind CSS 3.4.1",
    "ui_library": "Radix UI",
    "components": "Shadcn/ui",
    "animations": "tailwindcss-animate"
  },
  "routing": "React Router DOM 6.26.1",
  "forms": "React Hook Form 7.53.0",
  "validation": "Zod",
  "charts": "Recharts 2.12.7",
  "http_client": "Axios 1.7.7"
}
```

### **Backend Framework**
```json
{
  "framework": "Flask 3.0.3",
  "language": "Python 3.11+",
  "database": "SQLite 3 (development)",
  "orm": "Custom SQL queries",
  "cors": "Flask-CORS",
  "api_style": "RESTful",
  "authentication": "Custom JWT-like system"
}
```

### **AI & External Services**
```json
{
  "ai_service": "OpenAI GPT-4",
  "voice_service": "Africa's Talking API",
  "messaging": "WhatsApp Business API",
  "chat_platform": "Respondio",
  "speech_processing": "Custom Python services"
}
```

### **Development Tools**
```json
{
  "linting": "ESLint 9.9.1",
  "formatting": "Prettier (via ESLint)",
  "type_checking": "TypeScript",
  "bundling": "Vite + Rollup",
  "css_processing": "PostCSS + Autoprefixer",
  "testing": "Vitest (configured)"
}
```

## 📊 **Component Architecture**

### **Frontend Component Hierarchy**
```
App.tsx
├── Router
│   ├── Index (Landing Page)
│   ├── PatientLogin
│   │   └── PatientRegistration
│   ├── PatientDashboard
│   │   ├── SymptomChecker
│   │   ├── LanguageSelector
│   │   └── HealthcareStats
│   ├── ProviderLogin
│   └── ProviderDashboard
│       ├── MonitoringDashboard
│       ├── PatientDetailModal
│       ├── PatientSearchFilters
│       └── WhatsAppCommunicationBoard
```

### **Service Layer Architecture**
```
Services Layer
├── AI Services
│   ├── openaiService.ts
│   ├── medicalReportService.ts
│   └── intelligentMonitoringAgent.ts
├── Patient Services
│   ├── patientRegistrationService.ts
│   ├── patientCaseService.ts
│   └── authDatabaseService.ts
├── Healthcare Services
│   ├── healthcareDatabase.ts
│   ├── healthcareMonitoringService.ts
│   └── chatInteractionService.ts
└── Communication Services
    ├── whatsappApi.ts
    ├── voiceTriageIntegration.ts
    └── enhancedIntegration.ts
```

### **Backend API Structure**
```
Flask Backend
├── /health (Health check)
├── /api/patients (Patient management)
├── /api/ai-interactions (AI chat logging)
├── /api/openai/chat (OpenAI proxy)
└── Custom endpoints for voice triage
```

## 🗄️ **Database Schema**

### **Main Tables**
```sql
-- Patient information
patients (
  id, first_name, last_name, id_number,
  phone_number, email, date_of_birth,
  gender, province, city, medical_history
)

-- AI chat interactions
ai_interactions (
  id, patient_id, conversation_data,
  medical_report, assessment_summary,
  created_at, updated_at
)

-- Patient cases for providers
patient_cases (
  id, patient_id, case_data, status,
  urgency_level, provider_notes,
  created_at, updated_at
)
```

## 🚀 **Deployment Structure**

### **Development Environment**
```bash
# Frontend Development Server
npm run dev                 # Vite dev server on port 5173

# Backend Development Server
python backend/app.py       # Flask server on port 5000

# Voice Services
python voice-triage/mock_voice_triage.py
python "Health Agent Voice/app.py"
```

### **Production Deployment**
```bash
# Frontend (Static Files)
npm run build              # Builds to dist/ folder
# Serve with Nginx or Apache

# Backend (Python WSGI)
gunicorn -w 4 -b 0.0.0.0:5000 backend.app:app

# Database
# Migrate from SQLite to PostgreSQL/MySQL
# Set up database backups and replication
```

## 📦 **Key Dependencies**

### **Frontend Dependencies (package.json)**
```json
{
  "react": "^18.3.1",
  "react-dom": "^18.3.1",
  "typescript": "^5.6.2",
  "vite": "^5.4.2",
  "@radix-ui/react-*": "Various UI components",
  "tailwindcss": "^3.4.1",
  "react-router-dom": "^6.26.1",
  "react-hook-form": "^7.53.0",
  "axios": "^1.7.7",
  "recharts": "^2.12.7",
  "lucide-react": "^0.441.0"
}
```

### **Backend Dependencies (requirements.txt)**
```python
Flask==3.0.3
Flask-CORS==4.0.1
openai==1.51.0
requests==2.32.3
python-dotenv==1.0.1
sqlite3 (built-in)
```

## 🔧 **Configuration Management**

### **Environment Variables**
```bash
# Frontend (.env)
VITE_API_BASE_URL=http://localhost:5000
VITE_OPENAI_API_KEY=your_openai_key
VITE_ENVIRONMENT=development

# Backend
OPENAI_API_KEY=your_openai_key
FLASK_ENV=development
DATABASE_URL=sqlite:///database/ubuntu_health.db
```

### **Build Configuration**
- **Vite Config**: Modern build tool with HMR
- **TypeScript Config**: Strict type checking
- **Tailwind Config**: Custom Ubuntu theme
- **ESLint Config**: Code quality enforcement

---

## 🎯 **Framework Benefits**

### **Scalability**
- Modular component architecture
- Service-oriented design
- Microservices-ready structure
- Database abstraction layer

### **Maintainability**
- TypeScript for type safety
- Clear separation of concerns
- Comprehensive documentation
- Standardized code structure

### **Performance**
- Vite for fast development builds
- Code splitting and lazy loading
- Optimized bundle sizes
- Efficient database queries

### **Developer Experience**
- Hot module replacement
- Type checking and IntelliSense
- Comprehensive error handling
- Extensive testing utilities

---

*This framework structure supports a world-class healthcare platform with enterprise-grade architecture, modern development practices, and production-ready deployment capabilities.*
```
