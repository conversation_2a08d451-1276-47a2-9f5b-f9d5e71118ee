"""
Patient API endpoints for Ubuntu Health Connect SA
"""

from flask import Blueprint, request, jsonify, current_app
from app.api import bp
from app.database import get_db
from app.services.patient_service import PatientService
from app.utils.validators import validate_patient_data
from app.utils.responses import success_response, error_response
import uuid

@bp.route('/patients', methods=['POST'])
def create_patient():
    """
    Create a new patient record
    """
    try:
        data = request.get_json()
        
        # Validate input data
        validation_result = validate_patient_data(data)
        if not validation_result['valid']:
            return error_response(validation_result['errors'], 400)
        
        # Use patient service
        patient_service = PatientService(get_db())
        result = patient_service.create_or_update_patient(data)
        
        if result['success']:
            current_app.logger.info(f"✅ Patient {result['action']}: {result['patient_id']}")
            return success_response(result, 201 if result['action'] == 'created' else 200)
        else:
            return error_response(result['error'], 500)
        
    except Exception as e:
        current_app.logger.error(f"❌ Error in create_patient: {str(e)}")
        return error_response(str(e), 500)

@bp.route('/patients/<id_number>', methods=['GET'])
def get_patient(id_number):
    """
    Get patient by SA ID number
    """
    try:
        current_app.logger.info(f"🔍 Looking up patient by ID: {id_number}")
        
        patient_service = PatientService(get_db())
        patient_data = patient_service.get_patient_by_id_number(id_number)
        
        if patient_data:
            current_app.logger.info(f"✅ Patient found: {patient_data['id']}")
            return success_response(patient_data)
        else:
            current_app.logger.info(f"❌ Patient not found: {id_number}")
            return error_response('Patient not found', 404)
        
    except Exception as e:
        current_app.logger.error(f"❌ Error getting patient: {str(e)}")
        return error_response(str(e), 500)

@bp.route('/patients', methods=['GET'])
def get_all_patients():
    """
    Get all patients with optional search
    """
    try:
        search_term = request.args.get('search', '')
        provider_id = request.args.get('provider_id', 'PROV001')
        
        current_app.logger.info(f"🔍 Getting patients (search: '{search_term}', provider: {provider_id})")
        
        patient_service = PatientService(get_db())
        patients = patient_service.search_patients(search_term, provider_id)
        
        current_app.logger.info(f"✅ Found {len(patients)} patients")
        
        return success_response({
            'patients': patients,
            'count': len(patients),
            'search_term': search_term
        })
        
    except Exception as e:
        current_app.logger.error(f"❌ Error getting patients: {str(e)}")
        return error_response(str(e), 500)

@bp.route('/patients/<patient_id>', methods=['PUT'])
def update_patient(patient_id):
    """
    Update patient information
    """
    try:
        data = request.get_json()
        
        patient_service = PatientService(get_db())
        result = patient_service.update_patient(patient_id, data)
        
        if result['success']:
            current_app.logger.info(f"✅ Patient updated: {patient_id}")
            return success_response(result)
        else:
            return error_response(result['error'], 500)
        
    except Exception as e:
        current_app.logger.error(f"❌ Error updating patient: {str(e)}")
        return error_response(str(e), 500)

@bp.route('/patients/<patient_id>', methods=['DELETE'])
def delete_patient(patient_id):
    """
    Delete patient (soft delete)
    """
    try:
        patient_service = PatientService(get_db())
        result = patient_service.delete_patient(patient_id)
        
        if result['success']:
            current_app.logger.info(f"✅ Patient deleted: {patient_id}")
            return success_response(result)
        else:
            return error_response(result['error'], 500)
        
    except Exception as e:
        current_app.logger.error(f"❌ Error deleting patient: {str(e)}")
        return error_response(str(e), 500)
