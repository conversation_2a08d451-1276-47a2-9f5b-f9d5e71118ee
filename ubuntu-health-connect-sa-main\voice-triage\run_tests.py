#!/usr/bin/env python3
"""
Voice Triage System Test Runner
Simple script to run various tests for the voice triage system
"""

import os
import sys
import subprocess
import time
import requests
from datetime import datetime

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version.split()[0]} detected")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = ['flask', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\n📦 Install missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_environment():
    """Check environment configuration"""
    env_file = os.path.join(os.path.dirname(__file__), '.env')
    if os.path.exists(env_file):
        print("✅ .env file found")
        return True
    else:
        print("⚠️ .env file not found - creating from example...")
        example_file = os.path.join(os.path.dirname(__file__), '.env.example')
        if os.path.exists(example_file):
            try:
                with open(example_file, 'r') as src, open(env_file, 'w') as dst:
                    dst.write(src.read())
                print("✅ Created .env file from .env.example")
                return True
            except Exception as e:
                print(f"❌ Failed to create .env file: {e}")
                return False
        else:
            print("❌ .env.example file not found")
            return False

def run_simple_test():
    """Run the simple test script"""
    print("\n🧪 Running Simple Voice Triage Tests")
    print("=" * 50)
    
    try:
        # Run the test script
        result = subprocess.run([
            sys.executable, 'test_simple.py', '--test-only'
        ], cwd=os.path.dirname(__file__), capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Simple tests completed successfully")
            print(result.stdout)
        else:
            print("❌ Simple tests failed")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Tests timed out")
        return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def start_test_server():
    """Start the test server in background"""
    print("\n🚀 Starting Voice Triage Test Server")
    print("=" * 50)
    
    try:
        # Start server in background
        process = subprocess.Popen([
            sys.executable, 'test_simple.py', '--server-only'
        ], cwd=os.path.dirname(__file__))
        
        # Wait for server to start
        print("⏳ Waiting for server to start...")
        time.sleep(3)
        
        # Check if server is running
        try:
            response = requests.get('http://localhost:5000/health', timeout=5)
            if response.status_code == 200:
                print("✅ Test server is running at http://localhost:5000")
                return process
            else:
                print(f"❌ Server responded with status {response.status_code}")
                process.terminate()
                return None
        except requests.exceptions.RequestException:
            print("❌ Server is not responding")
            process.terminate()
            return None
            
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None

def run_api_tests():
    """Run API endpoint tests"""
    print("\n🔗 Testing API Endpoints")
    print("=" * 50)
    
    base_url = 'http://localhost:5000'
    endpoints = [
        {'url': '/health', 'method': 'GET', 'name': 'Health Check'},
        {'url': '/api/voice-triage/test', 'method': 'GET', 'name': 'Test Endpoint'},
        {'url': '/test-suite', 'method': 'GET', 'name': 'Test Suite'},
    ]
    
    results = []
    
    for endpoint in endpoints:
        try:
            if endpoint['method'] == 'GET':
                response = requests.get(f"{base_url}{endpoint['url']}", timeout=10)
            else:
                response = requests.post(f"{base_url}{endpoint['url']}", timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {endpoint['name']}: OK")
                results.append(True)
            else:
                print(f"❌ {endpoint['name']}: Status {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ {endpoint['name']}: Error - {e}")
            results.append(False)
    
    return all(results)

def main():
    """Main test runner function"""
    print("🏥 Ubuntu Health Voice Triage System - Test Runner")
    print("🌍 Ubuntu Philosophy: 'I am because we are'")
    print("=" * 60)
    print(f"📅 Test run started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Pre-flight checks
    print("\n🔍 Pre-flight Checks")
    print("-" * 30)
    
    if not check_python_version():
        return False
    
    if not check_dependencies():
        return False
    
    if not check_environment():
        return False
    
    print("\n✅ All pre-flight checks passed!")
    
    # Start test server
    server_process = start_test_server()
    if not server_process:
        return False
    
    try:
        # Run API tests
        api_success = run_api_tests()
        
        # Run comprehensive test suite
        print("\n🧪 Running Comprehensive Test Suite")
        print("=" * 50)
        
        try:
            response = requests.get('http://localhost:5000/test-suite', timeout=30)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ Comprehensive test suite completed successfully")
                    test_results = data.get('test_results', {})
                    print(f"📊 Results: {test_results.get('passed', 0)}/{test_results.get('total_tests', 0)} tests passed")
                else:
                    print(f"❌ Test suite failed: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ Test suite endpoint returned status {response.status_code}")
        except Exception as e:
            print(f"❌ Error running test suite: {e}")
        
        print("\n" + "=" * 60)
        print("🎯 TEST SUMMARY")
        print("=" * 60)
        print("✅ Voice Triage System is ready for testing!")
        print("🔗 Server running at: http://localhost:5000")
        print("📖 Available endpoints:")
        print("   - /health - System health check")
        print("   - /test-suite - Run all tests")
        print("   - /api/voice-triage/test - Basic test endpoint")
        print("   - /api/voice-triage/mock-assessment - AI assessment testing")
        print("   - /api/voice-triage/mock-call - Voice call simulation")
        print("\n🛑 Press Ctrl+C to stop the server")
        
        # Keep server running
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping server...")
        
    finally:
        # Clean up
        if server_process:
            server_process.terminate()
            server_process.wait()
        print("✅ Server stopped")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Test runner interrupted by user")
    except Exception as e:
        print(f"\n❌ Test runner failed: {e}")
