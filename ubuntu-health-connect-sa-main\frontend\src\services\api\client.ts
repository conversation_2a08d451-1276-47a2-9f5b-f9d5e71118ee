/**
 * API Client for Ubuntu Health Connect SA
 * Centralized HTTP client with proper error handling and type safety
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';
const API_VERSION = import.meta.env.VITE_API_VERSION || 'v1';

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string | string[];
  timestamp?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    per_page: number;
    total: number;
    pages: number;
  };
}

// API Error Class
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// API Client Class
class ApiClient {
  private client: AxiosInstance;
  private isConnected: boolean = false;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
    this.testConnection();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`🔄 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log(`✅ API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('❌ API Response Error:', error);
        
        if (error.response) {
          // Server responded with error status
          const apiError = new ApiError(
            error.response.data?.error || error.message,
            error.response.status,
            error.response.data
          );
          return Promise.reject(apiError);
        } else if (error.request) {
          // Request was made but no response received
          const apiError = new ApiError(
            'Network error - unable to connect to server',
            0
          );
          return Promise.reject(apiError);
        } else {
          // Something else happened
          const apiError = new ApiError(error.message, 0);
          return Promise.reject(apiError);
        }
      }
    );
  }

  private async testConnection(): Promise<void> {
    try {
      const response = await this.client.get('/health');
      if (response.data.status === 'healthy') {
        this.isConnected = true;
        console.log('✅ API Client connected to backend');
      }
    } catch (error) {
      this.isConnected = false;
      console.warn('⚠️ API Client could not connect to backend');
    }
  }

  // Public methods
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.get<ApiResponse<T>>(url, config);
    return response.data;
  }

  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.post<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.put<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.delete<ApiResponse<T>>(url, config);
    return response.data;
  }

  public async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.patch<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  // Connection status
  public getConnectionStatus(): boolean {
    return this.isConnected;
  }

  // Reconnect method
  public async reconnect(): Promise<boolean> {
    await this.testConnection();
    return this.isConnected;
  }

  // Health check
  public async healthCheck(): Promise<ApiResponse> {
    return this.get('/health');
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Export for debugging
(window as any).apiClient = apiClient;
