import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Phone, Mic, Clock, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

// Voice Triage Configuration
const VOICE_TRIAGE_CONFIG = {
  displayNumber: '************',
  internationalNumber: '+27 72 780 3582',
  telLink: '+27727803582',
  emergencyNumber: '10177'
};

interface VoiceTriageButtonProps {
  onStartCall?: () => void;
  disabled?: boolean;
  className?: string;
}

interface CallStatus {
  status: 'idle' | 'calling' | 'connected' | 'completed' | 'failed';
  duration?: number;
  message?: string;
}

export const VoiceTriageButton: React.FC<VoiceTriageButtonProps> = ({
  onStartCall,
  disabled = false,
  className = ''
}) => {
  const [callStatus, setCallStatus] = useState<CallStatus>({ status: 'idle' });
  const [showInstructions, setShowInstructions] = useState(false);

  const handleStartCall = () => {
    if (onStartCall) {
      onStartCall();
    } else {
      // Default behavior - show phone number to call
      setShowInstructions(true);
    }
  };

  const getStatusIcon = () => {
    switch (callStatus.status) {
      case 'calling':
        return <Phone className="w-5 h-5 animate-pulse" />;
      case 'connected':
        return <Mic className="w-5 h-5 text-green-600" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-600" />;
      default:
        return <Phone className="w-5 h-5" />;
    }
  };

  const getStatusText = () => {
    switch (callStatus.status) {
      case 'calling':
        return 'Connecting...';
      case 'connected':
        return `Connected (${Math.floor((callStatus.duration || 0) / 60)}:${String((callStatus.duration || 0) % 60).padStart(2, '0')})`;
      case 'completed':
        return 'Call completed';
      case 'failed':
        return 'Call failed';
      default:
        return 'Start Voice Triage';
    }
  };

  const getButtonColor = () => {
    switch (callStatus.status) {
      case 'calling':
        return 'bg-yellow-500 hover:bg-yellow-600';
      case 'connected':
        return 'bg-green-500 hover:bg-green-600';
      case 'completed':
        return 'bg-blue-500 hover:bg-blue-600';
      case 'failed':
        return 'bg-red-500 hover:bg-red-600';
      default:
        return 'bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700';
    }
  };

  if (showInstructions) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            <Phone className="w-6 h-6 text-green-600" />
            Voice Triage Assistant
          </CardTitle>
          <CardDescription>
            Call our AI-powered triage system for immediate symptom assessment
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 mb-2">
              {VOICE_TRIAGE_CONFIG.displayNumber}
            </div>
            <div className="text-sm text-gray-500 mb-1">
              International: {VOICE_TRIAGE_CONFIG.internationalNumber}
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Available 24/7 in all 11 South African languages
            </p>
          </div>

          <div className="space-y-3">
            <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
              <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
              <div>
                <p className="font-medium text-blue-900">Call the number above</p>
                <p className="text-sm text-blue-700">From your phone: Dial ************</p>
                <p className="text-sm text-blue-600">From computer: Use "Phone Link" or dial manually</p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
              <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
              <div>
                <p className="font-medium text-green-900">Describe your symptoms</p>
                <p className="text-sm text-green-700">Speak clearly about your health concerns</p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg">
              <div className="w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
              <div>
                <p className="font-medium text-purple-900">Get instant assessment</p>
                <p className="text-sm text-purple-700">Receive care recommendations and next steps</p>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
            <div className="flex items-center gap-2 mb-2">
              <Phone className="w-4 h-4 text-blue-600" />
              <span className="font-medium text-blue-800">Testing Mode</span>
            </div>
            <p className="text-sm text-blue-700">
              Currently in test mode. Calling this number will reach the system administrator for testing purposes.
            </p>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="w-4 h-4 text-yellow-600" />
              <span className="font-medium text-yellow-800">Emergency Notice</span>
            </div>
            <p className="text-sm text-yellow-700">
              For life-threatening emergencies, call <strong>{VOICE_TRIAGE_CONFIG.emergencyNumber}</strong> immediately
            </p>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={() => setShowInstructions(false)}
              variant="outline"
              className="flex-1"
            >
              Back
            </Button>
            <Button
              onClick={() => window.open(`tel:${VOICE_TRIAGE_CONFIG.telLink}`)}
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              <Phone className="w-4 h-4 mr-2" />
              Call Now
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Button
      onClick={handleStartCall}
      disabled={disabled || callStatus.status === 'calling'}
      className={`${getButtonColor()} text-white font-medium shadow-lg transition-all duration-300 hover:scale-105 ${className}`}
    >
      <div className="flex items-center gap-2">
        {getStatusIcon()}
        <span>{getStatusText()}</span>
        {callStatus.status === 'connected' && (
          <Badge variant="secondary" className="ml-2">
            <Clock className="w-3 h-3 mr-1" />
            Live
          </Badge>
        )}
      </div>
    </Button>
  );
};

export default VoiceTriageButton;
