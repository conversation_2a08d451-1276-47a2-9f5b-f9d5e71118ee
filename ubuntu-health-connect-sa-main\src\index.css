/* Import Google Fonts for better typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Ubuntu HealthConnect SA Theme */
@import url('https://fonts.googleapis.com/css2?family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap');
@import './styles/ubuntu-theme.css';

/* Ubuntu HealthConnect SA Global Styles */
* {
  font-family: 'Ubuntu', 'Segoe UI', 'Roboto', sans-serif;
}

body {
  font-family: 'Ubuntu', 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg,
    rgba(34, 139, 34, 0.1) 0%,    /* Green */
    rgba(255, 215, 0, 0.1) 25%,   /* Gold */
    rgba(255, 69, 0, 0.1) 50%,    /* Orange */
    rgba(220, 20, 60, 0.1) 75%,   /* Red */
    rgba(0, 0, 128, 0.1) 100%     /* Blue */
  );
  min-height: 100vh;
}

/* Ubuntu Philosophy Quote Styles */
.ubuntu-philosophy {
  font-style: italic;
  color: #FFD700;
  text-align: center;
  font-weight: 700;
}

/* South African Language Support */
.sa-language-text {
  font-family: 'Ubuntu', 'Noto Sans', 'DejaVu Sans', sans-serif;
  line-height: 1.6;
}

/* Ubuntu Accessibility Improvements */
.ubuntu-high-contrast-text {
  font-weight: 900;
  color: #000000;
}

/* Ubuntu Animation for Page Transitions */
.ubuntu-page-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ubuntu Focus Styles for Accessibility */
*:focus {
  outline: 3px solid #FFD700;
  outline-offset: 2px;
  border-radius: 4px;
}

/* South African Flag Wind Animation */
@keyframes flagWave {
  0%, 100% {
    transform: translateX(0) translateY(0) rotateY(0deg);
  }
  25% {
    transform: translateX(2px) translateY(-1px) rotateY(1deg);
  }
  50% {
    transform: translateX(-1px) translateY(2px) rotateY(-0.5deg);
  }
  75% {
    transform: translateX(3px) translateY(1px) rotateY(0.8deg);
  }
}

@keyframes windFlow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes gentleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.flag-wave {
  animation: flagWave 4s ease-in-out infinite;
}

.wind-flow {
  animation: windFlow 8s linear infinite;
}

.gentle-float {
  animation: gentleFloat 6s ease-in-out infinite;
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    /* Healthcare-inspired color palette */
    --background: 210 20% 98%;
    --foreground: 215 25% 27%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;

    /* Primary: Deep healthcare blue */
    --primary: 210 100% 56%;
    --primary-foreground: 0 0% 100%;

    /* Secondary: Soft teal */
    --secondary: 180 25% 94%;
    --secondary-foreground: 180 100% 15%;

    --muted: 210 20% 96%;
    --muted-foreground: 215 15% 47%;

    --accent: 180 100% 67%;
    --accent-foreground: 180 100% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 20% 90%;
    --input: 210 20% 90%;
    --ring: 210 100% 56%;

    --radius: 0.75rem;

    /* Enhanced Healthcare Color Palette */
    --healthcare-blue: 210 100% 56%;
    --healthcare-blue-light: 210 100% 65%;
    --healthcare-blue-dark: 210 100% 45%;
    --healthcare-teal: 180 100% 42%;
    --healthcare-teal-light: 180 100% 55%;
    --healthcare-teal-dark: 180 100% 35%;
    --healthcare-green: 142 76% 36%;
    --healthcare-green-light: 142 76% 45%;
    --healthcare-green-dark: 142 76% 30%;
    --healthcare-purple: 262 83% 58%;
    --healthcare-purple-light: 262 83% 68%;
    --healthcare-purple-dark: 262 83% 48%;
    --healthcare-orange: 25 95% 53%;
    --healthcare-orange-light: 25 95% 63%;
    --healthcare-orange-dark: 25 95% 43%;
    --healthcare-pink: 330 81% 60%;
    --healthcare-pink-light: 330 81% 70%;
    --healthcare-pink-dark: 330 81% 50%;

    /* Advanced Gradient Backgrounds */
    --gradient-primary: linear-gradient(135deg, hsl(210 100% 56%) 0%, hsl(180 100% 42%) 50%, hsl(142 76% 36%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(180 100% 42%) 0%, hsl(142 76% 36%) 50%, hsl(262 83% 58%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(262 83% 58%) 0%, hsl(330 81% 60%) 50%, hsl(25 95% 53%) 100%);
    --gradient-mesh: radial-gradient(at 40% 20%, hsl(210 100% 56%) 0px, transparent 50%),
                     radial-gradient(at 80% 0%, hsl(180 100% 42%) 0px, transparent 50%),
                     radial-gradient(at 0% 50%, hsl(142 76% 36%) 0px, transparent 50%),
                     radial-gradient(at 80% 50%, hsl(262 83% 58%) 0px, transparent 50%),
                     radial-gradient(at 0% 100%, hsl(330 81% 60%) 0px, transparent 50%),
                     radial-gradient(at 80% 100%, hsl(25 95% 53%) 0px, transparent 50%),
                     radial-gradient(at 0% 0%, hsl(210 100% 56%) 0px, transparent 50%);

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 215 28% 17%;
    --foreground: 210 20% 98%;

    --card: 215 28% 17%;
    --card-foreground: 210 20% 98%;

    --popover: 215 28% 17%;
    --popover-foreground: 210 20% 98%;

    --primary: 210 100% 56%;
    --primary-foreground: 0 0% 100%;

    --secondary: 215 25% 23%;
    --secondary-foreground: 210 20% 98%;

    --muted: 215 25% 23%;
    --muted-foreground: 215 15% 70%;

    --accent: 180 100% 67%;
    --accent-foreground: 180 100% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 215 25% 23%;
    --input: 215 25% 23%;
    --ring: 210 100% 56%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    scroll-behavior: smooth;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    letter-spacing: -0.025em;
    line-height: 1.2;
  }

  h1 {
    font-weight: 800;
    letter-spacing: -0.05em;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--primary));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.8);
  }
}

@layer components {
  /* Advanced Healthcare Gradient Backgrounds */
  .bg-healthcare-gradient {
    background: var(--gradient-primary);
    position: relative;
  }

  .bg-healthcare-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-mesh);
    opacity: 0.3;
    pointer-events: none;
  }

  .bg-healthcare-gradient-dark {
    background: linear-gradient(135deg, hsl(215 28% 17%) 0%, hsl(210 100% 20%) 50%, hsl(180 100% 25%) 100%);
  }

  .bg-healthcare-mesh {
    background: var(--gradient-mesh);
  }

  /* Enhanced Glassmorphism Effects */
  .glass-card {
    @apply backdrop-blur-2xl bg-white/25 border border-white/30 shadow-2xl;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.25);
  }

  .glass-card-dark {
    @apply backdrop-blur-2xl bg-black/25 border border-white/20 shadow-2xl;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(0, 0, 0, 0.25);
  }

  .glass-card-premium {
    @apply backdrop-blur-3xl bg-white/30 border border-white/40 shadow-2xl;
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  /* Circular progress indicators */
  .circular-progress {
    @apply relative inline-flex items-center justify-center;
  }

  .circular-progress svg {
    transform: rotate(-90deg);
  }

  .circular-progress .progress-ring {
    fill: none;
    stroke-width: 8;
    stroke-linecap: round;
  }

  .circular-progress .progress-background {
    stroke: hsl(var(--muted));
  }

  .circular-progress .progress-foreground {
    stroke: hsl(var(--primary));
    stroke-dasharray: 251.2; /* 2 * π * 40 */
    stroke-dashoffset: 251.2;
    transition: stroke-dashoffset 0.5s ease-in-out;
  }

  /* Healthcare-specific color utilities */
  .text-healthcare-blue { color: hsl(var(--healthcare-blue)); }
  .text-healthcare-teal { color: hsl(var(--healthcare-teal)); }
  .text-healthcare-green { color: hsl(var(--healthcare-green)); }
  .text-healthcare-purple { color: hsl(var(--healthcare-purple)); }
  .text-healthcare-orange { color: hsl(var(--healthcare-orange)); }
  .text-healthcare-pink { color: hsl(var(--healthcare-pink)); }

  .bg-healthcare-blue { background-color: hsl(var(--healthcare-blue)); }
  .bg-healthcare-teal { background-color: hsl(var(--healthcare-teal)); }
  .bg-healthcare-green { background-color: hsl(var(--healthcare-green)); }
  .bg-healthcare-purple { background-color: hsl(var(--healthcare-purple)); }
  .bg-healthcare-orange { background-color: hsl(var(--healthcare-orange)); }
  .bg-healthcare-pink { background-color: hsl(var(--healthcare-pink)); }

  /* Premium Modern Card Styles */
  .modern-card {
    @apply bg-white rounded-3xl border-0 overflow-hidden;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
                0 8px 10px -6px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .modern-card-hover {
    @apply modern-card transition-all duration-500 ease-out hover:shadow-2xl hover:-translate-y-2 hover:scale-[1.02];
    transform-origin: center;
  }

  .modern-card-premium {
    @apply bg-gradient-to-br from-white to-gray-50 rounded-3xl border-0 overflow-hidden;
    box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.15),
                0 8px 16px -8px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
  }

  .modern-card-premium-hover {
    @apply modern-card-premium transition-all duration-700 ease-out hover:shadow-3xl hover:-translate-y-3 hover:scale-[1.03];
    transform-origin: center;
  }

  /* Enhanced Statistics Display */
  .stat-card {
    @apply modern-card-premium p-8 text-center;
  }

  .stat-number {
    @apply text-5xl font-black mb-3;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .stat-label {
    @apply text-sm text-muted-foreground font-semibold tracking-wide uppercase;
  }

  /* Animated Elements */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-gradient {
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  @keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
  }

  /* Advanced Shadow Effects */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  /* Glow Effects */
  .glow-blue {
    box-shadow: 0 0 20px hsl(var(--healthcare-blue) / 0.5);
  }

  .glow-teal {
    box-shadow: 0 0 20px hsl(var(--healthcare-teal) / 0.5);
  }

  .glow-green {
    box-shadow: 0 0 20px hsl(var(--healthcare-green) / 0.5);
  }

  /* Text Glow - Removed for clarity */
  .text-glow {
    font-weight: 700;
  }

  /* Enhanced Text Visibility - No shadows */
  .text-white-enhanced {
    color: white;
    font-weight: 900;
  }

  .text-contrast-high {
    font-weight: 900;
    color: #000000;
  }

  .text-shadow-strong {
    font-weight: 800;
    color: #1a1a1a;
  }

  .text-shadow-medium {
    font-weight: 700;
    color: #2a2a2a;
  }

  .text-shadow-sm {
    font-weight: 600;
    color: #3a3a3a;
  }

  /* Morphing Background */
  .morphing-bg {
    background: linear-gradient(-45deg,
      hsl(var(--healthcare-blue)),
      hsl(var(--healthcare-teal)),
      hsl(var(--healthcare-green)),
      hsl(var(--healthcare-purple)));
    background-size: 400% 400%;
    animation: morphing 15s ease infinite;
  }

  @keyframes morphing {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Breathing Animation */
  .animate-breathe {
    animation: breathe 4s ease-in-out infinite;
  }

  @keyframes breathe {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  /* Bounce In Animation */
  .animate-bounce-in {
    animation: bounceIn 0.8s ease-out;
  }

  @keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
  }

  /* Slide Up Animation */
  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  @keyframes slideUp {
    0% { transform: translateY(30px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }

  /* Fade In Scale Animation */
  .animate-fade-in-scale {
    animation: fadeInScale 0.8s ease-out;
  }

  @keyframes fadeInScale {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
  }

  /* Enhanced Card Animations */
  .animate-tilt {
    animation: tilt 10s infinite linear;
  }

  @keyframes tilt {
    0%, 50%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(1deg); }
    75% { transform: rotate(-1deg); }
  }

  .hover\:scale-102:hover {
    transform: scale(1.02);
  }

  .hover\:scale-105:hover {
    transform: scale(1.05);
  }

  /* Enhanced Gradient Borders */
  .gradient-border {
    position: relative;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
  }

  @keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* Glass Morphism Enhanced */
  .glass-morphism {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-morphism-strong {
    background: rgba(255, 255, 255, 0.35);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border: 2px solid rgba(255, 255, 255, 0.25);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.45);
  }

  /* Enhanced Hover Effects */
  .card-hover-effect {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-hover-effect:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  /* Pulsing Glow Animation */
  .animate-pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite alternate;
  }

  @keyframes pulseGlow {
    from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
    to { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8); }
  }

  @keyframes fadeInScale {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
  }

  /* Chat-specific styles */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-white\/20::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 9999px;
  }

  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  /* Grid background pattern */
  .bg-grid-white\/\[0\.02\] {
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  }

  /* Chat message animations */
  @keyframes messageSlideIn {
    0% {
      transform: translateY(20px) scale(0.95);
      opacity: 0;
    }
    100% {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }

  .animate-message-slide-in {
    animation: messageSlideIn 0.4s ease-out;
  }

  /* Typing indicator animation */
  @keyframes typingDot {
    0%, 60%, 100% {
      transform: translateY(0);
    }
    30% {
      transform: translateY(-10px);
    }
  }

  .animate-typing-dot {
    animation: typingDot 1.4s infinite ease-in-out;
  }

  .animate-typing-dot:nth-child(2) {
    animation-delay: 0.2s;
  }

  .animate-typing-dot:nth-child(3) {
    animation-delay: 0.4s;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Responsive Text Sizes */
  .text-responsive-xl {
    font-size: clamp(1.5rem, 4vw, 3rem);
  }

  .text-responsive-2xl {
    font-size: clamp(2rem, 5vw, 4rem);
  }

  .text-responsive-3xl {
    font-size: clamp(2.5rem, 6vw, 5rem);
  }
}