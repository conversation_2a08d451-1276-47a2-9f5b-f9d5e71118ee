
import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface LanguageSelectorProps {
  selected: string;
  onSelect: (language: string) => void;
}

export const LanguageSelector = ({ selected, onSelect }: LanguageSelectorProps) => {
  const languages = [
    { code: 'en', name: 'English', nativeName: 'English', flag: '🇬🇧' },
    { code: 'zu', name: 'Zulu', nativeName: 'isiZulu', flag: '🇿🇦' },
    { code: 'xh', name: 'Xhosa', nativeName: 'isiXhosa', flag: '🇿🇦' },
    { code: 'af', name: 'Afrikaans', nativeName: 'Afrikaans', flag: '🇿🇦' },
    { code: 'st', name: 'Sesotho', nativeName: 'Sesotho', flag: '🇿🇦' },
    { code: 'tn', name: 'Sets<PERSON>', nativeName: 'Sets<PERSON>', flag: '🇿🇦' },
    { code: 'ss', name: '<PERSON><PERSON><PERSON>', nativeName: '<PERSON><PERSON><PERSON>', flag: '🇿🇦' },
    { code: 've', name: '<PERSON><PERSON><PERSON>', nativeName: 'Tshiven<PERSON>', flag: '🇿🇦' },
    { code: 'ts', name: 'Tsonga', nativeName: 'Xitsonga', flag: '🇿🇦' },
    { code: 'nd', name: 'Ndebele', nativeName: 'isiNdebele', flag: '🇿🇦' },
    { code: 'nr', name: 'Northern Sotho', nativeName: 'Sepedi', flag: '🇿🇦' }
  ];

  const selectedLanguage = languages.find(lang => lang.code === selected);

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-white font-medium" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.7)' }}>Language:</span>
      <Select value={selected} onValueChange={onSelect}>
        <SelectTrigger className="w-52 bg-white/90 backdrop-blur-sm border border-white/30 text-gray-900 font-medium">
          <SelectValue>
            {selectedLanguage ? (
              <div className="flex items-center gap-2">
                <span>{selectedLanguage.flag}</span>
                <span>{selectedLanguage.nativeName}</span>
              </div>
            ) : 'Select Language'}
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="bg-white/95 backdrop-blur-xl border border-gray-200">
          {languages.map((language) => (
            <SelectItem key={language.code} value={language.code} className="hover:bg-gray-100/80">
              <div className="flex items-center gap-3">
                <span className="text-lg">{language.flag}</span>
                <div className="flex flex-col items-start">
                  <span className="font-medium text-gray-900">{language.nativeName}</span>
                  <span className="text-xs text-gray-600">{language.name}</span>
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};
