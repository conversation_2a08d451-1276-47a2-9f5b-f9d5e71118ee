#!/usr/bin/env python3
"""
AI Triage Assessment Testing Script
Tests the AI assessment logic with various medical scenarios
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# Test configuration
BASE_URL = 'http://localhost:5000'
TEST_SCENARIOS = [
    {
        'name': 'Common Cold - Low Risk',
        'symptoms': ['runny nose', 'sneezing', 'mild fatigue'],
        'expected_risk': 'low',
        'expected_care': 'self_care',
        'description': 'Typical viral upper respiratory infection'
    },
    {
        'name': 'Flu-like Symptoms - Medium Risk',
        'symptoms': ['fever', 'body aches', 'headache', 'fatigue'],
        'expected_risk': 'medium',
        'expected_care': 'clinic',
        'description': 'Influenza-like illness requiring monitoring'
    },
    {
        'name': 'Severe Infection - High Risk',
        'symptoms': ['high fever', 'severe headache', 'neck stiffness'],
        'expected_risk': 'high',
        'expected_care': 'urgent_care',
        'description': 'Possible meningitis or serious infection'
    },
    {
        'name': 'Cardiac Emergency - Critical Risk',
        'symptoms': ['chest pain', 'shortness of breath', 'sweating'],
        'expected_risk': 'critical',
        'expected_care': 'emergency',
        'description': 'Possible heart attack requiring immediate care'
    },
    {
        'name': 'Respiratory Emergency - Critical Risk',
        'symptoms': ['difficulty breathing', 'blue lips', 'wheezing'],
        'expected_risk': 'critical',
        'expected_care': 'emergency',
        'description': 'Severe respiratory distress'
    },
    {
        'name': 'Neurological Emergency - Critical Risk',
        'symptoms': ['loss of consciousness', 'confusion', 'slurred speech'],
        'expected_risk': 'critical',
        'expected_care': 'emergency',
        'description': 'Possible stroke or neurological emergency'
    },
    {
        'name': 'Gastrointestinal Issue - Medium Risk',
        'symptoms': ['nausea', 'vomiting', 'abdominal pain'],
        'expected_risk': 'medium',
        'expected_care': 'clinic',
        'description': 'Digestive system issues'
    },
    {
        'name': 'Severe Trauma - Critical Risk',
        'symptoms': ['severe bleeding', 'unconscious', 'severe pain'],
        'expected_risk': 'critical',
        'expected_care': 'emergency',
        'description': 'Major trauma requiring immediate intervention'
    },
    {
        'name': 'Mental Health Crisis - High Risk',
        'symptoms': ['suicidal thoughts', 'severe depression', 'anxiety'],
        'expected_risk': 'high',
        'expected_care': 'urgent_care',
        'description': 'Mental health emergency'
    },
    {
        'name': 'Allergic Reaction - High Risk',
        'symptoms': ['severe allergic reaction', 'swelling', 'rash'],
        'expected_risk': 'high',
        'expected_care': 'urgent_care',
        'description': 'Possible anaphylaxis'
    }
]

def test_assessment_scenario(scenario: Dict[str, Any]) -> Dict[str, Any]:
    """Test a specific assessment scenario"""
    try:
        payload = {
            'symptoms': scenario['symptoms'],
            'patient_id': f"test-patient-{int(time.time())}"
        }
        
        print(f"\n🧪 Testing: {scenario['name']}")
        print(f"   Symptoms: {', '.join(scenario['symptoms'])}")
        print(f"   Expected: {scenario['expected_risk']} risk, {scenario['expected_care']} care")
        
        response = requests.post(
            f"{BASE_URL}/api/voice-triage/mock-assessment",
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            assessment = data.get('assessment', {})
            
            risk_level = assessment.get('risk_level', 'unknown')
            care_level = assessment.get('recommended_care_level', 'unknown')
            urgency_score = assessment.get('urgency_score', 0)
            
            # Check if results match expectations
            risk_match = risk_level == scenario['expected_risk']
            care_match = care_level == scenario['expected_care']
            
            status = "✅ PASS" if (risk_match and care_match) else "⚠️ PARTIAL" if (risk_match or care_match) else "❌ FAIL"
            
            print(f"   Result: {status}")
            print(f"   Risk: {risk_level} (expected: {scenario['expected_risk']}) {'✅' if risk_match else '❌'}")
            print(f"   Care: {care_level} (expected: {scenario['expected_care']}) {'✅' if care_match else '❌'}")
            print(f"   Urgency Score: {urgency_score}/10")
            
            # Show AI reasoning if available
            suspected_conditions = assessment.get('suspected_conditions', [])
            if suspected_conditions:
                print(f"   AI Diagnosis: {suspected_conditions[0].get('condition', 'Unknown')}")
            
            immediate_actions = assessment.get('immediate_actions', [])
            if immediate_actions:
                print(f"   Recommendations: {immediate_actions[0]}")
            
            return {
                'scenario': scenario['name'],
                'passed': risk_match and care_match,
                'risk_match': risk_match,
                'care_match': care_match,
                'actual_risk': risk_level,
                'actual_care': care_level,
                'urgency_score': urgency_score,
                'response_time': response.elapsed.total_seconds(),
                'details': assessment
            }
        else:
            print(f"   ❌ FAIL - HTTP {response.status_code}")
            return {
                'scenario': scenario['name'],
                'passed': False,
                'error': f"HTTP {response.status_code}",
                'response_time': response.elapsed.total_seconds()
            }
            
    except Exception as e:
        print(f"   ❌ FAIL - Error: {str(e)}")
        return {
            'scenario': scenario['name'],
            'passed': False,
            'error': str(e),
            'response_time': 0
        }

def run_comprehensive_assessment_tests():
    """Run comprehensive AI assessment tests"""
    print("🧠 AI Triage Assessment Testing Suite")
    print("🌍 Ubuntu Philosophy: 'I am because we are'")
    print("=" * 70)
    print(f"📅 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔗 Testing against: {BASE_URL}")
    print("=" * 70)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Voice Triage server is running")
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        print("💡 Make sure to run: python run_tests.py")
        return
    
    # Run all test scenarios
    results = []
    total_tests = len(TEST_SCENARIOS)
    
    print(f"\n🧪 Running {total_tests} assessment scenarios...")
    print("-" * 70)
    
    for i, scenario in enumerate(TEST_SCENARIOS, 1):
        print(f"\n[{i}/{total_tests}]", end=" ")
        result = test_assessment_scenario(scenario)
        results.append(result)
        time.sleep(0.5)  # Small delay between tests
    
    # Calculate statistics
    passed_tests = sum(1 for r in results if r.get('passed', False))
    failed_tests = total_tests - passed_tests
    success_rate = (passed_tests / total_tests) * 100
    
    risk_accuracy = sum(1 for r in results if r.get('risk_match', False))
    care_accuracy = sum(1 for r in results if r.get('care_match', False))
    
    avg_response_time = sum(r.get('response_time', 0) for r in results) / total_tests
    
    # Print summary
    print("\n" + "=" * 70)
    print("📊 AI ASSESSMENT TEST RESULTS")
    print("=" * 70)
    
    print(f"📈 Overall Results:")
    print(f"   Total Tests: {total_tests}")
    print(f"   ✅ Passed: {passed_tests}")
    print(f"   ❌ Failed: {failed_tests}")
    print(f"   🎯 Success Rate: {success_rate:.1f}%")
    
    print(f"\n🎯 Accuracy Breakdown:")
    print(f"   Risk Level Accuracy: {risk_accuracy}/{total_tests} ({(risk_accuracy/total_tests)*100:.1f}%)")
    print(f"   Care Level Accuracy: {care_accuracy}/{total_tests} ({(care_accuracy/total_tests)*100:.1f}%)")
    
    print(f"\n⚡ Performance:")
    print(f"   Average Response Time: {avg_response_time:.2f} seconds")
    
    # Show failed tests
    failed_scenarios = [r for r in results if not r.get('passed', False)]
    if failed_scenarios:
        print(f"\n❌ Failed Scenarios:")
        for result in failed_scenarios:
            print(f"   - {result['scenario']}")
            if 'error' in result:
                print(f"     Error: {result['error']}")
            else:
                print(f"     Expected: {result.get('expected_risk', 'unknown')} risk")
                print(f"     Actual: {result.get('actual_risk', 'unknown')} risk")
    
    # Risk level distribution
    risk_distribution = {}
    for result in results:
        risk = result.get('actual_risk', 'unknown')
        risk_distribution[risk] = risk_distribution.get(risk, 0) + 1
    
    print(f"\n📊 Risk Level Distribution:")
    for risk, count in risk_distribution.items():
        print(f"   {risk.title()}: {count} cases")
    
    print("\n" + "=" * 70)
    
    if success_rate >= 80:
        print("🎉 AI Assessment System is performing well!")
    elif success_rate >= 60:
        print("⚠️ AI Assessment System needs some tuning")
    else:
        print("❌ AI Assessment System requires significant improvement")
    
    print("=" * 70)
    
    return results

if __name__ == '__main__':
    try:
        results = run_comprehensive_assessment_tests()
        
        # Save results to file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"assessment_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'total_tests': len(results),
                'passed': sum(1 for r in results if r.get('passed', False)),
                'success_rate': (sum(1 for r in results if r.get('passed', False)) / len(results)) * 100,
                'results': results
            }, f, indent=2)
        
        print(f"📄 Detailed results saved to: {filename}")
        
    except KeyboardInterrupt:
        print("\n🛑 Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Testing failed: {e}")
