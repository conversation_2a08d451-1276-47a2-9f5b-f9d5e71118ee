"""
Data validation utilities
"""

import re
from typing import Dict, List, Any

def validate_patient_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate patient data
    """
    errors = []
    
    # Required fields
    required_fields = ['first_name', 'last_name', 'id_number', 'phone_number']
    for field in required_fields:
        if not data.get(field):
            errors.append(f'{field} is required')
    
    # SA ID number validation (13 digits)
    id_number = data.get('id_number', '')
    if id_number and not re.match(r'^\d{13}$', id_number):
        errors.append('SA ID number must be 13 digits')
    
    # Phone number validation
    phone = data.get('phone_number', '')
    if phone and not re.match(r'^(\+27|0)[0-9]{9}$', phone):
        errors.append('Phone number must be valid SA format')
    
    # Email validation (if provided)
    email = data.get('email', '')
    if email and not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
        errors.append('Email format is invalid')
    
    # Age validation
    age = data.get('age')
    if age is not None and (not isinstance(age, int) or age < 0 or age > 150):
        errors.append('Age must be between 0 and 150')
    
    # Gender validation
    gender = data.get('gender', '')
    if gender and gender not in ['Male', 'Female', 'Other']:
        errors.append('Gender must be Male, Female, or Other')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors
    }

def validate_ai_interaction_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate AI interaction data
    """
    errors = []
    
    # Required fields
    required_fields = ['patient_id']
    for field in required_fields:
        if not data.get(field):
            errors.append(f'{field} is required')
    
    # Interaction type validation
    interaction_type = data.get('interaction_type', 'chat')
    if interaction_type not in ['chat', 'voice', 'assessment', 'monitoring']:
        errors.append('Invalid interaction type')
    
    # Severity validation
    severity = data.get('severity', 'Low')
    if severity not in ['Low', 'Moderate', 'High', 'Critical']:
        errors.append('Invalid severity level')
    
    return {
        'valid': len(errors) == 0,
        'errors': errors
    }
