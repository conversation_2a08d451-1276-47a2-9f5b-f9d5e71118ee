/**
 * Enhanced API Client for Ubuntu Health Connect SA
 * Improved version with better error handling and type safety
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';
const API_TIMEOUT = 10000;

// Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string | string[];
  timestamp?: string;
}

export interface ApiError {
  message: string;
  status: number;
  response?: any;
}

// Enhanced API Client
class EnhancedApiClient {
  private client: AxiosInstance;
  private isConnected: boolean = false;
  private connectionAttempts: number = 0;
  private maxRetries: number = 3;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: API_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
    this.testConnection();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`🔄 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log(`✅ API Response: ${response.status} ${response.config.url}`);
        this.isConnected = true;
        this.connectionAttempts = 0;
        return response;
      },
      (error) => {
        console.error('❌ API Response Error:', error);
        this.isConnected = false;
        
        const apiError: ApiError = {
          message: error.response?.data?.error || error.message || 'Unknown error',
          status: error.response?.status || 0,
          response: error.response?.data
        };
        
        return Promise.reject(apiError);
      }
    );
  }

  private async testConnection(): Promise<void> {
    try {
      const response = await this.client.get('/health');
      if (response.data.status === 'healthy') {
        this.isConnected = true;
        this.connectionAttempts = 0;
        console.log('✅ Enhanced API Client connected to backend');
      }
    } catch (error) {
      this.isConnected = false;
      this.connectionAttempts++;
      console.warn(`⚠️ API Client connection attempt ${this.connectionAttempts} failed`);
      
      // Retry connection after delay
      if (this.connectionAttempts < this.maxRetries) {
        setTimeout(() => this.testConnection(), 2000 * this.connectionAttempts);
      }
    }
  }

  // Enhanced request methods with retry logic
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.executeWithRetry(() => this.client.get<ApiResponse<T>>(url, config));
  }

  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.executeWithRetry(() => this.client.post<ApiResponse<T>>(url, data, config));
  }

  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.executeWithRetry(() => this.client.put<ApiResponse<T>>(url, data, config));
  }

  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.executeWithRetry(() => this.client.delete<ApiResponse<T>>(url, config));
  }

  private async executeWithRetry<T>(
    requestFn: () => Promise<AxiosResponse<ApiResponse<T>>>,
    retries: number = 2
  ): Promise<ApiResponse<T>> {
    try {
      const response = await requestFn();
      return response.data;
    } catch (error: any) {
      if (retries > 0 && (error.status === 0 || error.status >= 500)) {
        console.log(`🔄 Retrying request... (${retries} attempts left)`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return this.executeWithRetry(requestFn, retries - 1);
      }
      throw error;
    }
  }

  // Connection utilities
  public getConnectionStatus(): boolean {
    return this.isConnected;
  }

  public async reconnect(): Promise<boolean> {
    this.connectionAttempts = 0;
    await this.testConnection();
    return this.isConnected;
  }

  public async healthCheck(): Promise<ApiResponse> {
    return this.get('/health');
  }
}

// Create singleton instance
export const enhancedApiClient = new EnhancedApiClient();

// Make available globally for debugging
(window as any).enhancedApiClient = enhancedApiClient;
