<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Health Assistant Chat Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .chat-box {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            min-height: 300px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        input, button, select {
            padding: 10px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        button {
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .message {
            margin: 10px 0;
            padding: 8px 12px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }
        .user {
            background: rgba(76, 175, 80, 0.8);
            margin-left: auto;
            text-align: right;
        }
        .assistant {
            background: rgba(33, 150, 243, 0.8);
            margin-right: auto;
        }
        .error {
            background: rgba(244, 67, 54, 0.8);
            margin-right: auto;
        }
        .status {
            font-size: 12px;
            opacity: 0.8;
            margin: 5px 0;
        }
        .loading {
            background: rgba(255, 193, 7, 0.8);
            margin-right: auto;
            font-style: italic;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid #4CAF50;
        }
        .failure {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🩺 AI Health Assistant Chat - Live Test</h1>
        
        <div class="test-section">
            <h3>🔧 System Status</h3>
            <div id="systemStatus">Checking system status...</div>
            <button onclick="checkSystemStatus()">Refresh Status</button>
        </div>

        <div class="test-section">
            <h3>💬 Live Chat Test</h3>
            <div>
                <label>Language: </label>
                <select id="languageSelect">
                    <option value="en">English</option>
                    <option value="zu">isiZulu</option>
                    <option value="af">Afrikaans</option>
                </select>
            </div>
            <div class="chat-box" id="chatBox"></div>
            <div>
                <input type="text" id="messageInput" placeholder="Type your health question..." style="width: 70%;">
                <button onclick="sendMessage()" id="sendButton">Send</button>
                <button onclick="clearChat()">Clear</button>
            </div>
            <div class="status" id="chatStatus">Ready to chat</div>
        </div>

        <div class="test-section">
            <h3>🧪 Quick Tests</h3>
            <button onclick="testHeadache()">Test: "I have a headache"</button>
            <button onclick="testFever()">Test: "I have a fever"</button>
            <button onclick="testChestPain()">Test: "I have chest pain" (Urgent)</button>
            <button onclick="testGeneral()">Test: "Hello, can you help me?"</button>
        </div>

        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="testResults"></div>
        </div>
    </div>

    <script>
        const API_KEY = '********************************************************************************************************************************************************************';
        let conversation = [];
        let isLoading = false;

        // System status check
        async function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            statusDiv.innerHTML = 'Checking...';
            
            const tests = {
                'Frontend': await testFrontend(),
                'Backend': await testBackend(),
                'OpenAI API': await testOpenAI()
            };
            
            let statusHTML = '';
            for (const [test, result] of Object.entries(tests)) {
                const icon = result ? '✅' : '❌';
                statusHTML += `<div>${icon} ${test}: ${result ? 'Working' : 'Failed'}</div>`;
            }
            
            statusDiv.innerHTML = statusHTML;
        }

        async function testFrontend() {
            try {
                const response = await fetch('http://localhost:8081');
                return response.ok;
            } catch {
                return false;
            }
        }

        async function testBackend() {
            try {
                const response = await fetch('http://localhost:5000/health');
                return response.ok;
            } catch {
                return false;
            }
        }

        async function testOpenAI() {
            try {
                const response = await fetch('https://api.openai.com/v1/models', {
                    headers: { 'Authorization': `Bearer ${API_KEY}` }
                });
                return response.ok;
            } catch {
                return false;
            }
        }

        // Chat functionality
        function addMessage(role, content, isError = false, isLoading = false) {
            const chatBox = document.getElementById('chatBox');
            const messageDiv = document.createElement('div');
            
            let className = 'message ';
            if (isLoading) className += 'loading';
            else if (isError) className += 'error';
            else className += role;
            
            messageDiv.className = className;
            messageDiv.innerHTML = `<strong>${role === 'user' ? 'You' : 'AI Assistant'}:</strong> ${content}`;
            
            if (isLoading) {
                messageDiv.id = 'loadingMessage';
            }
            
            chatBox.appendChild(messageDiv);
            chatBox.scrollTop = chatBox.scrollHeight;
            
            return messageDiv;
        }

        function removeLoadingMessage() {
            const loadingMsg = document.getElementById('loadingMessage');
            if (loadingMsg) {
                loadingMsg.remove();
            }
        }

        function updateChatStatus(status) {
            document.getElementById('chatStatus').textContent = status;
        }

        function clearChat() {
            document.getElementById('chatBox').innerHTML = '';
            conversation = [];
            updateChatStatus('Chat cleared');
        }

        async function sendMessageToOpenAI(message) {
            const language = document.getElementById('languageSelect').value;
            
            const systemPrompt = `You are a multilingual AI health assistant for HealthConnect SA, designed to help South African patients with medical questions and symptom assessment. 

You provide helpful health information but always recommend consulting healthcare professionals for serious symptoms. You can communicate in English, isiZulu, and Afrikaans.

Key guidelines:
- Be empathetic and professional
- Ask follow-up questions to better understand symptoms
- Provide practical health advice
- Always recommend professional medical care for serious symptoms
- Be culturally sensitive to South African context
- If symptoms seem urgent, strongly recommend immediate medical attention

User's preferred language: ${language === 'zu' ? 'isiZulu (Zulu)' : language === 'af' ? 'Afrikaans' : 'English'}. Please respond in this language when possible.`;

            try {
                const response = await fetch('https://api.openai.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'gpt-4o-mini',
                        messages: [
                            { role: 'system', content: systemPrompt },
                            ...conversation.map(msg => ({ role: msg.role, content: msg.content })),
                            { role: 'user', content: message }
                        ],
                        max_tokens: 500,
                        temperature: 0.7,
                        presence_penalty: 0.1,
                        frequency_penalty: 0.1
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                return data.choices[0].message.content;
            } catch (error) {
                throw error;
            }
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (!message || isLoading) return;

            // Add user message
            addMessage('user', message);
            conversation.push({ role: 'user', content: message });
            input.value = '';

            // Show loading
            isLoading = true;
            document.getElementById('sendButton').disabled = true;
            const loadingMsg = addMessage('assistant', 'Thinking...', false, true);
            updateChatStatus('AI is thinking...');

            try {
                const response = await sendMessageToOpenAI(message);
                
                // Remove loading message
                removeLoadingMessage();
                
                // Add AI response
                addMessage('assistant', response);
                conversation.push({ role: 'assistant', content: response });
                
                updateChatStatus('Message sent successfully');
                logTestResult('Chat Message', true, `Successfully sent: "${message.substring(0, 30)}..."`);
                
            } catch (error) {
                removeLoadingMessage();
                addMessage('assistant', `Error: ${error.message}`, true);
                updateChatStatus('Error occurred');
                logTestResult('Chat Message', false, error.message);
            } finally {
                isLoading = false;
                document.getElementById('sendButton').disabled = false;
            }
        }

        function logTestResult(testName, success, details) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${success ? 'success' : 'failure'}`;
            resultDiv.innerHTML = `
                <div>${success ? '✅' : '❌'} ${testName}</div>
                <div style="font-size: 12px; font-weight: normal;">${details}</div>
                <div style="font-size: 10px; opacity: 0.7;">${new Date().toLocaleTimeString()}</div>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        // Quick test functions
        function testHeadache() {
            document.getElementById('messageInput').value = 'I have a headache';
            sendMessage();
        }

        function testFever() {
            document.getElementById('messageInput').value = 'I have a fever and feel sick';
            sendMessage();
        }

        function testChestPain() {
            document.getElementById('messageInput').value = 'I have chest pain and difficulty breathing';
            sendMessage();
        }

        function testGeneral() {
            document.getElementById('messageInput').value = 'Hello, can you help me with my health?';
            sendMessage();
        }

        // Allow Enter key to send message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Initialize
        window.onload = function() {
            checkSystemStatus();
            updateChatStatus('Ready to chat - Type a health question');
        };
    </script>
</body>
</html>
