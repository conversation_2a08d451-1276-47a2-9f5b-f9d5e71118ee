"""
Simple Africa's Talking Voice Call Test
Based on the exact example code provided by the user
"""

# works with both python 2 and 3
from __future__ import print_function

import africastalking
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class VOICE:
    def __init__(self):
        # Set your app credentials from environment
        self.username = os.getenv('AFRICAS_TALKING_USERNAME', 'sandbox')
        self.api_key = os.getenv('AFRICAS_TALKING_API_KEY')
        
        print(f"Username: {self.username}")
        print(f"API Key: {self.api_key[:10]}..." if self.api_key else "API Key: Not set")
        
        if not self.api_key:
            raise ValueError("AFRICAS_TALKING_API_KEY environment variable is required")
        
        # Initialize the SDK
        africastalking.initialize(self.username, self.api_key)
        # Get the voice service
        self.voice = africastalking.Voice

    def call(self):
        # Set your Africa's Talking phone number in international format
        callFrom = os.getenv('TRIAGE_PHONE_NUMBER', '+27727803582')
        # Set the numbers you want to call to in a comma-separated list
        callTo = [os.getenv('TEST_CALLER_NUMBER', '+27727803582')]
        
        print(f"Calling from: {callFrom}")
        print(f"Calling to: {callTo}")
        
        try:
            # Make the call
            result = self.voice.call(callFrom, callTo)
            print("Call result:", result)
            return result
        except Exception as e:
            print("Encountered an error while making the call: %s" % str(e))
            return None

def test_sms():
    """Test SMS functionality"""
    print("\n📱 Testing SMS...")
    
    try:
        # Initialize SMS service
        sms = africastalking.SMS
        
        # Send test SMS
        message = "Ubuntu Health Test SMS"
        recipients = [os.getenv('TEST_CALLER_NUMBER', '+27727803582')]
        
        print(f"Sending SMS to: {recipients}")
        print(f"Message: {message}")
        
        response = sms.send(message, recipients)
        print("SMS result:", response)
        return response
        
    except Exception as e:
        print("Encountered an error while sending SMS: %s" % str(e))
        return None

if __name__ == '__main__':
    print("🚀 Starting Simple Africa's Talking Test")
    print("=" * 50)
    
    # Test voice call
    print("\n📞 Testing Voice Call...")
    voice_test = VOICE()
    call_result = voice_test.call()
    
    # Test SMS
    sms_result = test_sms()
    
    print("\n" + "=" * 50)
    print("📊 RESULTS SUMMARY")
    print("=" * 50)
    
    if call_result:
        print("✅ Voice call test: SUCCESS")
    else:
        print("❌ Voice call test: FAILED")
    
    if sms_result:
        print("✅ SMS test: SUCCESS")
    else:
        print("❌ SMS test: FAILED")
    
    print("\n🔍 Next Steps:")
    if not call_result and not sms_result:
        print("1. Check your Africa's Talking account balance")
        print("2. Verify your API credentials are correct")
        print("3. Ensure you have a purchased phone number (not sandbox)")
        print("4. Check if your account is verified for voice services")
    elif call_result or sms_result:
        print("1. Some services are working - check individual error messages")
        print("2. Consider upgrading from sandbox to production account")
        print("3. Purchase a dedicated phone number for voice services")
