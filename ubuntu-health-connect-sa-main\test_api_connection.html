<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ubuntu Health API Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #228B22;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1e7b1e;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 Ubuntu Health Connect SA - API Connection Test</h1>
        
        <div class="test-section info">
            <h3>📋 Test Information</h3>
            <p>This page tests the connection between the frontend and backend API.</p>
            <p><strong>Backend URL:</strong> http://localhost:5000</p>
            <p><strong>Frontend URL:</strong> http://localhost:8085</p>
        </div>

        <div class="test-section">
            <h3>🔍 Test 1: Health Check</h3>
            <button onclick="testHealthCheck()">Test Health Endpoint</button>
            <div id="health-result"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Test 2: Test Endpoint</h3>
            <button onclick="testTestEndpoint()">Test API Endpoint</button>
            <div id="test-result"></div>
        </div>

        <div class="test-section">
            <h3>👤 Test 3: Patient Registration</h3>
            <button onclick="testPatientRegistration()">Test Registration</button>
            <div id="registration-result"></div>
        </div>

        <div class="test-section">
            <h3>🔐 Test 4: Patient Login</h3>
            <button onclick="testPatientLogin()">Test Login</button>
            <div id="login-result"></div>
        </div>

        <div class="test-section">
            <h3>📊 Test 5: Get Patients</h3>
            <button onclick="testGetPatients()">Test Get Patients</button>
            <div id="patients-result"></div>
        </div>
    </div>

    <script>
        async function makeRequest(url, options = {}) {
            try {
                console.log(`Making request to: ${url}`);
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });
                
                console.log(`Response status: ${response.status}`);
                const data = await response.json();
                console.log('Response data:', data);
                
                return {
                    success: true,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                console.error('Request error:', error);
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            if (result.success) {
                element.innerHTML = `
                    <div class="success">
                        <strong>✅ Success (${result.status})</strong>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    </div>
                `;
            } else {
                element.innerHTML = `
                    <div class="error">
                        <strong>❌ Error</strong>
                        <p>${result.error}</p>
                    </div>
                `;
            }
        }

        async function testHealthCheck() {
            const result = await makeRequest('http://localhost:5000/health');
            displayResult('health-result', result);
        }

        async function testTestEndpoint() {
            const result = await makeRequest('http://localhost:5000/api/test');
            displayResult('test-result', result);
        }

        async function testPatientRegistration() {
            const testData = {
                first_name: 'Test',
                last_name: 'Patient',
                sa_id_number: '9999999999999',
                phone_number: '**********',
                email: '<EMAIL>',
                age: 30,
                gender: 'Male'
            };

            const result = await makeRequest('http://localhost:5000/api/patients/register', {
                method: 'POST',
                body: JSON.stringify(testData)
            });
            displayResult('registration-result', result);
        }

        async function testPatientLogin() {
            const loginData = {
                sa_id_number: '9999999999999',
                phone_number: '**********'
            };

            const result = await makeRequest('http://localhost:5000/api/patients/login', {
                method: 'POST',
                body: JSON.stringify(loginData)
            });
            displayResult('login-result', result);
        }

        async function testGetPatients() {
            const result = await makeRequest('http://localhost:5000/api/patients');
            displayResult('patients-result', result);
        }

        // Auto-run health check on page load
        window.addEventListener('load', () => {
            console.log('🏥 Ubuntu Health API Test Page Loaded');
            testHealthCheck();
        });
    </script>
</body>
</html>
