import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load environment variables
  const env = loadEnv(mode, process.cwd(), '')

  return {
    server: {
      host: "::",
      port: parseInt(env.VITE_FRONTEND_PORT) || 8085,
      cors: true,
    headers: {
      'Content-Security-Policy': mode === 'development'
        ? "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:* ws://localhost:*; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
        : "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline';"
    }
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Bo<PERSON>an),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  define: {
    global: 'globalThis',
    // Make environment variables available at runtime
    __FRONTEND_PORT__: JSON.stringify(env.VITE_FRONTEND_PORT || '8085'),
    __BACKEND_PORT__: JSON.stringify(env.VITE_BACKEND_PORT || '5001'),
    __BACKEND_HOST__: JSON.stringify(env.VITE_BACKEND_HOST || 'localhost'),
  },
  optimizeDeps: {
    include: ['react', 'react-dom']
  },
  build: {
    sourcemap: mode === 'development',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['lucide-react']
        }
      }
    }
  }
  }
});
