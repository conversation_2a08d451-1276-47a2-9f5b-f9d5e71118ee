<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Patient Registration & Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .section {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
        .output {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            min-height: 150px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        input {
            padding: 8px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix Patient Registration & Login Issues</h1>
        <p>This tool will help fix the "Patient Not Found" and "Registration Failed" errors.</p>
        
        <div class="section">
            <h3>🔍 System Status Check</h3>
            <button onclick="checkSystemStatus()">Check System Status</button>
            <div id="statusOutput" class="output">Click to check system status...</div>
        </div>

        <div class="section">
            <h3>👤 Register Test Patient</h3>
            <div>
                <input type="text" id="testIdNumber" placeholder="SA ID Number" value="9908075432083">
                <input type="text" id="testPhone" placeholder="Phone Number" value="+27821234567">
                <button onclick="registerTestPatient()">Register Test Patient</button>
            </div>
            <div id="registerOutput" class="output">Enter ID and phone to register test patient...</div>
        </div>

        <div class="section">
            <h3>🔐 Test Patient Login</h3>
            <div>
                <input type="text" id="loginIdNumber" placeholder="SA ID Number" value="9908075432083">
                <input type="text" id="loginPhone" placeholder="Phone Number" value="+27821234567">
                <button onclick="testPatientLogin()">Test Login</button>
            </div>
            <div id="loginOutput" class="output">Enter credentials to test login...</div>
        </div>

        <div class="section">
            <h3>🗄️ Database Management</h3>
            <button onclick="listAllPatients()">List All Patients</button>
            <button onclick="clearPatientData()">Clear Patient Data</button>
            <button onclick="syncFrontendBackend()">Sync Frontend-Backend</button>
            <div id="dbOutput" class="output">Database management tools...</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            return `<span class="${className}">[${timestamp}] ${message}</span>\n`;
        }

        async function checkSystemStatus() {
            const output = document.getElementById('statusOutput');
            output.innerHTML = log('Checking system status...', 'info');

            // Check backend
            try {
                const response = await fetch('http://localhost:5000/health');
                if (response.ok) {
                    const data = await response.json();
                    output.innerHTML += log('✅ Backend: Working', 'success');
                    output.innerHTML += log(`   Database: ${data.database?.connected ? 'Connected' : 'Disconnected'}`, data.database?.connected ? 'success' : 'error');
                    output.innerHTML += log(`   Patients: ${data.database?.stats?.patients || 0}`, 'info');
                } else {
                    output.innerHTML += log('❌ Backend: Not responding', 'error');
                }
            } catch (error) {
                output.innerHTML += log(`❌ Backend: Connection failed - ${error.message}`, 'error');
            }

            // Check frontend storage
            const frontendPatients = localStorage.getItem('ubuntu-health-patients');
            if (frontendPatients) {
                const patients = JSON.parse(frontendPatients);
                const count = Object.keys(patients).length;
                output.innerHTML += log(`✅ Frontend: ${count} patients in storage`, 'success');
            } else {
                output.innerHTML += log('⚠️ Frontend: No patients in storage', 'warning');
            }

            // Check current session
            const currentId = localStorage.getItem('currentPatientIdNumber');
            const currentPhone = localStorage.getItem('currentPatientPhone');
            if (currentId && currentPhone) {
                output.innerHTML += log(`👤 Current Session: ID ${currentId}, Phone ${currentPhone}`, 'info');
            } else {
                output.innerHTML += log('👤 Current Session: No active session', 'warning');
            }
        }

        async function registerTestPatient() {
            const output = document.getElementById('registerOutput');
            const idNumber = document.getElementById('testIdNumber').value;
            const phone = document.getElementById('testPhone').value;
            
            output.innerHTML = log(`Registering patient: ID ${idNumber}, Phone ${phone}`, 'info');

            try {
                // Step 1: Register in backend
                output.innerHTML += log('Step 1: Registering in backend...', 'info');
                const backendData = {
                    patient_id: `PAT_${Date.now()}`,
                    first_name: 'Test',
                    last_name: 'Patient',
                    id_number: idNumber,
                    phone_number: phone,
                    email: '<EMAIL>',
                    age: 30,
                    gender: 'Male',
                    address: 'Cape Town, South Africa',
                    emergency_contact_name: 'Emergency Contact',
                    emergency_contact_phone: '+27821234568',
                    emergency_contact_relationship: 'Family'
                };

                const backendResponse = await fetch('http://localhost:5000/api/patients', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(backendData)
                });

                if (backendResponse.ok) {
                    const result = await backendResponse.json();
                    output.innerHTML += log(`✅ Backend registration: ${result.action}`, 'success');
                } else {
                    throw new Error(`Backend registration failed: ${backendResponse.status}`);
                }

                // Step 2: Register in frontend
                output.innerHTML += log('Step 2: Registering in frontend...', 'info');
                const patientId = `PAT_${Date.now()}_FRONTEND`;
                const now = new Date().toISOString();
                
                const frontendPatient = {
                    id: patientId,
                    personalInfo: {
                        firstName: 'Test',
                        lastName: 'Patient',
                        dateOfBirth: '1994-01-01',
                        age: 30,
                        gender: 'Male',
                        idNumber: idNumber,
                        phone: phone,
                        email: '<EMAIL>',
                        address: 'Cape Town, South Africa',
                        emergencyContact: {
                            name: 'Emergency Contact',
                            relationship: 'Family',
                            phone: '+27821234568'
                        }
                    },
                    medicalHistory: [],
                    medications: [],
                    allergies: [],
                    vaccinations: [],
                    labResults: [],
                    appointments: [],
                    insurance: { provider: '', policyNumber: '', groupNumber: '', validUntil: '' },
                    healthcareProviders: ['PROV001'],
                    aiInteractions: [],
                    createdAt: now,
                    updatedAt: now
                };

                // Store in localStorage
                let patients = {};
                const existingData = localStorage.getItem('ubuntu-health-patients');
                if (existingData) {
                    patients = JSON.parse(existingData);
                }
                patients[patientId] = frontendPatient;
                localStorage.setItem('ubuntu-health-patients', JSON.stringify(patients));

                // Store ID mapping
                let idMapping = {};
                const existingMapping = localStorage.getItem('ubuntu-health-patients-by-id');
                if (existingMapping) {
                    idMapping = JSON.parse(existingMapping);
                }
                idMapping[idNumber] = patientId;
                localStorage.setItem('ubuntu-health-patients-by-id', JSON.stringify(idMapping));

                output.innerHTML += log('✅ Frontend registration: Success', 'success');
                output.innerHTML += log(`Patient ID: ${patientId}`, 'info');
                
                // Set current session
                localStorage.setItem('currentPatientId', patientId);
                localStorage.setItem('currentPatientIdNumber', idNumber);
                localStorage.setItem('currentPatientPhone', phone);
                localStorage.setItem('currentPatientName', 'Test Patient');

                output.innerHTML += log('🎉 Registration completed successfully!', 'success');
                output.innerHTML += log('You can now try logging in with these credentials.', 'info');

            } catch (error) {
                output.innerHTML += log(`❌ Registration failed: ${error.message}`, 'error');
            }
        }

        async function testPatientLogin() {
            const output = document.getElementById('loginOutput');
            const idNumber = document.getElementById('loginIdNumber').value;
            const phone = document.getElementById('loginPhone').value;
            
            output.innerHTML = log(`Testing login: ID ${idNumber}, Phone ${phone}`, 'info');

            try {
                // Check frontend storage
                output.innerHTML += log('Checking frontend storage...', 'info');
                const idMapping = localStorage.getItem('ubuntu-health-patients-by-id');
                if (!idMapping) {
                    throw new Error('No patient ID mapping found. Please register first.');
                }

                const mapping = JSON.parse(idMapping);
                const patientId = mapping[idNumber];
                if (!patientId) {
                    throw new Error(`Patient with ID ${idNumber} not found. Please register first.`);
                }

                const patientsData = localStorage.getItem('ubuntu-health-patients');
                if (!patientsData) {
                    throw new Error('No patient data found. Please register first.');
                }

                const patients = JSON.parse(patientsData);
                const patient = patients[patientId];
                if (!patient) {
                    throw new Error('Patient data corrupted. Please register again.');
                }

                output.innerHTML += log(`✅ Patient found: ${patient.personalInfo.firstName} ${patient.personalInfo.lastName}`, 'success');
                output.innerHTML += log(`Phone match: ${patient.personalInfo.phone === phone ? 'Yes' : 'No'}`, patient.personalInfo.phone === phone ? 'success' : 'warning');

                if (patient.personalInfo.phone !== phone) {
                    output.innerHTML += log('⚠️ Phone number mismatch, but login can proceed', 'warning');
                }

                // Set session
                localStorage.setItem('currentPatientId', patientId);
                localStorage.setItem('currentPatientIdNumber', idNumber);
                localStorage.setItem('currentPatientPhone', phone);
                localStorage.setItem('currentPatientName', `${patient.personalInfo.firstName} ${patient.personalInfo.lastName}`);

                output.innerHTML += log('🎉 Login test successful!', 'success');
                output.innerHTML += log('Session data has been set. You can now access the patient dashboard.', 'info');

            } catch (error) {
                output.innerHTML += log(`❌ Login test failed: ${error.message}`, 'error');
                output.innerHTML += log('💡 Try registering the patient first using the registration tool above.', 'info');
            }
        }

        async function listAllPatients() {
            const output = document.getElementById('dbOutput');
            output.innerHTML = log('Listing all patients...', 'info');

            try {
                // Backend patients
                output.innerHTML += log('Backend patients:', 'info');
                const response = await fetch('http://localhost:5000/api/patients');
                if (response.ok) {
                    const backendPatients = await response.json();
                    output.innerHTML += log(`Found ${backendPatients.length} patients in backend:`, 'success');
                    backendPatients.forEach((patient, index) => {
                        output.innerHTML += log(`  ${index + 1}. ${patient.first_name} ${patient.last_name} (ID: ${patient.id_number})`, 'info');
                    });
                } else {
                    output.innerHTML += log('❌ Failed to get backend patients', 'error');
                }

                // Frontend patients
                output.innerHTML += log('\nFrontend patients:', 'info');
                const frontendData = localStorage.getItem('ubuntu-health-patients');
                if (frontendData) {
                    const patients = JSON.parse(frontendData);
                    const patientList = Object.values(patients);
                    output.innerHTML += log(`Found ${patientList.length} patients in frontend:`, 'success');
                    patientList.forEach((patient, index) => {
                        output.innerHTML += log(`  ${index + 1}. ${patient.personalInfo.firstName} ${patient.personalInfo.lastName} (ID: ${patient.personalInfo.idNumber})`, 'info');
                    });
                } else {
                    output.innerHTML += log('No patients found in frontend storage', 'warning');
                }

            } catch (error) {
                output.innerHTML += log(`❌ Error listing patients: ${error.message}`, 'error');
            }
        }

        function clearPatientData() {
            const output = document.getElementById('dbOutput');
            output.innerHTML = log('Clearing patient data...', 'warning');
            
            // Clear frontend storage
            localStorage.removeItem('ubuntu-health-patients');
            localStorage.removeItem('ubuntu-health-patients-by-id');
            localStorage.removeItem('currentPatientId');
            localStorage.removeItem('currentPatientIdNumber');
            localStorage.removeItem('currentPatientPhone');
            localStorage.removeItem('currentPatientName');
            
            output.innerHTML += log('✅ Frontend patient data cleared', 'success');
            output.innerHTML += log('Note: Backend data is not cleared. Use backend admin tools if needed.', 'info');
        }

        async function syncFrontendBackend() {
            const output = document.getElementById('dbOutput');
            output.innerHTML = log('Syncing frontend and backend...', 'info');

            try {
                // Get backend patients
                const response = await fetch('http://localhost:5000/api/patients');
                if (!response.ok) {
                    throw new Error('Failed to get backend patients');
                }

                const backendPatients = await response.json();
                output.innerHTML += log(`Found ${backendPatients.length} patients in backend`, 'info');

                // Convert to frontend format and store
                let frontendPatients = {};
                let idMapping = {};

                backendPatients.forEach(backendPatient => {
                    const frontendId = `PAT_SYNC_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                    const frontendPatient = {
                        id: frontendId,
                        personalInfo: {
                            firstName: backendPatient.first_name || 'Unknown',
                            lastName: backendPatient.last_name || 'Patient',
                            dateOfBirth: '1990-01-01',
                            age: backendPatient.age || 30,
                            gender: backendPatient.gender || 'Unknown',
                            idNumber: backendPatient.id_number,
                            phone: backendPatient.phone_number || '+27000000000',
                            email: backendPatient.email || '<EMAIL>',
                            address: backendPatient.address || 'Unknown',
                            emergencyContact: {
                                name: backendPatient.emergency_contact_name || 'Emergency Contact',
                                relationship: backendPatient.emergency_contact_relationship || 'Family',
                                phone: backendPatient.emergency_contact_phone || '+27000000001'
                            }
                        },
                        medicalHistory: [],
                        medications: [],
                        allergies: [],
                        vaccinations: [],
                        labResults: [],
                        appointments: [],
                        insurance: { provider: '', policyNumber: '', groupNumber: '', validUntil: '' },
                        healthcareProviders: ['PROV001'],
                        aiInteractions: [],
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    };

                    frontendPatients[frontendId] = frontendPatient;
                    idMapping[backendPatient.id_number] = frontendId;
                });

                // Store in localStorage
                localStorage.setItem('ubuntu-health-patients', JSON.stringify(frontendPatients));
                localStorage.setItem('ubuntu-health-patients-by-id', JSON.stringify(idMapping));

                output.innerHTML += log(`✅ Synced ${Object.keys(frontendPatients).length} patients to frontend`, 'success');
                output.innerHTML += log('Frontend and backend are now synchronized', 'success');

            } catch (error) {
                output.innerHTML += log(`❌ Sync failed: ${error.message}`, 'error');
            }
        }

        // Auto-check status on load
        window.onload = function() {
            checkSystemStatus();
        };
    </script>
</body>
</html>
