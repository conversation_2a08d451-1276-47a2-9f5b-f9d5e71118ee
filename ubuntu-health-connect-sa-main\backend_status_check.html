<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ Backend Status - FIXED!</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            color: #333;
        }
        h1 {
            color: #4CAF50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .status-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            background: #f8fff8;
        }
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .log {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .celebration {
            text-align: center;
            font-size: 3em;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="celebration">🎉🚀✅</div>
        <h1>Backend Status - FIXED!</h1>
        
        <div class="success">
            <h2>✅ BACKEND IS NOW RUNNING!</h2>
            <p>The backend server has been successfully started and is responding to requests.</p>
        </div>

        <div class="status-section">
            <h2>🔍 Current Status</h2>
            <div id="currentStatus">Loading...</div>
            <button class="test-button" onclick="checkStatus()">Refresh Status</button>
        </div>

        <div class="status-section">
            <h2>🧪 Test All Functions</h2>
            <button class="test-button" onclick="testAllFunctions()">Test All Backend Functions</button>
            <div id="testResults"></div>
        </div>

        <div class="status-section">
            <h2>🔧 Next Steps</h2>
            <div class="info">
                <h3>What to do now:</h3>
                <ol>
                    <li><strong>Refresh your patient portal page</strong> - The frontend should now connect to the backend</li>
                    <li><strong>Test patient registration</strong> - Try registering a new patient</li>
                    <li><strong>Test AI chat</strong> - The AI assistant should now work properly</li>
                    <li><strong>Test login</strong> - Try logging in with registered patient credentials</li>
                </ol>
            </div>
        </div>

        <div class="status-section">
            <h2>📋 System Log</h2>
            <div class="log" id="systemLog">Backend status check ready...</div>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('systemLog');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            logElement.innerHTML += logMessage + '\n';
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logMessage);
        }

        async function checkStatus() {
            log('🔍 Checking backend status...');
            
            try {
                const response = await fetch('http://localhost:5000/health');
                if (response.ok) {
                    const data = await response.json();
                    
                    document.getElementById('currentStatus').innerHTML = `
                        <div class="success">
                            <h3>✅ Backend Status: ${data.status.toUpperCase()}</h3>
                            <p><strong>Service:</strong> ${data.service}</p>
                            <p><strong>Database Connected:</strong> ${data.database.connected ? '✅ Yes' : '❌ No'}</p>
                            <p><strong>Patients in Database:</strong> ${data.database.stats.patients}</p>
                            <p><strong>AI Interactions:</strong> ${data.database.stats.ai_interactions}</p>
                            <p><strong>Last Updated:</strong> ${new Date(data.timestamp).toLocaleString()}</p>
                        </div>
                    `;
                    
                    log(`✅ Backend is healthy! ${data.database.stats.patients} patients in database`);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('currentStatus').innerHTML = `
                    <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;">
                        <h3>❌ Backend Connection Failed</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                log(`❌ Backend check failed: ${error.message}`);
            }
        }

        async function testAllFunctions() {
            log('🧪 Testing all backend functions...');
            const results = [];
            
            // Test 1: Health check
            try {
                const response = await fetch('http://localhost:5000/health');
                if (response.ok) {
                    results.push('✅ Health check: PASSED');
                    log('✅ Health check passed');
                } else {
                    results.push('❌ Health check: FAILED');
                }
            } catch (error) {
                results.push('❌ Health check: ERROR');
            }

            // Test 2: Get all patients
            try {
                const response = await fetch('http://localhost:5000/api/patients');
                if (response.ok) {
                    const data = await response.json();
                    results.push(`✅ Get patients: PASSED (${data.count} patients)`);
                    log(`✅ Get patients passed: ${data.count} patients found`);
                } else {
                    results.push('❌ Get patients: FAILED');
                }
            } catch (error) {
                results.push('❌ Get patients: ERROR');
            }

            // Test 3: Create test patient
            try {
                const testPatient = {
                    patient_id: `PAT_TEST_${Date.now()}`,
                    first_name: 'Test',
                    last_name: 'Patient',
                    id_number: '9999999999999',
                    phone_number: '**********',
                    email: '<EMAIL>',
                    age: 30,
                    gender: 'Other',
                    address: 'Test Address'
                };

                const response = await fetch('http://localhost:5000/api/patients', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testPatient)
                });

                if (response.ok) {
                    const data = await response.json();
                    results.push(`✅ Create patient: PASSED (${data.action})`);
                    log(`✅ Create patient passed: ${data.action}`);
                } else {
                    results.push('❌ Create patient: FAILED');
                }
            } catch (error) {
                results.push('❌ Create patient: ERROR');
            }

            // Display results
            document.getElementById('testResults').innerHTML = `
                <div class="success">
                    <h3>🧪 Test Results:</h3>
                    <ul>
                        ${results.map(result => `<li>${result}</li>`).join('')}
                    </ul>
                </div>
            `;

            log('🎉 All tests completed!');
        }

        // Auto-check status on page load
        window.addEventListener('load', () => {
            setTimeout(checkStatus, 1000);
        });

        // Auto-refresh every 30 seconds
        setInterval(checkStatus, 30000);
    </script>
</body>
</html>
