/**
 * Enhanced Integration Layer for Ubuntu Health Connect SA
 * Bridges the existing frontend services with the enhanced backend
 */

import { enhancedApiClient } from './api/apiClient';
import { enhancedPatientService } from './api/patientService';
import { patientApiService } from './patientApiService';
import { patientRegistrationService } from './patientRegistrationService';
import { healthcareDatabase } from './healthcareDatabase';

// Integration status
interface IntegrationStatus {
  backendConnected: boolean;
  enhancedFeaturesEnabled: boolean;
  dataSync: boolean;
  lastHealthCheck: string;
  version: string;
}

class EnhancedIntegrationService {
  private static instance: EnhancedIntegrationService;
  private status: IntegrationStatus = {
    backendConnected: false,
    enhancedFeaturesEnabled: false,
    dataSync: false,
    lastHealthCheck: '',
    version: '2.0.0-enhanced'
  };

  constructor() {
    this.initialize();
  }

  static getInstance(): EnhancedIntegrationService {
    if (!EnhancedIntegrationService.instance) {
      EnhancedIntegrationService.instance = new EnhancedIntegrationService();
    }
    return EnhancedIntegrationService.instance;
  }

  private async initialize() {
    console.log('🚀 Enhanced Integration: Initializing...');
    
    try {
      // Test enhanced backend connection
      const healthResponse = await enhancedApiClient.healthCheck();
      
      if (healthResponse.success && healthResponse.data?.status === 'healthy') {
        this.status.backendConnected = true;
        this.status.enhancedFeaturesEnabled = true;
        this.status.lastHealthCheck = new Date().toISOString();
        
        console.log('✅ Enhanced Integration: Connected to enhanced backend');
        console.log(`📊 Backend version: ${healthResponse.data.version}`);
        console.log(`🔧 Enhanced features: ${Object.keys(healthResponse.data.features).join(', ')}`);
        
        // Sync existing frontend data with enhanced backend
        await this.syncFrontendDataToBackend();
      } else {
        console.warn('⚠️ Enhanced Integration: Enhanced backend not available, using existing services');
        this.fallbackToExistingServices();
      }
    } catch (error) {
      console.error('❌ Enhanced Integration: Initialization failed:', error);
      this.fallbackToExistingServices();
    }
  }

  private fallbackToExistingServices() {
    console.log('🔄 Enhanced Integration: Falling back to existing services');
    this.status.backendConnected = false;
    this.status.enhancedFeaturesEnabled = false;
  }

  private async syncFrontendDataToBackend() {
    console.log('🔄 Enhanced Integration: Syncing frontend data to enhanced backend...');
    
    try {
      // Get all patients from existing frontend services
      const frontendPatients = patientRegistrationService.getAllRegisteredPatients();
      console.log(`📋 Found ${frontendPatients.length} patients in frontend`);
      
      let syncedCount = 0;
      let errorCount = 0;
      
      for (const patientResult of frontendPatients) {
        try {
          // Convert frontend patient to enhanced backend format
          const enhancedPatientData = this.convertFrontendToEnhancedFormat(patientResult.patient);
          
          // Send to enhanced backend
          const response = await enhancedPatientService.createPatient(enhancedPatientData);
          
          if (response.success) {
            syncedCount++;
            console.log(`✅ Synced patient: ${patientResult.patient.personalInfo.firstName} ${patientResult.patient.personalInfo.lastName}`);
          } else {
            errorCount++;
            console.warn(`⚠️ Failed to sync patient: ${patientResult.patient.personalInfo.firstName} ${patientResult.patient.personalInfo.lastName}`);
          }
        } catch (error) {
          errorCount++;
          console.error(`❌ Error syncing patient:`, error);
        }
      }
      
      console.log(`📊 Sync complete: ${syncedCount} synced, ${errorCount} errors`);
      this.status.dataSync = errorCount === 0;
      
    } catch (error) {
      console.error('❌ Enhanced Integration: Data sync failed:', error);
      this.status.dataSync = false;
    }
  }

  private convertFrontendToEnhancedFormat(frontendPatient: any): any {
    return {
      patient_id: frontendPatient.id,
      first_name: frontendPatient.personalInfo.firstName,
      last_name: frontendPatient.personalInfo.lastName,
      id_number: frontendPatient.personalInfo.idNumber,
      phone_number: frontendPatient.personalInfo.phone,
      email: frontendPatient.personalInfo.email,
      date_of_birth: frontendPatient.personalInfo.dateOfBirth,
      age: frontendPatient.personalInfo.age,
      gender: frontendPatient.personalInfo.gender,
      address: frontendPatient.personalInfo.address,
      emergency_contact_name: frontendPatient.personalInfo.emergencyContact?.name,
      emergency_contact_phone: frontendPatient.personalInfo.emergencyContact?.phone,
      emergency_contact_relationship: frontendPatient.personalInfo.emergencyContact?.relationship,
      medical_history: frontendPatient.medicalHistory?.map((condition: any) => ({
        condition_name: condition.condition,
        severity: condition.severity,
        status: condition.status,
        diagnosed_date: condition.diagnosedDate,
        notes: condition.notes
      })) || []
    };
  }

  // Public API methods that intelligently route to enhanced or existing services

  /**
   * Create or update patient - Enhanced version
   */
  async createPatient(patientData: any): Promise<any> {
    if (this.status.enhancedFeaturesEnabled) {
      console.log('🔧 Enhanced Integration: Using enhanced patient creation');
      try {
        const response = await enhancedPatientService.createPatient(patientData);
        
        // Also update frontend services for consistency
        if (response.success) {
          await this.updateFrontendServices(patientData);
        }
        
        return response;
      } catch (error) {
        console.warn('⚠️ Enhanced Integration: Enhanced creation failed, falling back');
        return this.createPatientFallback(patientData);
      }
    } else {
      return this.createPatientFallback(patientData);
    }
  }

  private async createPatientFallback(patientData: any): Promise<any> {
    console.log('🔄 Enhanced Integration: Using existing patient creation');
    // Use existing patient API service
    return patientApiService.sendPatientToBackend(patientData);
  }

  private async updateFrontendServices(patientData: any) {
    // Update existing frontend services to maintain consistency
    // This ensures the existing UI components continue to work
    try {
      // Convert enhanced format back to frontend format if needed
      // Implementation depends on the specific data structures
      console.log('🔄 Enhanced Integration: Updating frontend services for consistency');
    } catch (error) {
      console.warn('⚠️ Enhanced Integration: Frontend service update failed:', error);
    }
  }

  /**
   * Get patient by ID number - Enhanced version
   */
  async getPatientByIdNumber(idNumber: string): Promise<any> {
    if (this.status.enhancedFeaturesEnabled) {
      console.log('🔧 Enhanced Integration: Using enhanced patient lookup');
      try {
        const response = await enhancedPatientService.getPatientByIdNumber(idNumber);
        
        if (response.success && response.data) {
          return {
            success: true,
            patient: response.data,
            source: 'enhanced'
          };
        }
      } catch (error) {
        console.warn('⚠️ Enhanced Integration: Enhanced lookup failed, falling back');
      }
    }
    
    // Fallback to existing services
    console.log('🔄 Enhanced Integration: Using existing patient lookup');
    const result = await patientApiService.getPatientByIdNumber(idNumber, 'PROV001');
    return {
      success: result !== null,
      patient: result,
      source: 'existing'
    };
  }

  /**
   * Search patients - Enhanced version
   */
  async searchPatients(searchTerm: string = ''): Promise<any> {
    if (this.status.enhancedFeaturesEnabled) {
      console.log('🔧 Enhanced Integration: Using enhanced patient search');
      try {
        const response = await enhancedPatientService.searchPatients(searchTerm);
        
        if (response.success && response.data) {
          return {
            success: true,
            patients: response.data.patients,
            count: response.data.count,
            source: 'enhanced'
          };
        }
      } catch (error) {
        console.warn('⚠️ Enhanced Integration: Enhanced search failed, falling back');
      }
    }
    
    // Fallback to existing services
    console.log('🔄 Enhanced Integration: Using existing patient search');
    const results = await patientApiService.searchPatients({
      query: searchTerm,
      searchType: 'all',
      providerId: 'PROV001'
    });
    
    return {
      success: true,
      patients: results,
      count: results.length,
      source: 'existing'
    };
  }

  /**
   * Get integration status
   */
  getStatus(): IntegrationStatus {
    return { ...this.status };
  }

  /**
   * Force reconnection to enhanced backend
   */
  async reconnect(): Promise<boolean> {
    console.log('🔄 Enhanced Integration: Forcing reconnection...');
    
    try {
      const connected = await enhancedApiClient.reconnect();
      
      if (connected) {
        this.status.backendConnected = true;
        this.status.enhancedFeaturesEnabled = true;
        this.status.lastHealthCheck = new Date().toISOString();
        
        // Re-sync data
        await this.syncFrontendDataToBackend();
        
        console.log('✅ Enhanced Integration: Reconnection successful');
        return true;
      } else {
        this.fallbackToExistingServices();
        console.warn('⚠️ Enhanced Integration: Reconnection failed');
        return false;
      }
    } catch (error) {
      console.error('❌ Enhanced Integration: Reconnection error:', error);
      this.fallbackToExistingServices();
      return false;
    }
  }

  /**
   * Test all integrations
   */
  async testIntegrations(): Promise<any> {
    console.log('🧪 Enhanced Integration: Testing all integrations...');
    
    const results = {
      enhancedBackend: false,
      existingServices: false,
      dataConsistency: false,
      timestamp: new Date().toISOString()
    };
    
    // Test enhanced backend
    try {
      const healthResponse = await enhancedApiClient.healthCheck();
      results.enhancedBackend = healthResponse.success;
    } catch (error) {
      console.error('❌ Enhanced backend test failed:', error);
    }
    
    // Test existing services
    try {
      const patients = patientRegistrationService.getAllRegisteredPatients();
      results.existingServices = patients.length >= 0;
    } catch (error) {
      console.error('❌ Existing services test failed:', error);
    }
    
    // Test data consistency
    try {
      if (results.enhancedBackend && results.existingServices) {
        // Compare data between enhanced and existing services
        results.dataConsistency = this.status.dataSync;
      }
    } catch (error) {
      console.error('❌ Data consistency test failed:', error);
    }
    
    console.log('📊 Integration test results:', results);
    return results;
  }
}

// Create singleton instance
export const enhancedIntegration = EnhancedIntegrationService.getInstance();

// Make available globally for debugging
(window as any).enhancedIntegration = enhancedIntegration;
