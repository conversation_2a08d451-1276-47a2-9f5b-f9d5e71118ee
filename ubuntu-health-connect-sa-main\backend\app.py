#!/usr/bin/env python3
"""
Ubuntu Health Connect SA - Main Flask Application
Restructured backend with proper MVC architecture
"""

import os
import sys
import logging
from flask import Flask

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.config import config

def main():
    """
    Main application entry point
    """
    # Get configuration environment
    config_name = os.environ.get('FLASK_ENV', 'development')
    
    # Create Flask application
    app = create_app(config[config_name])
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("🚀 Ubuntu Health Connect SA Backend starting...")
    logger.info(f"📊 Environment: {config_name}")
    logger.info(f"🔧 Debug mode: {app.config['DEBUG']}")
    logger.info("✅ Backend API server ready for frontend connections!")
    
    return app

# Create app instance
app = main()

if __name__ == '__main__':
    # Validate configuration
    try:
        config_name = os.environ.get('FLASK_ENV', 'development')
        config[config_name].validate_config()
        print("✅ Configuration validated successfully")
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        print("⚠️ Continuing without OpenAI API key - some features may not work")
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        print("⚠️ Continuing anyway...")
    
    # Start the server
    print("🌐 Starting server on http://localhost:5001")
    print("📱 Frontend can connect from http://localhost:8085")

    app.run(
        host='0.0.0.0',
        port=5001,
        debug=True,
        use_reloader=False  # Prevent double startup in debug mode
    )
