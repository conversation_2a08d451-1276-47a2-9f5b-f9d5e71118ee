const http = require('http');
const https = require('https');
const fs = require('fs');
const url = require('url');

// Simple file-based database
const DB_FILE = 'patients_db.json';

// Initialize database file if it doesn't exist
function initDatabase() {
  if (!fs.existsSync(DB_FILE)) {
    fs.writeFileSync(DB_FILE, JSON.stringify({ patients: [] }, null, 2));
    console.log('✅ Database file created:', DB_FILE);
  }
}

// Read database
function readDatabase() {
  try {
    const data = fs.readFileSync(DB_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('❌ Error reading database:', error);
    return { patients: [] };
  }
}

// Write database
function writeDatabase(data) {
  try {
    fs.writeFileSync(DB_FILE, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error('❌ Error writing database:', error);
    return false;
  }
}

// Generate patient ID
function generatePatientId() {
  return `PAT_${Date.now()}_${Math.random().toString(36).substring(2, 7).toUpperCase()}`;
}

// Normalize phone number to consistent format
function normalizePhoneNumber(phone) {
  if (!phone) return '';

  // Remove all spaces and special characters except +
  let normalized = phone.replace(/[\s\-\(\)]/g, '');

  // If starts with 0, replace with +27
  if (normalized.startsWith('0')) {
    normalized = '+27' + normalized.substring(1);
  }

  // If doesn't start with +27, add it (assuming SA number)
  if (!normalized.startsWith('+27') && normalized.length === 9) {
    normalized = '+27' + normalized;
  }

  return normalized;
}

// CORS headers
function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
}

// Send JSON response
function sendJSON(res, statusCode, data) {
  setCORSHeaders(res);
  res.writeHead(statusCode, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify(data));
}

// Parse JSON body
function parseBody(req, callback) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });
  req.on('end', () => {
    try {
      const data = body ? JSON.parse(body) : {};
      callback(null, data);
    } catch (error) {
      callback(error, null);
    }
  });
}

// Initialize database
console.log('🔧 Initializing database...');
initDatabase();
console.log('✅ Database initialized');

// Create server
console.log('🔧 Creating HTTP server...');
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    setCORSHeaders(res);
    res.writeHead(200);
    res.end();
    return;
  }

  // Health check
  if (path === '/health' && method === 'GET') {
    const db = readDatabase();
    sendJSON(res, 200, {
      status: 'healthy',
      service: 'Patient Authentication API - Simple Node.js',
      timestamp: new Date().toISOString(),
      database: {
        connected: true,
        total_patients: db.patients.length,
        file: DB_FILE
      }
    });
    return;
  }

  // Register patient
  if (path === '/api/patients/register' && method === 'POST') {
    parseBody(req, (err, data) => {
      if (err) {
        sendJSON(res, 400, {
          success: false,
          error: 'Invalid JSON',
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Validate required fields
      const requiredFields = ['first_name', 'last_name', 'id_number', 'phone_number'];
      for (const field of requiredFields) {
        if (!data[field]) {
          sendJSON(res, 400, {
            success: false,
            error: `${field} is required`,
            timestamp: new Date().toISOString()
          });
          return;
        }
      }

      // Read current database
      const db = readDatabase();

      // Normalize phone number for consistent storage and comparison
      const normalizedPhone = normalizePhoneNumber(data.phone_number);

      // Check if patient already exists
      const existingPatient = db.patients.find(p =>
        p.id_number === data.id_number || p.phone_number === normalizedPhone
      );

      if (existingPatient) {
        sendJSON(res, 400, {
          success: false,
          error: 'Patient already registered with this SA ID or phone number',
          existing_patient_id: existingPatient.patient_id,
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Create new patient
      const patientId = data.patient_id || generatePatientId();
      const newPatient = {
        patient_id: patientId,
        id_number: data.id_number,
        phone_number: normalizedPhone,
        first_name: data.first_name,
        last_name: data.last_name,
        email: data.email || '',
        date_of_birth: data.date_of_birth || '',
        age: data.age || 0,
        gender: data.gender || '',
        address: data.address || '',
        emergency_contact_name: data.emergency_contact_name || '',
        emergency_contact_phone: data.emergency_contact_phone || '',
        emergency_contact_relationship: data.emergency_contact_relationship || '',
        medical_aid_provider: data.medical_aid_provider || '',
        medical_aid_number: data.medical_aid_number || '',
        registration_date: new Date().toISOString(),
        last_login: null,
        is_active: true
      };

      // Add to database
      db.patients.push(newPatient);

      // Save database
      if (writeDatabase(db)) {
        console.log(`✅ Patient registered: ${patientId} (${data.first_name} ${data.last_name})`);
        
        sendJSON(res, 201, {
          success: true,
          data: {
            patient_id: patientId,
            action: 'created',
            message: 'Patient registered successfully'
          },
          timestamp: new Date().toISOString()
        });
      } else {
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to save patient data',
          timestamp: new Date().toISOString()
        });
      }
    });
    return;
  }

  // Login patient
  if (path === '/api/patients/login' && method === 'POST') {
    parseBody(req, (err, data) => {
      if (err) {
        sendJSON(res, 400, {
          success: false,
          error: 'Invalid JSON',
          timestamp: new Date().toISOString()
        });
        return;
      }

      const { sa_id_number, phone_number } = data;

      if (!sa_id_number || !phone_number) {
        sendJSON(res, 400, {
          success: false,
          error: 'SA ID number and phone number are required',
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Read database
      const db = readDatabase();

      // Normalize the input phone number for comparison
      const normalizedInputPhone = normalizePhoneNumber(phone_number);

      console.log(`🔍 Login attempt: SA ID ${sa_id_number}, Input Phone: ${phone_number}, Normalized: ${normalizedInputPhone}`);

      // Find patient
      const patient = db.patients.find(p =>
        p.id_number === sa_id_number &&
        p.phone_number === normalizedInputPhone &&
        p.is_active
      );

      if (!patient) {
        // Debug: Show all patients for troubleshooting
        console.log('🔍 Available patients in database:');
        db.patients.forEach(p => {
          console.log(`  - ID: ${p.id_number}, Phone: ${p.phone_number}, Name: ${p.first_name} ${p.last_name}`);
        });
      }

      if (patient) {
        // Update last login
        patient.last_login = new Date().toISOString();
        writeDatabase(db);

        console.log(`✅ Patient authenticated: ${patient.patient_id} (${patient.first_name} ${patient.last_name})`);

        sendJSON(res, 200, {
          success: true,
          data: {
            patient: patient,
            message: 'Login successful'
          },
          timestamp: new Date().toISOString()
        });
      } else {
        console.log(`❌ Authentication failed: No patient found with SA ID ${sa_id_number} and phone ${phone_number}`);

        sendJSON(res, 401, {
          success: false,
          error: 'Invalid credentials or patient not found',
          timestamp: new Date().toISOString()
        });
      }
    });
    return;
  }

  // Get all patients with search functionality
  if (path === '/api/patients' && method === 'GET') {
    const db = readDatabase();
    const activePatients = db.patients.filter(p => p.is_active);

    // Get search parameter from URL
    const urlParams = new URL(req.url, `http://${req.headers.host}`);
    const searchTerm = urlParams.searchParams.get('search') || '';

    let filteredPatients = activePatients;

    // Apply search filter if provided
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filteredPatients = activePatients.filter(p =>
        p.first_name.toLowerCase().includes(searchLower) ||
        p.last_name.toLowerCase().includes(searchLower) ||
        p.id_number.includes(searchTerm) ||
        p.phone_number.includes(searchTerm) ||
        p.patient_id.toLowerCase().includes(searchLower) ||
        p.email.toLowerCase().includes(searchLower)
      );

      console.log(`🔍 Search for "${searchTerm}" returned ${filteredPatients.length} results`);
    }

    sendJSON(res, 200, {
      success: true,
      data: {
        patients: filteredPatients,
        count: filteredPatients.length,
        total_patients: activePatients.length,
        search_term: searchTerm
      },
      timestamp: new Date().toISOString()
    });
    return;
  }

  // OpenAI API Proxy
  if (path === '/api/openai/chat' && method === 'POST') {
    parseBody(req, (err, data) => {
      if (err) {
        sendJSON(res, 400, {
          success: false,
          error: 'Invalid JSON',
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Get OpenAI API key from environment or hardcoded fallback
      const openaiApiKey = process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************';

      if (!openaiApiKey) {
        sendJSON(res, 500, {
          success: false,
          error: 'OpenAI API key not configured',
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Prepare OpenAI request
      const openaiData = JSON.stringify({
        model: data.model || 'gpt-4o-mini',
        messages: data.messages || [],
        max_tokens: data.max_tokens || 500,
        temperature: data.temperature || 0.7
      });

      const options = {
        hostname: 'api.openai.com',
        port: 443,
        path: '/v1/chat/completions',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${openaiApiKey}`,
          'Content-Length': Buffer.byteLength(openaiData)
        }
      };

      console.log('📤 Proxying request to OpenAI API...');

      const openaiReq = https.request(options, (openaiRes) => {
        let responseData = '';

        openaiRes.on('data', (chunk) => {
          responseData += chunk;
        });

        openaiRes.on('end', () => {
          try {
            const parsedResponse = JSON.parse(responseData);

            if (openaiRes.statusCode === 200) {
              console.log('✅ OpenAI API response received successfully');
              sendJSON(res, 200, {
                success: true,
                data: parsedResponse,
                timestamp: new Date().toISOString()
              });
            } else {
              console.log('❌ OpenAI API error:', openaiRes.statusCode, parsedResponse);
              sendJSON(res, openaiRes.statusCode, {
                success: false,
                error: parsedResponse.error || 'OpenAI API error',
                timestamp: new Date().toISOString()
              });
            }
          } catch (parseError) {
            console.error('❌ Error parsing OpenAI response:', parseError);
            sendJSON(res, 500, {
              success: false,
              error: 'Failed to parse OpenAI response',
              timestamp: new Date().toISOString()
            });
          }
        });
      });

      openaiReq.on('error', (error) => {
        console.error('❌ OpenAI API request error:', error);
        sendJSON(res, 500, {
          success: false,
          error: 'Failed to connect to OpenAI API',
          timestamp: new Date().toISOString()
        });
      });

      openaiReq.write(openaiData);
      openaiReq.end();
    });
    return;
  }

  // 404 for other routes
  sendJSON(res, 404, {
    success: false,
    error: 'Route not found',
    timestamp: new Date().toISOString()
  });
});

const PORT = 5000;

// Add error handling
server.on('error', (error) => {
  console.error('❌ Server error:', error);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught exception:', error);
  console.error('Stack:', error.stack);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled rejection at:', promise, 'reason:', reason);
});

server.listen(PORT, '0.0.0.0', () => {
  console.log(`🌐 Patient Authentication API running on http://localhost:${PORT}`);
  console.log('📱 Frontend can connect from http://localhost:8081');
  console.log('🔐 Features: Registration, Login, Patient Management, OpenAI Proxy');
  console.log('💾 Database: File-based JSON storage');
  console.log('🤖 OpenAI Proxy: /api/openai/chat');
});
