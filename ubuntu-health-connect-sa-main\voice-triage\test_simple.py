"""
Comprehensive Voice Triage System Testing Script
Tests all components including API endpoints, AI assessment, and integrations
"""

import os
import sys
import json
import requests
import time
from flask import Flask, jsonify, request
from datetime import datetime
from typing import Dict, List, Any

# Add the voice-triage directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Simple Flask app for testing
app = Flask(__name__)

# Test configuration
TEST_CONFIG = {
    'base_url': 'http://localhost:5000',
    'test_phone': '+27727803582',  # Your actual number
    'test_patient_id': 'test-patient-001',
    'test_scenarios': [
        {
            'name': 'Low Risk - Common Cold',
            'symptoms': ['runny nose', 'mild headache', 'fatigue'],
            'expected_risk': 'low'
        },
        {
            'name': 'Medium Risk - Fever and Cough',
            'symptoms': ['fever', 'persistent cough', 'body aches'],
            'expected_risk': 'medium'
        },
        {
            'name': 'High Risk - Severe Symptoms',
            'symptoms': ['severe headache', 'high fever', 'difficulty swallowing'],
            'expected_risk': 'high'
        },
        {
            'name': 'Critical Risk - Emergency',
            'symptoms': ['chest pain', 'difficulty breathing', 'loss of consciousness'],
            'expected_risk': 'critical'
        }
    ]
}

# Testing utility functions
def run_test_suite():
    """Run comprehensive test suite for Voice Triage System"""
    print("🧪 Starting Voice Triage System Test Suite")
    print("=" * 60)

    test_results = {
        'total_tests': 0,
        'passed': 0,
        'failed': 0,
        'results': []
    }

    # Test 1: Health Check
    result = test_health_endpoint()
    test_results['results'].append(result)
    test_results['total_tests'] += 1
    if result['passed']:
        test_results['passed'] += 1
    else:
        test_results['failed'] += 1

    # Test 2: Mock Assessment Scenarios
    for scenario in TEST_CONFIG['test_scenarios']:
        result = test_assessment_scenario(scenario)
        test_results['results'].append(result)
        test_results['total_tests'] += 1
        if result['passed']:
            test_results['passed'] += 1
        else:
            test_results['failed'] += 1

    # Test 3: Voice Call Simulation
    result = test_voice_call_simulation()
    test_results['results'].append(result)
    test_results['total_tests'] += 1
    if result['passed']:
        test_results['passed'] += 1
    else:
        test_results['failed'] += 1

    # Print results
    print_test_results(test_results)
    return test_results

def test_health_endpoint():
    """Test the health check endpoint"""
    try:
        response = requests.get(f"{TEST_CONFIG['base_url']}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            return {
                'test_name': 'Health Check Endpoint',
                'passed': True,
                'message': f"✅ Health check passed - Status: {data.get('status', 'unknown')}",
                'details': data
            }
        else:
            return {
                'test_name': 'Health Check Endpoint',
                'passed': False,
                'message': f"❌ Health check failed - Status code: {response.status_code}",
                'details': {'status_code': response.status_code}
            }
    except Exception as e:
        return {
            'test_name': 'Health Check Endpoint',
            'passed': False,
            'message': f"❌ Health check failed - Error: {str(e)}",
            'details': {'error': str(e)}
        }

def test_assessment_scenario(scenario):
    """Test AI assessment with specific symptom scenario"""
    try:
        payload = {
            'symptoms': scenario['symptoms'],
            'patient_id': TEST_CONFIG['test_patient_id']
        }

        response = requests.post(
            f"{TEST_CONFIG['base_url']}/api/voice-triage/mock-assessment",
            json=payload,
            timeout=10
        )

        if response.status_code == 200:
            data = response.json()
            assessment = data.get('assessment', {})
            risk_level = assessment.get('risk_level', 'unknown')

            # Check if risk level matches expected
            risk_match = risk_level == scenario['expected_risk']

            return {
                'test_name': f"Assessment - {scenario['name']}",
                'passed': risk_match,
                'message': f"{'✅' if risk_match else '⚠️'} Risk: {risk_level} (expected: {scenario['expected_risk']})",
                'details': {
                    'symptoms': scenario['symptoms'],
                    'risk_level': risk_level,
                    'urgency_score': assessment.get('urgency_score'),
                    'care_level': assessment.get('recommended_care_level')
                }
            }
        else:
            return {
                'test_name': f"Assessment - {scenario['name']}",
                'passed': False,
                'message': f"❌ Assessment failed - Status: {response.status_code}",
                'details': {'status_code': response.status_code}
            }
    except Exception as e:
        return {
            'test_name': f"Assessment - {scenario['name']}",
            'passed': False,
            'message': f"❌ Assessment failed - Error: {str(e)}",
            'details': {'error': str(e)}
        }

def test_voice_call_simulation():
    """Test voice call simulation"""
    try:
        payload = {
            'phone_number': TEST_CONFIG['test_phone']
        }

        response = requests.post(
            f"{TEST_CONFIG['base_url']}/api/voice-triage/mock-call",
            json=payload,
            timeout=10
        )

        if response.status_code == 200:
            data = response.json()
            return {
                'test_name': 'Voice Call Simulation',
                'passed': data.get('success', False),
                'message': f"✅ Call simulation successful - ID: {data.get('call_id', 'unknown')}",
                'details': data
            }
        else:
            return {
                'test_name': 'Voice Call Simulation',
                'passed': False,
                'message': f"❌ Call simulation failed - Status: {response.status_code}",
                'details': {'status_code': response.status_code}
            }
    except Exception as e:
        return {
            'test_name': 'Voice Call Simulation',
            'passed': False,
            'message': f"❌ Call simulation failed - Error: {str(e)}",
            'details': {'error': str(e)}
        }

def print_test_results(results):
    """Print formatted test results"""
    print("\n" + "=" * 60)
    print("🧪 TEST RESULTS SUMMARY")
    print("=" * 60)

    for result in results['results']:
        print(f"{result['message']}")
        if not result['passed'] and 'error' in result.get('details', {}):
            print(f"   Error: {result['details']['error']}")

    print("\n" + "-" * 60)
    print(f"📊 TOTAL: {results['total_tests']} | ✅ PASSED: {results['passed']} | ❌ FAILED: {results['failed']}")

    success_rate = (results['passed'] / results['total_tests']) * 100 if results['total_tests'] > 0 else 0
    print(f"🎯 SUCCESS RATE: {success_rate:.1f}%")

    if results['failed'] == 0:
        print("🎉 ALL TESTS PASSED! Voice Triage System is ready!")
    else:
        print("⚠️ Some tests failed. Check the details above.")

    print("=" * 60)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'message': 'Voice Triage System is running!',
        'services': {
            'flask_server': True,
            'basic_functionality': True,
            'ready_for_integration': True
        }
    })

@app.route('/test-suite', methods=['GET'])
def run_test_suite_endpoint():
    """Run test suite via HTTP endpoint"""
    try:
        # Wait a moment for server to be ready
        time.sleep(1)
        results = run_test_suite()
        return jsonify({
            'success': True,
            'test_results': results,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/voice-triage/test', methods=['GET', 'POST'])
def test_endpoint():
    """Test endpoint for basic functionality"""
    if request.method == 'POST':
        data = request.get_json() or {}
        return jsonify({
            'success': True,
            'message': 'Voice Triage API is working!',
            'received_data': data,
            'timestamp': datetime.now().isoformat()
        })
    else:
        return jsonify({
            'message': 'Voice Triage Test Endpoint',
            'methods': ['GET', 'POST'],
            'status': 'ready'
        })

@app.route('/api/voice-triage/mock-assessment', methods=['POST'])
def mock_assessment():
    """Mock AI triage assessment for testing"""
    data = request.get_json() or {}
    
    symptoms = data.get('symptoms', ['headache'])
    patient_id = data.get('patient_id', 'test-patient')
    
    # Mock AI response based on symptoms
    if any(symptom in ['chest pain', 'difficulty breathing', 'unconscious'] for symptom in symptoms):
        risk_level = 'critical'
        urgency_score = 9
        care_level = 'emergency'
    elif any(symptom in ['severe headache', 'high fever', 'severe pain'] for symptom in symptoms):
        risk_level = 'high'
        urgency_score = 7
        care_level = 'urgent_care'
    elif any(symptom in ['headache', 'fever', 'fatigue'] for symptom in symptoms):
        risk_level = 'medium'
        urgency_score = 5
        care_level = 'clinic'
    else:
        risk_level = 'low'
        urgency_score = 3
        care_level = 'self_care'
    
    mock_response = {
        'success': True,
        'patient_id': patient_id,
        'assessment': {
            'risk_level': risk_level,
            'urgency_score': urgency_score,
            'symptoms_analyzed': symptoms,
            'suspected_conditions': [
                {
                    'condition': 'Viral upper respiratory infection' if 'headache' in symptoms else 'General malaise',
                    'probability': 0.7,
                    'reasoning': 'Based on reported symptoms and common presentations'
                }
            ],
            'immediate_actions': [
                'Rest and stay hydrated',
                'Monitor symptoms',
                'Take appropriate pain relief if needed'
            ],
            'recommended_care_level': care_level,
            'follow_up_required': risk_level in ['medium', 'high', 'critical'],
            'ai_confidence': 0.85
        },
        'timestamp': datetime.now().isoformat(),
        'note': 'This is a mock assessment for testing purposes'
    }
    
    return jsonify(mock_response)

@app.route('/api/voice-triage/mock-call', methods=['POST'])
def mock_call():
    """Mock voice call for testing"""
    data = request.get_json() or {}
    phone_number = data.get('phone_number', '+27123456789')
    
    return jsonify({
        'success': True,
        'call_id': f'mock_call_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'phone_number': phone_number,
        'status': 'initiated',
        'message': f'Mock call initiated to {phone_number}',
        'timestamp': datetime.now().isoformat(),
        'note': 'This is a mock call for testing purposes'
    })

@app.route('/', methods=['GET'])
def home():
    """Home endpoint"""
    return jsonify({
        'message': 'Ubuntu Health Voice Triage System',
        'version': '1.0.0',
        'status': 'running',
        'endpoints': {
            'health': '/health',
            'test': '/api/voice-triage/test',
            'mock_assessment': '/api/voice-triage/mock-assessment',
            'mock_call': '/api/voice-triage/mock-call'
        },
        'ubuntu_message': 'Ubuntu ngumuntu ngabantu - I am because we are'
    })

if __name__ == '__main__':
    import threading
    import argparse

    parser = argparse.ArgumentParser(description='Voice Triage System Testing')
    parser.add_argument('--test-only', action='store_true', help='Run tests only without starting server')
    parser.add_argument('--server-only', action='store_true', help='Start server only without running tests')
    args = parser.parse_args()

    print("🚀 Ubuntu Health Voice Triage System")
    print("🌍 Ubuntu Philosophy: 'I am because we are'")
    print("=" * 60)

    # Check environment setup
    env_file = os.path.join(os.path.dirname(__file__), '.env')
    if os.path.exists(env_file):
        print("✅ Found .env file")
    else:
        print("⚠️ No .env file found - using defaults")

    if args.test_only:
        # Run tests only (assumes server is already running)
        print("🧪 Running tests against existing server...")
        time.sleep(2)  # Wait for server to be ready
        run_test_suite()
    elif args.server_only:
        # Start server only
        print("🔗 Starting server only...")
        print("Available endpoints:")
        print("   - http://localhost:5000/health")
        print("   - http://localhost:5000/test-suite")
        print("   - http://localhost:5000/api/voice-triage/test")
        print("   - http://localhost:5000/api/voice-triage/mock-assessment")
        print("   - http://localhost:5000/api/voice-triage/mock-call")
        print("=" * 60)

        app.run(host='0.0.0.0', port=5000, debug=True)
    else:
        # Default: Start server and run tests
        print("🔗 Starting server and running tests...")
        print("Available endpoints:")
        print("   - http://localhost:5000/health")
        print("   - http://localhost:5000/test-suite")
        print("   - http://localhost:5000/api/voice-triage/test")
        print("   - http://localhost:5000/api/voice-triage/mock-assessment")
        print("   - http://localhost:5000/api/voice-triage/mock-call")
        print("=" * 60)

        # Start server in a separate thread
        server_thread = threading.Thread(
            target=lambda: app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False)
        )
        server_thread.daemon = True
        server_thread.start()

        # Wait for server to start
        print("⏳ Waiting for server to start...")
        time.sleep(3)

        # Run tests
        try:
            run_test_suite()
        except KeyboardInterrupt:
            print("\n🛑 Testing interrupted by user")
        except Exception as e:
            print(f"\n❌ Testing failed: {str(e)}")

        print("\n🔗 Server is still running at http://localhost:5000")
        print("Press Ctrl+C to stop the server")

        try:
            # Keep the main thread alive
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Server stopped by user")
