#!/usr/bin/env node
/**
 * Health Check Script
 * Verifies that both frontend and backend are running and properly connected
 */

const http = require('http');
const https = require('https');
require('dotenv').config();

// Configuration from environment variables
const FRONTEND_PORT = process.env.VITE_FRONTEND_PORT || '8085';
const BACKEND_PORT = process.env.VITE_BACKEND_PORT || '5001';
const BACKEND_HOST = process.env.VITE_BACKEND_HOST || 'localhost';

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkUrl(url, name) {
  return new Promise((resolve) => {
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      if (res.statusCode >= 200 && res.statusCode < 400) {
        log(`✅ ${name} is running at ${url}`, 'green');
        resolve(true);
      } else {
        log(`❌ ${name} returned status ${res.statusCode} at ${url}`, 'red');
        resolve(false);
      }
    });
    
    req.on('error', (error) => {
      log(`❌ ${name} is not accessible at ${url} - ${error.message}`, 'red');
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      log(`⏰ ${name} health check timed out at ${url}`, 'yellow');
      req.destroy();
      resolve(false);
    });
  });
}

async function healthCheck() {
  log('🏥 Ubuntu Health Connect SA - Health Check', 'bold');
  log('================================================', 'blue');
  
  // Configuration display
  log(`Frontend Port: ${FRONTEND_PORT}`, 'blue');
  log(`Backend Port: ${BACKEND_PORT}`, 'blue');
  log(`Backend Host: ${BACKEND_HOST}`, 'blue');
  log('', 'reset');
  
  // Check backend health endpoint
  const backendHealthUrl = `http://${BACKEND_HOST}:${BACKEND_PORT}/health`;
  const backendRunning = await checkUrl(backendHealthUrl, 'Backend Health');
  
  // Check backend AI interactions endpoint
  const backendApiUrl = `http://${BACKEND_HOST}:${BACKEND_PORT}/api/ai-interactions`;
  const backendApiRunning = await checkUrl(backendApiUrl, 'Backend API');
  
  // Check frontend (if running)
  const frontendUrl = `http://localhost:${FRONTEND_PORT}`;
  const frontendRunning = await checkUrl(frontendUrl, 'Frontend');
  
  log('', 'reset');
  log('📊 Health Check Summary:', 'bold');
  log('========================', 'blue');
  
  if (backendRunning && backendApiRunning) {
    log('✅ Backend is fully operational', 'green');
  } else {
    log('❌ Backend has issues', 'red');
  }
  
  if (frontendRunning) {
    log('✅ Frontend is accessible', 'green');
  } else {
    log('⚠️ Frontend is not running (this is normal if you haven\'t started it yet)', 'yellow');
  }
  
  if (backendRunning && backendApiRunning && frontendRunning) {
    log('', 'reset');
    log('🎉 All services are running! Your Ubuntu Health Connect SA is ready!', 'green');
    log(`🌐 Frontend: http://localhost:${FRONTEND_PORT}`, 'green');
    log(`🔧 Backend: http://${BACKEND_HOST}:${BACKEND_PORT}`, 'green');
  } else if (backendRunning && backendApiRunning) {
    log('', 'reset');
    log('✅ Backend is ready! Start the frontend with: npm run start:frontend:only', 'green');
  } else {
    log('', 'reset');
    log('❌ Some services are not running. Start all services with: npm run dev', 'red');
  }
  
  log('', 'reset');
  process.exit(backendRunning && backendApiRunning ? 0 : 1);
}

// Run health check
healthCheck().catch((error) => {
  log(`❌ Health check failed: ${error.message}`, 'red');
  process.exit(1);
});
