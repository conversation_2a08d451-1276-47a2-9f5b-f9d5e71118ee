"""
Configuration management for Ubuntu Health Connect SA Backend
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Base configuration class"""
    
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'ubuntu-health-connect-sa-secret-key'
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    # Database Configuration
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'sqlite:///ubuntu_health.db'
    DATABASE_RETENTION_DAYS = int(os.environ.get('DATABASE_RETENTION_DAYS', 2555))
    
    # OpenAI Configuration
    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
    OPENAI_MODEL = os.environ.get('OPENAI_MODEL', 'gpt-4o-mini')
    OPENAI_MAX_TOKENS = int(os.environ.get('OPENAI_MAX_TOKENS', 400))
    OPENAI_TEMPERATURE = float(os.environ.get('OPENAI_TEMPERATURE', 0.7))
    
    # WhatsApp Configuration
    TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
    TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')
    TWILIO_PHONE_NUMBER = os.environ.get('TWILIO_PHONE_NUMBER')
    
    # Healthcare Provider Configuration
    HEALTHCARE_PROVIDER_NAME = os.environ.get('HEALTHCARE_PROVIDER_NAME', 'HealthConnect SA')
    HEALTHCARE_PROVIDER_PHONE = os.environ.get('HEALTHCARE_PROVIDER_PHONE', '+***********')
    EMERGENCY_PHONE_NUMBER = os.environ.get('EMERGENCY_PHONE_NUMBER', '10177')
    
    # Feature Flags
    FEATURE_AI_RESPONSES = os.environ.get('FEATURE_AI_RESPONSES', 'True').lower() == 'true'
    FEATURE_MEDICATION_REMINDERS = os.environ.get('FEATURE_MEDICATION_REMINDERS', 'True').lower() == 'true'
    FEATURE_HEALTH_CHECKINS = os.environ.get('FEATURE_HEALTH_CHECKINS', 'True').lower() == 'true'
    FEATURE_EMERGENCY_DETECTION = os.environ.get('FEATURE_EMERGENCY_DETECTION', 'True').lower() == 'true'
    
    # API Configuration
    API_VERSION = 'v1'
    API_TITLE = 'Ubuntu Health Connect SA API'
    API_DESCRIPTION = 'Healthcare management system API for South African healthcare providers'
    
    # CORS Configuration
    CORS_ORIGINS = [
        'http://localhost:3000',
        'http://localhost:8081',
        'http://localhost:8082',
        'http://localhost:8085',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:8081',
        'http://127.0.0.1:8082',
        'http://127.0.0.1:8085'
    ]
    
    # Logging Configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', 'logs/ubuntu_health.log')
    
    @staticmethod
    def validate_config():
        """Validate required configuration"""
        required_vars = []
        
        if not Config.OPENAI_API_KEY:
            required_vars.append('OPENAI_API_KEY')
        
        if required_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(required_vars)}")
        
        return True

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    DATABASE_URL = 'sqlite:///ubuntu_health_dev.db'

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'sqlite:///ubuntu_health_prod.db'

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    DATABASE_URL = 'sqlite:///ubuntu_health_test.db'

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
