# Ubuntu Health Connect SA - Environment Configuration
# Copy this file to .env and customize as needed

# Frontend Configuration
VITE_FRONTEND_PORT=8085
VITE_BACKEND_PORT=5001
VITE_BACKEND_HOST=localhost

# Backend Configuration  
FLASK_PORT=5001
FLASK_HOST=0.0.0.0
FLASK_ENV=development
FLASK_DEBUG=true

# API Configuration
VITE_API_BASE_URL=http://localhost:5001
VITE_API_HEALTH_ENDPOINT=/health
VITE_API_PATIENTS_ENDPOINT=/api/patients
VITE_API_AI_INTERACTIONS_ENDPOINT=/api/ai-interactions

# OpenAI Configuration (Optional)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini

# Twilio Configuration (Optional)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Development Settings
AUTO_START_BACKEND=true
AUTO_OPEN_BROWSER=true
ENABLE_HOT_RELOAD=true

# Logging
LOG_LEVEL=info
ENABLE_CONSOLE_LOGS=true
