<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Urgent Cases Loading</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .urgent {
            border-left-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
        }
        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .urgent-btn {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        .result {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Frontend Urgent Cases Loading Test</h1>
        <p>This test verifies that the frontend is properly loading urgent cases from the backend.</p>
        
        <div class="test-section">
            <h3>Test 1: Backend API Direct Test</h3>
            <p>Test the backend urgent cases endpoint directly</p>
            <button onclick="testBackendAPI()">Test Backend API</button>
            <div id="backend-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>Test 2: Frontend AI Interactions Loading</h3>
            <p>Test if frontend can load AI interactions from backend</p>
            <button onclick="testFrontendLoading()">Test Frontend Loading</button>
            <div id="frontend-result" class="result"></div>
        </div>
        
        <div class="test-section urgent">
            <h3>Test 3: Patient Case Service Debug</h3>
            <p>Debug the patient case service state</p>
            <button class="urgent-btn" onclick="debugPatientCaseService()">Debug Case Service</button>
            <div id="debug-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>Test 4: Provider Dashboard Integration</h3>
            <p>Test if provider dashboard can access urgent cases</p>
            <button onclick="testProviderDashboard()">Test Dashboard Integration</button>
            <div id="dashboard-result" class="result"></div>
        </div>
        
        <div class="test-section urgent">
            <h3>Test 5: Complete Flow Verification</h3>
            <p>Verify the complete urgent case flow</p>
            <button class="urgent-btn" onclick="verifyCompleteFlow()">Verify Complete Flow</button>
            <div id="flow-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
        }
        
        async function testBackendAPI() {
            log('backend-result', '🔄 Testing backend urgent cases API...', 'info');
            
            try {
                // Test urgent cases endpoint
                const response = await fetch(`${API_BASE}/api/urgent-cases`);
                log('backend-result', `📡 Response status: ${response.status}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        const count = data.data.count;
                        log('backend-result', `✅ Backend API working: ${count} urgent cases found`, 'success');
                        
                        if (count > 0) {
                            data.data.urgent_cases.forEach((case_, index) => {
                                log('backend-result', `   ${index + 1}. ${case_.patient_name}: ${case_.severity}`, 'success');
                            });
                        }
                    } else {
                        log('backend-result', `❌ API error: ${data.error}`, 'error');
                    }
                } else {
                    log('backend-result', `❌ HTTP error: ${response.status}`, 'error');
                }
                
            } catch (error) {
                log('backend-result', `❌ Exception: ${error.message}`, 'error');
            }
        }
        
        async function testFrontendLoading() {
            log('frontend-result', '🔄 Testing frontend AI interactions loading...', 'info');
            
            try {
                // Test AI interactions endpoint (what frontend uses)
                const response = await fetch(`${API_BASE}/api/ai-interactions`);
                log('frontend-result', `📡 AI interactions status: ${response.status}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        const count = data.data.count;
                        log('frontend-result', `✅ AI interactions loaded: ${count} total`, 'success');
                        
                        const urgentCount = data.data.interactions.filter(i => i.urgent_care).length;
                        log('frontend-result', `🚨 Urgent interactions: ${urgentCount}`, urgentCount > 0 ? 'success' : 'warning');
                        
                        // Show interaction details
                        data.data.interactions.forEach((interaction, index) => {
                            if (interaction.urgent_care) {
                                log('frontend-result', `   🚨 ${index + 1}. ${interaction.patient_id}: ${interaction.interaction_type}`, 'success');
                            }
                        });
                    } else {
                        log('frontend-result', `❌ API error: ${data.error}`, 'error');
                    }
                } else {
                    log('frontend-result', `❌ HTTP error: ${response.status}`, 'error');
                }
                
            } catch (error) {
                log('frontend-result', `❌ Exception: ${error.message}`, 'error');
            }
        }
        
        function debugPatientCaseService() {
            log('debug-result', '🔍 Debugging patient case service...', 'info');
            
            try {
                // Check if patient case service is available
                if (typeof window.debugPatientCases === 'function') {
                    log('debug-result', '✅ Patient case service debug function found', 'success');
                    
                    // Capture console output
                    const originalLog = console.log;
                    let debugOutput = '';
                    console.log = (...args) => {
                        debugOutput += args.join(' ') + '\\n';
                        originalLog(...args);
                    };
                    
                    // Run debug
                    window.debugPatientCases();
                    
                    // Restore console.log
                    console.log = originalLog;
                    
                    log('debug-result', '📊 Debug output:', 'info');
                    log('debug-result', debugOutput, 'info');
                } else {
                    log('debug-result', '❌ Patient case service not available', 'error');
                    log('debug-result', '⚠️ This test needs to be run from the provider dashboard', 'warning');
                }
                
            } catch (error) {
                log('debug-result', `❌ Debug error: ${error.message}`, 'error');
            }
        }
        
        async function testProviderDashboard() {
            log('dashboard-result', '🏥 Testing provider dashboard integration...', 'info');
            
            try {
                // Check if we can access the provider dashboard
                const dashboardResponse = await fetch('http://localhost:8085');
                log('dashboard-result', `📡 Dashboard status: ${dashboardResponse.status}`, 'info');
                
                if (dashboardResponse.ok) {
                    log('dashboard-result', '✅ Provider dashboard accessible', 'success');
                    log('dashboard-result', '🔄 Dashboard should load urgent cases automatically', 'info');
                    log('dashboard-result', '📋 Check "Urgent Ubuntu Care" section for cases', 'info');
                } else {
                    log('dashboard-result', '❌ Provider dashboard not accessible', 'error');
                }
                
            } catch (error) {
                log('dashboard-result', `❌ Exception: ${error.message}`, 'error');
            }
        }
        
        async function verifyCompleteFlow() {
            log('flow-result', '🚀 Verifying complete urgent case flow...', 'info');
            
            try {
                // Step 1: Check backend has urgent cases
                log('flow-result', '1️⃣ Checking backend urgent cases...', 'info');
                const urgentResponse = await fetch(`${API_BASE}/api/urgent-cases`);
                
                if (urgentResponse.ok) {
                    const urgentData = await urgentResponse.json();
                    if (urgentData.success && urgentData.data.count > 0) {
                        log('flow-result', `✅ Backend has ${urgentData.data.count} urgent cases`, 'success');
                    } else {
                        log('flow-result', '❌ No urgent cases in backend', 'error');
                        return;
                    }
                } else {
                    log('flow-result', '❌ Backend urgent cases endpoint failed', 'error');
                    return;
                }
                
                // Step 2: Check AI interactions
                log('flow-result', '2️⃣ Checking AI interactions...', 'info');
                const aiResponse = await fetch(`${API_BASE}/api/ai-interactions`);
                
                if (aiResponse.ok) {
                    const aiData = await aiResponse.json();
                    if (aiData.success) {
                        const urgentAI = aiData.data.interactions.filter(i => i.urgent_care);
                        log('flow-result', `✅ AI interactions: ${urgentAI.length} urgent`, 'success');
                    } else {
                        log('flow-result', '❌ AI interactions failed', 'error');
                        return;
                    }
                } else {
                    log('flow-result', '❌ AI interactions endpoint failed', 'error');
                    return;
                }
                
                // Step 3: Verify frontend accessibility
                log('flow-result', '3️⃣ Checking frontend accessibility...', 'info');
                const frontendResponse = await fetch('http://localhost:8085');
                
                if (frontendResponse.ok) {
                    log('flow-result', '✅ Frontend accessible', 'success');
                } else {
                    log('flow-result', '❌ Frontend not accessible', 'error');
                    return;
                }
                
                log('flow-result', '🎉 COMPLETE FLOW VERIFIED!', 'success');
                log('flow-result', '📋 Next steps:', 'info');
                log('flow-result', '   1. Open provider dashboard: http://localhost:8085', 'info');
                log('flow-result', '   2. Navigate to "Urgent Ubuntu Care" tab', 'info');
                log('flow-result', '   3. Urgent cases should be visible with red flags', 'info');
                log('flow-result', '   4. Check browser console for any loading errors', 'info');
                
            } catch (error) {
                log('flow-result', `❌ Flow verification failed: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
