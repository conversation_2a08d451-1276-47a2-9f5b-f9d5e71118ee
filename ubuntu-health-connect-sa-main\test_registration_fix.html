<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Patient Registration Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #4CAF50, #FFC107, #FF5722);
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #45a049;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 Ubuntu Health Connect SA - Registration Test</h1>
        
        <div id="status" class="status info">
            Testing backend connection...
        </div>

        <button onclick="testBackendConnection()">Test Backend Connection</button>
        <button onclick="testPatientRegistration()">Test Patient Registration</button>
        
        <h2>Register Test Patient</h2>
        <form id="registrationForm">
            <div class="form-group">
                <label for="firstName">First Name *</label>
                <input type="text" id="firstName" value="Thebi" required>
            </div>
            
            <div class="form-group">
                <label for="lastName">Last Name *</label>
                <input type="text" id="lastName" value="Test" required>
            </div>
            
            <div class="form-group">
                <label for="idNumber">SA ID Number *</label>
                <input type="text" id="idNumber" value="9999999999995" required>
            </div>
            
            <div class="form-group">
                <label for="phone">Phone Number *</label>
                <input type="text" id="phone" value="**********" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="age">Age</label>
                <input type="number" id="age" value="25">
            </div>
            
            <div class="form-group">
                <label for="gender">Gender</label>
                <select id="gender">
                    <option value="Female" selected>Female</option>
                    <option value="Male">Male</option>
                    <option value="Other">Other</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="address">Address</label>
                <input type="text" id="address" value="Test Address, Cape Town">
            </div>
            
            <div class="form-group">
                <label for="emergencyName">Emergency Contact Name</label>
                <input type="text" id="emergencyName" value="Test Emergency">
            </div>
            
            <div class="form-group">
                <label for="emergencyPhone">Emergency Contact Phone</label>
                <input type="text" id="emergencyPhone" value="0888888885">
            </div>
            
            <div class="form-group">
                <label for="emergencyRelationship">Emergency Contact Relationship</label>
                <select id="emergencyRelationship">
                    <option value="Sibling" selected>Sibling</option>
                    <option value="Parent">Parent</option>
                    <option value="Spouse">Spouse</option>
                    <option value="Friend">Friend</option>
                </select>
            </div>
            
            <button type="submit">Register Patient</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        const BACKEND_URL = 'http://localhost:5000';
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        async function testBackendConnection() {
            updateStatus('Testing backend connection...', 'info');
            
            try {
                const response = await fetch(`${BACKEND_URL}/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    updateStatus(`✅ Backend connected! Service: ${data.service}, Version: ${data.version}`, 'success');
                    console.log('Backend response:', data);
                } else {
                    updateStatus(`❌ Backend connection failed: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus(`❌ Backend connection error: ${error.message}`, 'error');
                console.error('Connection error:', error);
            }
        }
        
        async function testPatientRegistration() {
            const testPatient = {
                first_name: 'Test',
                last_name: 'Registration',
                id_number: '9999999999994',
                phone_number: '**********',
                email: '<EMAIL>',
                age: 30,
                gender: 'Other',
                address: 'Test Address, Cape Town',
                emergency_contact_name: 'Test Emergency',
                emergency_contact_phone: '**********',
                emergency_contact_relationship: 'Friend'
            };
            
            updateStatus('Testing patient registration...', 'info');
            
            try {
                const response = await fetch(`${BACKEND_URL}/api/patients`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testPatient)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    updateStatus(`✅ Test registration successful! Patient ID: ${result.data.patient_id}`, 'success');
                    console.log('Registration result:', result);
                } else {
                    updateStatus(`❌ Test registration failed: ${result.error}`, 'error');
                    console.error('Registration error:', result);
                }
            } catch (error) {
                updateStatus(`❌ Registration error: ${error.message}`, 'error');
                console.error('Registration error:', error);
            }
        }
        
        document.getElementById('registrationForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                first_name: document.getElementById('firstName').value,
                last_name: document.getElementById('lastName').value,
                id_number: document.getElementById('idNumber').value,
                phone_number: document.getElementById('phone').value,
                email: document.getElementById('email').value,
                age: parseInt(document.getElementById('age').value) || null,
                gender: document.getElementById('gender').value,
                address: document.getElementById('address').value,
                emergency_contact_name: document.getElementById('emergencyName').value,
                emergency_contact_phone: document.getElementById('emergencyPhone').value,
                emergency_contact_relationship: document.getElementById('emergencyRelationship').value
            };
            
            updateStatus('Registering patient...', 'info');
            
            try {
                const response = await fetch(`${BACKEND_URL}/api/patients`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    updateStatus(`✅ Registration successful! Patient ID: ${result.data.patient_id}`, 'success');
                    document.getElementById('result').innerHTML = `
                        <div class="status success">
                            <h3>Registration Successful!</h3>
                            <p><strong>Patient ID:</strong> ${result.data.patient_id}</p>
                            <p><strong>Action:</strong> ${result.data.action}</p>
                            <p><strong>Message:</strong> ${result.data.message}</p>
                        </div>
                    `;
                    console.log('Registration result:', result);
                } else {
                    updateStatus(`❌ Registration failed: ${result.error}`, 'error');
                    document.getElementById('result').innerHTML = `
                        <div class="status error">
                            <h3>Registration Failed!</h3>
                            <p><strong>Error:</strong> ${result.error}</p>
                            <p><strong>Status:</strong> ${response.status}</p>
                        </div>
                    `;
                    console.error('Registration error:', result);
                }
            } catch (error) {
                updateStatus(`❌ Registration error: ${error.message}`, 'error');
                document.getElementById('result').innerHTML = `
                    <div class="status error">
                        <h3>Registration Error!</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
                console.error('Registration error:', error);
            }
        });
        
        // Test connection on page load
        window.addEventListener('load', testBackendConnection);
    </script>
</body>
</html>
