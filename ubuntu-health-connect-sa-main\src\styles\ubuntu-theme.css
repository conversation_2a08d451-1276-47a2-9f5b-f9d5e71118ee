/* Ubuntu HealthConnect SA - South African Cultural Theme */
/* Reflecting Ubuntu philosophy, Rainbow Nation spirit, and South African values */

:root {
  /* Ubuntu Color Palette - Inspired by South African Flag and Ubuntu Philosophy */
  --ubuntu-green: #228B22;        /* Forest Green - Growth, healing, nature */
  --ubuntu-gold: #FFD700;         /* Gold - Prosperity, wisdom, Ubuntu spirit */
  --ubuntu-orange: #FF8C00;       /* Dark Orange - Energy, warmth, community */
  --ubuntu-red: #DC143C;          /* Crimson - Strength, courage, life force */
  --ubuntu-blue: #000080;         /* Navy Blue - Trust, stability, depth */
  --ubuntu-indigo: #4B0082;       /* Indigo - Spirituality, tradition */
  --ubuntu-white: #FFFFFF;        /* White - Peace, unity, clarity */
  --ubuntu-black: #000000;        /* Black - Strength, dignity, heritage */
  
  /* Rainbow Nation Extended Palette */
  --rainbow-violet: #8A2BE2;      /* Blue Violet - Diversity */
  --rainbow-cyan: #00CED1;        /* Dark Turquoise - Harmony */
  --rainbow-lime: #32CD32;        /* Lime Green - Fresh beginnings */
  --rainbow-coral: #FF7F50;       /* Coral - Warmth, community */
  
  /* Ubuntu Gradients */
  --ubuntu-primary-gradient: linear-gradient(135deg, var(--ubuntu-green) 0%, var(--ubuntu-gold) 50%, var(--ubuntu-orange) 100%);
  --ubuntu-secondary-gradient: linear-gradient(135deg, var(--ubuntu-red) 0%, var(--ubuntu-blue) 50%, var(--ubuntu-indigo) 100%);
  --ubuntu-rainbow-gradient: linear-gradient(135deg, 
    var(--ubuntu-green) 0%, 
    var(--ubuntu-gold) 16%, 
    var(--ubuntu-orange) 32%, 
    var(--ubuntu-red) 48%, 
    var(--ubuntu-blue) 64%, 
    var(--ubuntu-indigo) 80%, 
    var(--rainbow-violet) 100%);
  
  /* Ubuntu Semantic Colors */
  --ubuntu-success: var(--ubuntu-green);
  --ubuntu-warning: var(--ubuntu-orange);
  --ubuntu-error: var(--ubuntu-red);
  --ubuntu-info: var(--ubuntu-blue);
  --ubuntu-accent: var(--ubuntu-gold);
  
  /* Ubuntu Typography */
  --ubuntu-font-family: 'Ubuntu', 'Segoe UI', 'Roboto', sans-serif;
  --ubuntu-font-weight-light: 300;
  --ubuntu-font-weight-normal: 400;
  --ubuntu-font-weight-medium: 500;
  --ubuntu-font-weight-bold: 700;
  --ubuntu-font-weight-black: 900;
  
  /* Ubuntu Spacing - Based on Ubuntu design principles */
  --ubuntu-space-xs: 0.25rem;     /* 4px */
  --ubuntu-space-sm: 0.5rem;      /* 8px */
  --ubuntu-space-md: 1rem;        /* 16px */
  --ubuntu-space-lg: 1.5rem;      /* 24px */
  --ubuntu-space-xl: 2rem;        /* 32px */
  --ubuntu-space-2xl: 3rem;       /* 48px */
  --ubuntu-space-3xl: 4rem;       /* 64px */
  
  /* Ubuntu Border Radius */
  --ubuntu-radius-sm: 0.5rem;     /* 8px */
  --ubuntu-radius-md: 1rem;       /* 16px */
  --ubuntu-radius-lg: 1.5rem;     /* 24px */
  --ubuntu-radius-xl: 2rem;       /* 32px */
  
  /* Ubuntu Shadows */
  --ubuntu-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --ubuntu-shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
  --ubuntu-shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
  --ubuntu-shadow-xl: 0 12px 48px rgba(0, 0, 0, 0.25);
  
  /* Ubuntu Glass Effect */
  --ubuntu-glass-bg: rgba(255, 255, 255, 0.1);
  --ubuntu-glass-border: rgba(255, 215, 0, 0.3);
  --ubuntu-glass-backdrop: blur(20px);
}

/* Ubuntu Base Styles */
.ubuntu-theme {
  font-family: var(--ubuntu-font-family);
  color: var(--ubuntu-black);
}

/* Ubuntu Background Patterns */
.ubuntu-pattern-dots {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255,215,0,0.2) 3px, transparent 3px),
    radial-gradient(circle at 50% 50%, rgba(255,255,255,0.05) 1px, transparent 1px);
  background-size: 120px 120px, 180px 180px, 90px 90px;
}

.ubuntu-pattern-waves {
  background-image: 
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 10px,
      rgba(255,215,0,0.1) 10px,
      rgba(255,215,0,0.1) 20px
    );
}

/* Ubuntu Button Styles */
.ubuntu-btn-primary {
  background: var(--ubuntu-primary-gradient);
  color: var(--ubuntu-white);
  border: 2px solid var(--ubuntu-gold);
  border-radius: var(--ubuntu-radius-lg);
  padding: var(--ubuntu-space-md) var(--ubuntu-space-xl);
  font-weight: var(--ubuntu-font-weight-black);
  box-shadow: var(--ubuntu-shadow-lg);
  transition: all 0.3s ease;
}

.ubuntu-btn-primary:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(255,215,0,0.4);
}

.ubuntu-btn-secondary {
  background: var(--ubuntu-secondary-gradient);
  color: var(--ubuntu-gold);
  border: 2px solid var(--ubuntu-gold);
  border-radius: var(--ubuntu-radius-lg);
  padding: var(--ubuntu-space-md) var(--ubuntu-space-xl);
  font-weight: var(--ubuntu-font-weight-black);
  box-shadow: var(--ubuntu-shadow-lg);
  transition: all 0.3s ease;
}

.ubuntu-btn-secondary:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(255,215,0,0.4);
}

/* Ubuntu Card Styles */
.ubuntu-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: var(--ubuntu-glass-backdrop);
  border: 3px solid var(--ubuntu-glass-border);
  border-radius: var(--ubuntu-radius-xl);
  box-shadow: var(--ubuntu-shadow-xl);
  padding: var(--ubuntu-space-xl);
  transition: all 0.5s ease;
}

.ubuntu-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 50px rgba(255,215,0,0.3);
}

.ubuntu-card-header {
  background: var(--ubuntu-glass-bg);
  border-bottom: 2px solid var(--ubuntu-glass-border);
  border-radius: var(--ubuntu-radius-lg) var(--ubuntu-radius-lg) 0 0;
  padding: var(--ubuntu-space-lg);
  backdrop-filter: var(--ubuntu-glass-backdrop);
}

/* Ubuntu Text Styles */
.ubuntu-heading-1 {
  font-size: 3rem;
  font-weight: var(--ubuntu-font-weight-black);
  color: var(--ubuntu-white);
  margin-bottom: var(--ubuntu-space-lg);
}

.ubuntu-heading-2 {
  font-size: 2rem;
  font-weight: var(--ubuntu-font-weight-black);
  color: var(--ubuntu-gold);
  margin-bottom: var(--ubuntu-space-md);
}

.ubuntu-heading-3 {
  font-size: 1.5rem;
  font-weight: var(--ubuntu-font-weight-black);
  color: var(--ubuntu-green);
  margin-bottom: var(--ubuntu-space-sm);
}

.ubuntu-text-body {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--ubuntu-black);
  font-weight: var(--ubuntu-font-weight-medium);
}

.ubuntu-text-blessing {
  font-style: italic;
  color: var(--ubuntu-gold);
  font-size: 1.1rem;
  text-align: center;
  padding: var(--ubuntu-space-lg);
  background: rgba(0,0,0,0.2);
  border-radius: var(--ubuntu-radius-lg);
  border: 1px solid var(--ubuntu-glass-border);
  font-weight: var(--ubuntu-font-weight-bold);
}

/* Ubuntu Badge Styles */
.ubuntu-badge-success {
  background: var(--ubuntu-green);
  color: var(--ubuntu-white);
  padding: var(--ubuntu-space-xs) var(--ubuntu-space-md);
  border-radius: var(--ubuntu-radius-sm);
  font-weight: var(--ubuntu-font-weight-black);
  box-shadow: var(--ubuntu-shadow-sm);
}

.ubuntu-badge-warning {
  background: var(--ubuntu-orange);
  color: var(--ubuntu-white);
  padding: var(--ubuntu-space-xs) var(--ubuntu-space-md);
  border-radius: var(--ubuntu-radius-sm);
  font-weight: var(--ubuntu-font-weight-black);
  box-shadow: var(--ubuntu-shadow-sm);
}

.ubuntu-badge-error {
  background: var(--ubuntu-red);
  color: var(--ubuntu-white);
  padding: var(--ubuntu-space-xs) var(--ubuntu-space-md);
  border-radius: var(--ubuntu-radius-sm);
  font-weight: var(--ubuntu-font-weight-black);
  box-shadow: var(--ubuntu-shadow-sm);
}

/* Ubuntu Animation Classes */
.ubuntu-pulse {
  animation: ubuntu-pulse 2s infinite;
}

@keyframes ubuntu-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.ubuntu-glow {
  animation: ubuntu-glow 3s ease-in-out infinite alternate;
}

@keyframes ubuntu-glow {
  from {
    box-shadow: 0 0 20px rgba(255,215,0,0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(255,215,0,0.6);
  }
}

/* Ubuntu Responsive Design */
@media (max-width: 768px) {
  .ubuntu-heading-1 {
    font-size: 2rem;
  }
  
  .ubuntu-heading-2 {
    font-size: 1.5rem;
  }
  
  .ubuntu-card {
    padding: var(--ubuntu-space-lg);
  }
  
  .ubuntu-btn-primary,
  .ubuntu-btn-secondary {
    padding: var(--ubuntu-space-sm) var(--ubuntu-space-lg);
    font-size: 0.9rem;
  }
}

/* Ubuntu Accessibility */
.ubuntu-focus:focus {
  outline: 3px solid var(--ubuntu-gold);
  outline-offset: 2px;
}

.ubuntu-high-contrast {
  filter: contrast(1.2);
}

/* Ubuntu Tab Styles */
.ubuntu-tab-trigger {
  color: white !important;
  font-weight: 900 !important;
  border: none !important;
  background: transparent !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  white-space: normal !important;
  word-wrap: break-word !important;
  line-height: 1.2 !important;
  min-height: 3rem !important;
}

.ubuntu-tab-trigger:hover {
  background: rgba(255,215,0,0.2) !important;
  color: #FFD700 !important;
  transform: scale(1.02) !important;
}

.ubuntu-tab-trigger[data-state="active"] {
  background: linear-gradient(135deg, #228B22, #FFD700) !important;
  color: white !important;
  font-weight: 900 !important;
  border: 2px solid #FFD700 !important;
  box-shadow: 0 4px 15px rgba(255,215,0,0.4) !important;
  transform: scale(1.05) !important;
}

.ubuntu-tab-trigger[data-state="active"]:hover {
  background: linear-gradient(135deg, #32CD32, #FFD700) !important;
  box-shadow: 0 6px 20px rgba(255,215,0,0.6) !important;
}

/* Ensure text visibility in all states */
.ubuntu-tab-trigger * {
  color: inherit !important;
  font-weight: inherit !important;
}

/* Tab text wrapping */
.ubuntu-tab-trigger span {
  display: block !important;
  word-break: break-word !important;
  hyphens: auto !important;
}

/* Ubuntu Print Styles */
@media print {
  .ubuntu-theme {
    background: white !important;
    color: black !important;
  }

  .ubuntu-card {
    border: 2px solid black !important;
    box-shadow: none !important;
  }

  .ubuntu-tab-trigger {
    color: black !important;
    background: white !important;
    border: 1px solid black !important;
  }
}
