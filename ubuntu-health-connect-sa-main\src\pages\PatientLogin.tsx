import React, { useState } from 'react';

interface PatientLoginProps {
  onBack: () => void;
  onLogin: (idNumber: string, phoneNumber: string) => void;
  onRegister?: () => void;
}

const PatientLogin = ({ onBack, onLogin, onRegister }: PatientLoginProps) => {
  const [idNumber, setIdNumber] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ idNumber?: string; phoneNumber?: string }>({});

  const validateIdNumber = (id: string) => {
    // Basic SA ID validation - 13 digits
    const idRegex = /^\d{13}$/;
    return idRegex.test(id);
  };

  const validatePhoneNumber = (phone: string) => {
    // SA phone number validation - starts with +27 or 0, followed by 9 digits
    const phoneRegex = /^(\+27|0)[0-9]{9}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});

    // Validation
    const newErrors: { idNumber?: string; phoneNumber?: string } = {};
    
    if (!validateIdNumber(idNumber)) {
      newErrors.idNumber = 'Please enter a valid 13-digit South African ID number';
    }
    
    if (!validatePhoneNumber(phoneNumber)) {
      newErrors.phoneNumber = 'Please enter a valid South African phone number';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsLoading(true);
    
    // Simulate login process
    setTimeout(() => {
      setIsLoading(false);
      onLogin(idNumber, phoneNumber);
    }, 1500);
  };

  const handleRegister = () => {
    if (onRegister) {
      onRegister();
    } else {
      alert('Registration feature coming soon! For now, you can use any valid SA ID and phone number to continue.');
    }
  };

  const handleEmergencyCall = () => {
    // Open phone dialer with emergency number
    window.open('tel:10177', '_self');
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 50%, #CBD5E1 100%)',
      padding: '1rem 0.8rem',
      fontFamily: 'Ubuntu, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center'
    }}>
      {/* Back Button */}
      <button
        onClick={onBack}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.4rem',
          background: 'none',
          border: 'none',
          color: '#6B7280',
          fontSize: '0.9rem',
          cursor: 'pointer',
          padding: '0.4rem',
          marginBottom: '1rem',
          transition: 'color 0.3s ease',
          alignSelf: 'flex-start',
          maxWidth: '350px',
          margin: '0 auto 1rem auto'
        }}
        onMouseOver={(e) => e.currentTarget.style.color = '#228B22'}
        onMouseOut={(e) => e.currentTarget.style.color = '#6B7280'}
      >
        ← Back to Home
      </button>

      {/* Login Form Container */}
      <div style={{
        maxWidth: '350px',
        margin: '0 auto',
        background: 'rgba(255, 255, 255, 0.95)',
        borderRadius: '16px',
        padding: '1.5rem 1.5rem',
        boxShadow: '0 15px 40px rgba(0, 0, 0, 0.08)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        {/* User Icon */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          marginBottom: '1.2rem'
        }}>
          <div style={{
            width: '60px',
            height: '60px',
            background: 'linear-gradient(135deg, #3B82F6, #228B22)',
            borderRadius: '16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '1.5rem',
            color: 'white',
            boxShadow: '0 6px 20px rgba(59, 130, 246, 0.25)'
          }}>
            👤
          </div>
        </div>

        {/* Welcome Text */}
        <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
          <h1 style={{
            fontSize: '1.4rem',
            fontWeight: '700',
            color: '#1F2937',
            marginBottom: '0.3rem'
          }}>
            Welcome Back
          </h1>
          <p style={{
            color: '#6B7280',
            fontSize: '0.85rem',
            lineHeight: '1.4'
          }}>
            Sign in to access your health dashboard
          </p>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit}>
          {/* South African ID Number */}
          <div style={{ marginBottom: '1rem' }}>
            <label style={{
              display: 'block',
              fontSize: '0.8rem',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '0.4rem'
            }}>
              South African ID Number
            </label>
            <input
              type="text"
              value={idNumber}
              onChange={(e) => setIdNumber(e.target.value.replace(/\D/g, '').slice(0, 13))}
              placeholder="Enter your 13-digit ID number"
              style={{
                width: '100%',
                padding: '0.6rem 0.8rem',
                border: errors.idNumber ? '2px solid #EF4444' : '2px solid #E5E7EB',
                borderRadius: '10px',
                fontSize: '0.9rem',
                outline: 'none',
                transition: 'border-color 0.3s ease',
                background: '#FAFAFA',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => e.target.style.borderColor = '#228B22'}
              onBlur={(e) => e.target.style.borderColor = errors.idNumber ? '#EF4444' : '#E5E7EB'}
            />
            {errors.idNumber && (
              <p style={{ color: '#EF4444', fontSize: '0.75rem', marginTop: '0.2rem' }}>
                {errors.idNumber}
              </p>
            )}
          </div>

          {/* Phone Number */}
          <div style={{ marginBottom: '1.2rem' }}>
            <label style={{
              display: 'block',
              fontSize: '0.8rem',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '0.4rem'
            }}>
              Phone Number
            </label>
            <input
              type="tel"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              placeholder="+27 XX XXX XXXX"
              style={{
                width: '100%',
                padding: '0.6rem 0.8rem',
                border: errors.phoneNumber ? '2px solid #EF4444' : '2px solid #E5E7EB',
                borderRadius: '10px',
                fontSize: '0.9rem',
                outline: 'none',
                transition: 'border-color 0.3s ease',
                background: '#FAFAFA',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => e.target.style.borderColor = '#228B22'}
              onBlur={(e) => e.target.style.borderColor = errors.phoneNumber ? '#EF4444' : '#E5E7EB'}
            />
            {errors.phoneNumber && (
              <p style={{ color: '#EF4444', fontSize: '0.75rem', marginTop: '0.2rem' }}>
                {errors.phoneNumber}
              </p>
            )}
          </div>

          {/* Sign In Button */}
          <button
            type="submit"
            disabled={isLoading}
            style={{
              width: '100%',
              background: isLoading
                ? 'linear-gradient(135deg, #9CA3AF, #6B7280)'
                : 'linear-gradient(135deg, #3B82F6, #228B22)',
              color: 'white',
              padding: '0.8rem',
              fontSize: '1rem',
              fontWeight: '600',
              border: 'none',
              borderRadius: '10px',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              boxShadow: '0 3px 15px rgba(59, 130, 246, 0.25)',
              marginBottom: '1rem'
            }}
            onMouseOver={(e) => {
              if (!isLoading) {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 30px rgba(59, 130, 246, 0.4)';
              }
            }}
            onMouseOut={(e) => {
              if (!isLoading) {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 20px rgba(59, 130, 246, 0.3)';
              }
            }}
          >
            {isLoading ? 'Signing In...' : 'Sign In'}
          </button>
        </form>

        {/* Register Link */}
        <div style={{ textAlign: 'center', marginBottom: '1rem' }}>
          <span style={{ color: '#6B7280', fontSize: '0.8rem' }}>
            Don't have an account?{' '}
          </span>
          <button
            onClick={handleRegister}
            style={{
              background: 'none',
              border: 'none',
              color: '#228B22',
              fontSize: '0.8rem',
              fontWeight: '600',
              cursor: 'pointer',
              textDecoration: 'underline'
            }}
          >
            Register here
          </button>
        </div>

        {/* Emergency Call */}
        <div style={{
          textAlign: 'center',
          padding: '1rem',
          background: 'rgba(239, 68, 68, 0.1)',
          borderRadius: '12px',
          border: '1px solid rgba(239, 68, 68, 0.2)'
        }}>
          <button
            onClick={handleEmergencyCall}
            style={{
              background: 'none',
              border: 'none',
              color: '#EF4444',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem',
              width: '100%'
            }}
          >
            🚨 Emergency? Call 10177 (Toll-Free)
          </button>
        </div>
      </div>
    </div>
  );
};

export default PatientLogin;
