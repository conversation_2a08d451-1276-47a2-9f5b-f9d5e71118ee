/**
 * Shared API Types for Ubuntu Health Connect SA
 * Types shared between frontend and backend
 */

// Base API Response
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string | string[];
  timestamp?: string;
}

// Pagination
export interface PaginationParams {
  page?: number;
  per_page?: number;
  search?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    per_page: number;
    total: number;
    pages: number;
  };
}

// Patient Types
export interface Patient {
  patient_id: string;
  first_name: string;
  last_name: string;
  id_number: string;
  phone_number: string;
  email?: string;
  date_of_birth?: string;
  age?: number;
  gender?: 'Male' | 'Female' | 'Other';
  address?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  created_at?: string;
  updated_at?: string;
}

export interface MedicalCondition {
  id?: number;
  patient_id?: string;
  condition_name: string;
  severity?: 'Mild' | 'Moderate' | 'Severe' | 'Critical';
  status?: 'Active' | 'Chronic' | 'Under Treatment' | 'Resolved';
  diagnosed_date?: string;
  notes?: string;
  created_at?: string;
}

export interface AIInteraction {
  id: string;
  patient_id: string;
  interaction_type: 'chat' | 'voice' | 'assessment' | 'monitoring';
  summary?: string;
  full_conversation?: string;
  ai_assessment?: string;
  severity?: 'Low' | 'Moderate' | 'High' | 'Critical';
  recommendations?: string;
  urgent_care?: boolean;
  timestamp?: string;
}

// Request Types
export interface CreatePatientRequest {
  patient_id?: string;
  first_name: string;
  last_name: string;
  id_number: string;
  phone_number: string;
  email?: string;
  date_of_birth?: string;
  age?: number;
  gender?: 'Male' | 'Female' | 'Other';
  address?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  medical_history?: MedicalCondition[];
}

export interface UpdatePatientRequest {
  first_name?: string;
  last_name?: string;
  phone_number?: string;
  email?: string;
  address?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
}

export interface CreateAIInteractionRequest {
  interaction_type?: 'chat' | 'voice' | 'assessment' | 'monitoring';
  summary?: string;
  full_conversation?: string;
  ai_assessment?: string;
  severity?: 'Low' | 'Moderate' | 'High' | 'Critical';
  recommendations?: string;
  urgent_care?: boolean;
}

// Response Types
export interface PatientResponse extends Patient {
  medical_history?: MedicalCondition[];
  ai_interactions?: AIInteraction[];
  found_in_backend?: boolean;
}

export interface PatientSearchResponse {
  patients: PatientResponse[];
  count: number;
  search_term: string;
}

export interface AIInteractionResponse {
  interactions: AIInteraction[];
  count: number;
}

// Health Check Types
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  service: string;
  version: string;
  timestamp: string;
  database: {
    connected: boolean;
    stats: {
      patients: number;
      ai_interactions: number;
      medical_history: number;
      medications: number;
      appointments: number;
    };
  };
  features: {
    patient_api: boolean;
    ai_interactions: boolean;
    frontend_backend_integration: boolean;
    database_persistence: boolean;
  };
  endpoints: {
    health: string;
    patients: string;
    patient_by_id: string;
    ai_interactions: string;
  };
}

// Error Types
export interface ApiError {
  message: string;
  status: number;
  code?: string;
  details?: any;
}
