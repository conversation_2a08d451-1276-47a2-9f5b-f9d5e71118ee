@echo off
title Ubuntu Health Connect SA - Development Environment

echo.
echo 🚀 Ubuntu Health Connect SA - Starting Development Environment
echo ================================================================
echo.

echo 🔧 Starting Backend Server...
start "Ubuntu Health Backend" cmd /k "python "Health Agent Voice/backend_minimal.py""

echo ⏳ Waiting 3 seconds for backend to start...
timeout /t 3 /nobreak >nul

echo.
echo 🌐 Starting Frontend Server...
echo 📱 Frontend: http://localhost:8085
echo 🔧 Backend: http://localhost:5001
echo.
echo ✅ Both services are starting!
echo 💡 Close both windows to stop the servers
echo.

npm run start:frontend:only
