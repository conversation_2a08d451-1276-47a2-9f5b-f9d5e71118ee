import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Bo<PERSON>, User, Heart, MessageCircle, AlertTriangle, CheckCircle } from 'lucide-react';
import { patientCaseService } from '@/services/patientCaseService';
import { monitoringChatbotService } from '@/services/monitoringChatbotService';
import { MonitoringChatInterface } from './MonitoringChatInterface';

export const MonitoringChatbotDemo: React.FC = () => {
  const [showChatInterface, setShowChatInterface] = useState(false);
  const [demoStep, setDemoStep] = useState(0);

  // Removed test case creation - only real AI Health Assistant Chat cases should be used
  const createTestUrgentCase = () => {
    console.log('⚠️ Test case creation disabled - only real AI Health Assistant Chat interactions are displayed');
    alert('Test case creation has been disabled. Only real patient interactions from AI Health Assistant Chat will appear in urgent care.');
  };

  const simulatePatientResponse = (response: string) => {
    const activeSessions = monitoringChatbotService.getActiveSessions();
    if (activeSessions.length > 0) {
      const session = activeSessions[0];
      monitoringChatbotService.addPatientResponse(session.id, response);
      console.log('Added patient response:', response);
    }
  };

  const activeSessions = monitoringChatbotService.getActiveSessions();
  const allSessions = monitoringChatbotService.getAllSessions();

  return (
    <div className="space-y-6">
      <Card className="border-2 border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-green-800">
            <Bot className="w-6 h-6" />
            Ubuntu AI Monitoring Chatbot Demo
            <Badge className="bg-green-500 text-white">
              {activeSessions.length} Active
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Step 1: Create Urgent Case */}
            <Card className={`border-2 ${demoStep >= 1 ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    demoStep >= 1 ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-500'
                  }`}>
                    1
                  </div>
                  <h3 className="font-semibold">Create Urgent Case</h3>
                </div>
                <p className="text-sm text-gray-600 mb-3">
                  Only real AI Health Assistant Chat cases appear
                </p>
                <Button
                  onClick={createTestUrgentCase}
                  className="w-full bg-gray-400 hover:bg-gray-500"
                  disabled={true}
                >
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  Test Cases Disabled
                </Button>
              </CardContent>
            </Card>

            {/* Step 2: Monitor Automatically */}
            <Card className={`border-2 ${activeSessions.length > 0 ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    activeSessions.length > 0 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-500'
                  }`}>
                    2
                  </div>
                  <h3 className="font-semibold">Auto Monitoring</h3>
                </div>
                <p className="text-sm text-gray-600 mb-3">
                  AI automatically starts 5-minute check-ins
                </p>
                <div className="space-y-2">
                  {activeSessions.length > 0 ? (
                    <div className="text-sm">
                      <div className="flex items-center gap-2 text-green-600">
                        <CheckCircle className="w-4 h-4" />
                        Monitoring Active
                      </div>
                      <p className="text-gray-600">
                        Next check-in: {activeSessions[0]?.nextCheckIn.toLocaleTimeString()}
                      </p>
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm">Waiting for urgent case...</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Step 3: View Chat Interface */}
            <Card className="border-2 border-purple-200">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-8 h-8 rounded-full bg-purple-500 text-white flex items-center justify-center">
                    3
                  </div>
                  <h3 className="font-semibold">View Chat</h3>
                </div>
                <p className="text-sm text-gray-600 mb-3">
                  Open WhatsApp-style monitoring interface
                </p>
                <Button 
                  onClick={() => setShowChatInterface(true)}
                  className="w-full bg-purple-500 hover:bg-purple-600"
                  disabled={allSessions.length === 0}
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Open Chat Interface
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Quick Response Simulation */}
          {activeSessions.length > 0 && (
            <Card className="border-2 border-orange-200 bg-orange-50">
              <CardContent className="p-4">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Simulate Patient Responses
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                  <Button 
                    onClick={() => simulatePatientResponse("I'm feeling much better now, the pain has reduced significantly.")}
                    variant="outline"
                    className="text-green-600 border-green-300 hover:bg-green-50"
                  >
                    😊 Feeling Better
                  </Button>
                  <Button 
                    onClick={() => simulatePatientResponse("The pain is still there but manageable. I'm a bit worried.")}
                    variant="outline"
                    className="text-yellow-600 border-yellow-300 hover:bg-yellow-50"
                  >
                    😐 Concerning
                  </Button>
                  <Button 
                    onClick={() => simulatePatientResponse("The pain is getting worse! I can barely breathe!")}
                    variant="outline"
                    className="text-red-600 border-red-300 hover:bg-red-50"
                  >
                    😰 Urgent
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Session Status */}
          <Card className="border-2 border-gray-200">
            <CardContent className="p-4">
              <h3 className="font-semibold mb-3">Monitoring Status</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Active Sessions</p>
                  <p className="text-2xl font-bold text-green-600">{activeSessions.length}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Sessions</p>
                  <p className="text-2xl font-bold text-blue-600">{allSessions.length}</p>
                </div>
              </div>
              
              {activeSessions.length > 0 && (
                <div className="mt-4 space-y-2">
                  {activeSessions.map((session) => (
                    <div key={session.id} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{session.patientName}</span>
                        <Badge className="bg-green-500 text-white">
                          {session.status}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        Check-ins: {session.checkInCount} | 
                        Responses: {session.patientResponses.length}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </CardContent>
      </Card>

      {/* Chat Interface Modal */}
      {showChatInterface && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="w-full max-w-6xl h-[80vh] bg-white rounded-2xl shadow-2xl overflow-hidden">
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white">
              <div className="flex items-center gap-3">
                <Bot className="w-6 h-6" />
                <h2 className="text-xl font-bold">Ubuntu AI Monitoring Chat Demo</h2>
                <Badge className="bg-white/20 text-white">
                  {activeSessions.length} Active
                </Badge>
              </div>
              <Button
                onClick={() => setShowChatInterface(false)}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20"
              >
                ✕
              </Button>
            </div>
            <MonitoringChatInterface onClose={() => setShowChatInterface(false)} />
          </div>
        </div>
      )}
    </div>
  );
};
