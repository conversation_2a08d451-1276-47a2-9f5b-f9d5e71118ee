#!/usr/bin/env python3
"""
Start Backend API Server for Frontend-Backend Integration
This script starts the Flask backend server with patient API endpoints
"""

import os
import sys
import subprocess
import time
import requests
from datetime import datetime

def check_port_available(port):
    """Check if a port is available"""
    try:
        response = requests.get(f'http://localhost:{port}/health', timeout=2)
        return False  # Port is in use
    except:
        return True  # Port is available

def start_backend_server():
    """Start the backend server with database"""
    print("🚀 Starting Ubuntu Health Backend API Server...")
    print("=" * 60)

    # Check if port 5000 is available
    if not check_port_available(5000):
        print("⚠️ Port 5000 is already in use")
        print("   Checking if it's our backend server...")

        try:
            response = requests.get('http://localhost:5000/health', timeout=5)
            data = response.json()

            if data.get('service') == 'HealthConnect SA Voice Assistant':
                print("✅ Backend server is already running!")
                print(f"   Service: {data.get('service')}")
                print(f"   Status: {data.get('status')}")
                print(f"   Database: {data.get('database', {})}")
                print(f"   Features: {data.get('features', {})}")
                print("\n🌐 Backend API Endpoints:")
                endpoints = data.get('endpoints', {})
                for name, path in endpoints.items():
                    print(f"   - {name}: http://localhost:5000{path}")
                print("\n✅ Ready for frontend connections!")
                return True
            else:
                print("❌ Port 5000 is used by another service")
                print("   Please stop the other service or change the port")
                return False

        except Exception as e:
            print(f"❌ Error checking existing service: {e}")
            return False

    # Initialize database
    print("🗄️ Initializing database...")
    try:
        from database.db_manager import DatabaseManager
        db = DatabaseManager()
        if db.test_connection():
            print("✅ Database initialized successfully")
            stats = db.get_database_stats()
            print(f"   Database stats: {stats}")
        else:
            print("❌ Database initialization failed")
            return False
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

    # Start the server
    print("🔧 Starting Flask backend server...")
    print("   Port: 5000")
    print("   CORS enabled for frontend connections")
    print("   Database persistence enabled")
    print("   Patient API endpoints enabled")
    print("\n📋 Available Endpoints:")
    print("   - GET  /health                           - Health check with DB status")
    print("   - POST /api/patients                     - Create patient")
    print("   - GET  /api/patients                     - Get all patients")
    print("   - GET  /api/patients/<id_number>         - Get patient by SA ID")
    print("   - POST /api/patients/<id>/ai-interactions - Create AI interaction")
    print("   - GET  /api/patients/<id>/ai-interactions  - Get AI interactions")
    print("   - POST /voice                            - Voice triage webhook")
    print("   - GET  /logs                             - View conversation logs")

    try:
        # Change to the correct directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)

        print(f"\n🔄 Starting server from: {script_dir}")
        print("   Press Ctrl+C to stop the server")
        print("=" * 60)

        # Start the Flask app
        subprocess.run([sys.executable, 'app.py'], check=True)

    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        return True
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        return False

def test_backend_connection():
    """Test the backend connection"""
    print("\n🧪 Testing Backend Connection...")
    print("-" * 40)
    
    try:
        # Test health endpoint
        response = requests.get('http://localhost:5000/health', timeout=5)
        if response.ok:
            data = response.json()
            print("✅ Health check passed")
            print(f"   Service: {data.get('service')}")
            print(f"   Status: {data.get('status')}")
            
            # Test patient API
            test_patient = {
                'patient_id': 'TEST123',
                'first_name': 'Test',
                'last_name': 'Patient',
                'id_number': '9001015800087',
                'phone_number': '+27821234567',
                'email': '<EMAIL>',
                'age': 35,
                'gender': 'Male'
            }
            
            response = requests.post('http://localhost:5000/api/patients', 
                                   json=test_patient, timeout=5)
            if response.ok:
                print("✅ Patient API test passed")
                result = response.json()
                print(f"   Patient ID: {result.get('patient_id')}")
            else:
                print(f"❌ Patient API test failed: {response.status_code}")
                
        else:
            print(f"❌ Health check failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Connection test failed: {e}")

if __name__ == '__main__':
    print("🏥 Ubuntu Health Connect SA - Backend API Server")
    print("   Frontend-Backend Integration for Patient Data")
    print(f"   Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        test_backend_connection()
    else:
        start_backend_server()
