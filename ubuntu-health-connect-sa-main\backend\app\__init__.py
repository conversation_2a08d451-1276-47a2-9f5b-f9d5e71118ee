"""
Ubuntu Health Connect SA - Backend Application Package
Main Flask application factory and configuration
"""

from flask import Flask
from flask_cors import CORS
from app.config import Config
from app.database import init_db
import logging
import os

def create_app(config_class=Config):
    """
    Application factory pattern for Flask app creation
    """
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize CORS
    CORS(app, origins=[
        'http://localhost:3000',
        'http://localhost:8081', 
        'http://localhost:8082',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:8081',
        'http://127.0.0.1:8082'
    ])
    
    # Initialize database
    init_db(app)
    
    # Configure logging
    if not app.debug and not app.testing:
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        file_handler = logging.FileHandler('logs/ubuntu_health.log')
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('Ubuntu Health Connect SA startup')
    
    # Register blueprints
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    from app.api.health import bp as health_bp
    app.register_blueprint(health_bp)
    
    return app
