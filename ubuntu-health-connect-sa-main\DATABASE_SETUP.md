# 🗄️ Ubuntu Health Connect SA - Database Setup Guide

## Overview

This guide ensures that the Ubuntu Health Connect SA project maintains proper database connectivity and uses the intended Ubuntu Health database for all future sessions.

## 🎯 Database Architecture

### Centralized Database Design
- **Primary Database**: `database/ubuntu_health.db`
- **Synchronized Copies**: Automatically synced across all components
- **Schema**: Comprehensive healthcare data model with POPIA compliance

### Database Components
```
ubuntu-health-connect-sa-main/
├── database/
│   └── ubuntu_health.db                    # Primary database
├── backend/database/
│   └── ubuntu_health.db                    # Backend copy (synced)
├── Health Agent Voice/database/
│   └── ubuntu_health.db                    # Voice agent copy (synced)
└── voice-triage/database/
    └── ubuntu_health.db                    # Voice triage copy (synced)
```

## 🚀 Quick Start

### 1. Automatic Setup (Recommended)
```bash
# Start with automatic database setup
npm run dev
# or
python start_ubuntu_health_final.py
```

### 2. Manual Database Setup
```bash
# Setup database directories and sync
python database_config.py

# Verify database connectivity
python -c "from database_config import get_database_info; import json; print(json.dumps(get_database_info(), indent=2))"
```

## 🔧 Database Configuration

### Environment Variables
```env
# Database Configuration
UBUNTU_HEALTH_DB_PATH=database/ubuntu_health.db
DATABASE_BACKUP_ENABLED=true
DATABASE_SYNC_ON_STARTUP=true
```

### Database Schema
The Ubuntu Health database includes:
- **Patients**: Core patient information with SA ID numbers
- **AI Interactions**: AI-powered health conversations and assessments
- **Medical History**: Patient medical conditions and treatments
- **Medications**: Prescription and medication tracking
- **Appointments**: Healthcare provider appointments
- **Healthcare Providers**: Provider information and access control
- **Audit Logs**: POPIA-compliant activity tracking

## 🔄 Database Synchronization

### Automatic Sync
The system automatically synchronizes the database across all components:
1. **On Startup**: Database files are synced when starting the application
2. **Real-time**: All components use the same primary database
3. **Backup**: Regular backups are created automatically

### Manual Sync
```bash
# Sync database files manually
npm run db:sync
# or
python -c "from database_config import sync_database_files; sync_database_files()"
```

## 📊 Database Health Monitoring

### Health Check Endpoint
```bash
# Check database connectivity
curl http://localhost:5000/health
```

Response includes:
- Database connection status
- Database file path
- Patient and interaction counts
- System health status

### Database Statistics
```python
# Get database statistics
from database_config import get_database_info
info = get_database_info()
print(f"Database size: {info['size_mb']} MB")
print(f"Patients: {info['exists']}")
```

## 🛠️ Troubleshooting

### Common Issues

#### Database Not Found
```bash
# Reinitialize database
python database_config.py
```

#### Connection Issues
```bash
# Test database connection
python -c "
import sys
sys.path.append('Health Agent Voice/database')
from db_manager import DatabaseManager
db = DatabaseManager()
print('Database test:', db.test_connection())
"
```

#### Sync Problems
```bash
# Force database sync
python -c "from database_config import sync_database_files; sync_database_files()"
```

### Database Recovery
If the database becomes corrupted:
1. Stop all services
2. Backup current database: `cp database/ubuntu_health.db database/ubuntu_health.db.backup`
3. Reinitialize: `python database_config.py`
4. Restart services: `npm run dev`

## 🔒 Security & Compliance

### POPIA Compliance
- All patient data is encrypted at rest
- Audit logs track all database access
- Data retention policies are enforced
- Access control by healthcare provider

### Backup Strategy
- Automatic daily backups
- Backup retention: 30 days
- Encrypted backup storage
- Point-in-time recovery available

## 📋 Database Maintenance

### Regular Tasks
```bash
# Check database health
npm run health:check

# Verify database integrity
python -c "
import sqlite3
conn = sqlite3.connect('database/ubuntu_health.db')
conn.execute('PRAGMA integrity_check')
print('Database integrity: OK')
conn.close()
"

# Get database statistics
python -c "
from database_config import get_database_info
import json
print(json.dumps(get_database_info(), indent=2))
"
```

### Performance Optimization
- Regular VACUUM operations
- Index optimization
- Query performance monitoring
- Connection pooling

## 🎯 Best Practices

### Development
1. Always use the centralized database configuration
2. Test database connectivity before making changes
3. Use the provided database manager classes
4. Follow the established schema patterns

### Production
1. Enable database backups
2. Monitor database performance
3. Implement proper error handling
4. Use connection pooling for high load

### Data Management
1. Follow POPIA compliance guidelines
2. Implement proper data validation
3. Use audit logging for all changes
4. Regular data cleanup and archiving

## 🔗 Integration Points

### Backend Integration
```python
# Use the database manager
from database.db_manager import DatabaseManager
db = DatabaseManager()

# Create patient
patient_id = db.create_patient(patient_data)

# Get patients
patients = db.get_all_patients()
```

### Frontend Integration
```javascript
// API calls automatically use the connected database
const response = await fetch('/api/patients');
const patients = await response.json();
```

## 📞 Support

For database-related issues:
1. Check the health endpoint: `http://localhost:5000/health`
2. Review the startup logs
3. Run the database configuration script
4. Check the troubleshooting section above

---

**Built with ❤️ for South African healthcare communities** 🇿🇦

*Embodying Ubuntu: "I am because we are" - Ensuring reliable, connected healthcare data.*
