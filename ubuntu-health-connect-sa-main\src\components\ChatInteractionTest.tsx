import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Phone, Bot, User, CheckCircle, Clock } from 'lucide-react';
import { chatInteractionService } from '@/services/chatInteractionService';
import { voiceTriageIntegrationService } from '@/services/voiceTriageIntegration';
import { healthcareDatabase } from '@/services/healthcareDatabase';

export const ChatInteractionTest = () => {
  const [activeSessions, setActiveSessions] = useState<any[]>([]);
  const [completedSessions, setCompletedSessions] = useState<any[]>([]);
  const [aiInteractions, setAiInteractions] = useState<any[]>([]);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const refreshData = async () => {
    try {
      setActiveSessions(chatInteractionService.getActiveSessions());
      setCompletedSessions(chatInteractionService.getCompletedSessions());
      
      // Get AI interactions for a test patient
      const interactions = await healthcareDatabase.getPatientAIInteractions('8501015800087', 'SYSTEM');
      setAiInteractions(interactions);
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
  };

  useEffect(() => {
    refreshData();
    const interval = setInterval(refreshData, 2000);
    return () => clearInterval(interval);
  }, []);

  const testAIHealthAssistantChat = async () => {
    addTestResult('Testing AI Health Assistant Chat...');
    
    try {
      // Start a chat session
      const sessionId = chatInteractionService.startChatSession(
        'AI Health Assistant Chat',
        'en',
        {
          patientId: '8501015800087',
          idNumber: '8501015800087',
          phone: '+27123456789'
        }
      );
      
      addTestResult(`Started chat session: ${sessionId}`);

      // Add some sample messages
      const messages = [
        { role: 'user' as const, content: 'Hello, I have been having headaches for the past few days.' },
        { role: 'assistant' as const, content: 'I understand you are experiencing headaches. Can you tell me more about the severity and frequency?' },
        { role: 'user' as const, content: 'The pain is quite severe, about 7/10, and happens mostly in the morning.' },
        { role: 'assistant' as const, content: 'Based on your symptoms, I recommend monitoring your blood pressure and considering a medical consultation.' }
      ];

      for (let i = 0; i < messages.length; i++) {
        setTimeout(() => {
          const message = {
            id: `test_${Date.now()}_${i}`,
            ...messages[i],
            timestamp: new Date()
          };
          chatInteractionService.addMessageToSession(sessionId, message);
          addTestResult(`Added message: ${message.role} - ${message.content.substring(0, 30)}...`);
        }, i * 1000);
      }

      // End the session after all messages
      setTimeout(async () => {
        await chatInteractionService.endChatSession(sessionId, 'Test chat session completed');
        addTestResult('Chat session ended and saved to medical records');
        refreshData();
      }, messages.length * 1000 + 1000);

    } catch (error) {
      addTestResult(`Error: ${error}`);
    }
  };

  const testChatMonitor = async () => {
    addTestResult('Testing Chat Monitor...');
    
    try {
      // Start a monitoring session
      const sessionId = chatInteractionService.startChatSession(
        'Chat Monitor',
        'zu',
        {
          patientId: '8501015800087',
          idNumber: '8501015800087'
        }
      );
      
      addTestResult(`Started monitoring session: ${sessionId}`);

      // Simulate monitoring messages
      const monitoringMessages = [
        { role: 'assistant' as const, content: 'Sawubona! Ubuntu Health AI lapha. Unjani namuhla? Ngicela ungazise ukuthi uzizwa kanjani.' },
        { role: 'user' as const, content: 'Ngiyabonga, kodwa ngisenezinhlungu zekhanda.' },
        { role: 'assistant' as const, content: 'Ngiyaqonda ukuthi usanezinhlungu zekhanda. Ngizokwenza ukuthi udokotela akubone masinyane.' }
      ];

      for (let i = 0; i < monitoringMessages.length; i++) {
        setTimeout(() => {
          const message = {
            id: `monitor_${Date.now()}_${i}`,
            ...monitoringMessages[i],
            timestamp: new Date()
          };
          chatInteractionService.addMessageToSession(sessionId, message);
          addTestResult(`Added monitoring message: ${message.role} - ${message.content.substring(0, 30)}...`);
        }, i * 1500);
      }

      // End the session
      setTimeout(async () => {
        await chatInteractionService.endChatSession(sessionId, 'Monitoring session completed - patient referred to doctor');
        addTestResult('Monitoring session ended and saved to medical records');
        refreshData();
      }, monitoringMessages.length * 1500 + 1000);

    } catch (error) {
      addTestResult(`Error: ${error}`);
    }
  };

  const testVoiceTriage = async () => {
    addTestResult('Testing Voice Triage...');
    
    try {
      // Start a voice call
      const callId = voiceTriageIntegrationService.startVoiceTriageCall(
        `AT_${Date.now()}`,
        '+27987654321',
        'af',
        '8501015800087'
      );
      
      addTestResult(`Started voice call: ${callId}`);

      // Add transcripts
      const transcripts = [
        { speaker: 'ai' as const, text: 'Hallo! Welkom by Ubuntu Health AI. Hoe kan ek jou vandag help?' },
        { speaker: 'patient' as const, text: 'Hallo, ek het borspyn en voel kortasem.' },
        { speaker: 'ai' as const, text: 'Ek verstaan jy het borspyn en kortasemigheid. Dit klink ernstig. Ek beveel aan dat jy onmiddellik mediese hulp soek.' }
      ];

      for (let i = 0; i < transcripts.length; i++) {
        setTimeout(() => {
          voiceTriageIntegrationService.addTranscript(
            callId,
            transcripts[i].speaker,
            transcripts[i].text,
            0.95,
            i * 15
          );
          addTestResult(`Added transcript: ${transcripts[i].speaker} - ${transcripts[i].text.substring(0, 30)}...`);
        }, i * 2000);
      }

      // End the call with assessment
      setTimeout(async () => {
        await voiceTriageIntegrationService.endVoiceTriageCall(
          callId,
          180, // 3 minutes
          {
            symptoms: ['Chest pain', 'Shortness of breath'],
            severity: 'Critical',
            recommendations: ['Seek immediate emergency care', 'Call ambulance if symptoms worsen'],
            triageDecision: 'Emergency'
          }
        );
        addTestResult('Voice call ended and saved to medical records');
        refreshData();
      }, transcripts.length * 2000 + 1000);

    } catch (error) {
      addTestResult(`Error: ${error}`);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              Chat Interaction Service Test
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <Button onClick={testAIHealthAssistantChat} className="flex items-center gap-2">
                <Bot className="w-4 h-4" />
                Test AI Health Assistant
              </Button>
              <Button onClick={testChatMonitor} variant="outline" className="flex items-center gap-2">
                <MessageSquare className="w-4 h-4" />
                Test Chat Monitor
              </Button>
              <Button onClick={testVoiceTriage} variant="outline" className="flex items-center gap-2">
                <Phone className="w-4 h-4" />
                Test Voice Triage
              </Button>
              <Button onClick={refreshData} variant="ghost">
                Refresh Data
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Active Sessions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Active Sessions ({activeSessions.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {activeSessions.length === 0 ? (
                <p className="text-gray-500">No active sessions</p>
              ) : (
                <div className="space-y-2">
                  {activeSessions.map((session) => (
                    <div key={session.id} className="p-3 bg-blue-50 rounded-lg border">
                      <div className="flex items-center justify-between">
                        <Badge variant="outline">{session.type}</Badge>
                        <span className="text-sm text-gray-600">{session.language}</span>
                      </div>
                      <p className="text-sm mt-1">Messages: {session.messages.length}</p>
                      <p className="text-xs text-gray-500">Started: {session.startTime.toLocaleTimeString()}</p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Completed Sessions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                Completed Sessions ({completedSessions.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {completedSessions.length === 0 ? (
                <p className="text-gray-500">No completed sessions</p>
              ) : (
                <div className="space-y-2">
                  {completedSessions.map((session) => (
                    <div key={session.id} className="p-3 bg-green-50 rounded-lg border">
                      <div className="flex items-center justify-between">
                        <Badge variant="outline">{session.type}</Badge>
                        <span className="text-sm text-gray-600">{session.language}</span>
                      </div>
                      <p className="text-sm mt-1">Messages: {session.messages.length}</p>
                      <p className="text-xs text-gray-500">
                        Duration: {session.endTime && session.startTime ? 
                          Math.round((session.endTime.getTime() - session.startTime.getTime()) / 1000) : 0}s
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* AI Interactions in Medical Records */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              AI Interactions in Medical Records ({aiInteractions.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {aiInteractions.length === 0 ? (
              <p className="text-gray-500">No AI interactions in medical records</p>
            ) : (
              <div className="space-y-4">
                {aiInteractions.map((interaction) => (
                  <div key={interaction.id} className="p-4 bg-white rounded-lg border shadow-sm">
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline">{interaction.type}</Badge>
                      <span className="text-sm text-gray-600">{interaction.language}</span>
                    </div>
                    <p className="text-sm font-medium mb-2">{interaction.summary}</p>
                    <div className="text-xs text-gray-500 space-y-1">
                      <p>Messages: {interaction.messages.length}</p>
                      <p>Timestamp: {new Date(interaction.timestamp).toLocaleString()}</p>
                      {interaction.duration && <p>Duration: {interaction.duration}</p>}
                      {interaction.aiAssessment && (
                        <p>Assessment: {interaction.aiAssessment.severity} - {interaction.aiAssessment.triageDecision}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Test Results Log */}
        <Card>
          <CardHeader>
            <CardTitle>Test Results Log</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">
              {testResults.length === 0 ? (
                <p>No test results yet. Click a test button to start.</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index}>{result}</div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
