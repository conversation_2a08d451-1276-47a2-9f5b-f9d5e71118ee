"""
Africa's Talking Voice API Integration for Voice Triage System
Handles voice calls, SMS notifications, and call forwarding
"""

import os
import logging
import json
from typing import Dict, List, Optional, Tuple
import africastalking
from datetime import datetime
import requests
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CallResponse:
    success: bool
    call_id: str
    message: str
    error: Optional[str] = None

@dataclass
class SMSResponse:
    success: bool
    message_id: str
    status: str
    cost: str
    error: Optional[str] = None

class AfricasTalkingService:
    """Service for handling Africa's Talking Voice and SMS APIs"""
    
    def __init__(self):
        # Initialize Africa's Talking
        self.username = os.getenv('AFRICAS_TALKING_USERNAME', 'sandbox')
        self.api_key = os.getenv('AFRICAS_TALKING_API_KEY')
        
        if not self.api_key:
            raise ValueError("AFRICAS_TALKING_API_KEY environment variable is required")
        
        # Initialize the SDK
        africastalking.initialize(self.username, self.api_key)
        
        # Get service instances
        self.voice = africastalking.Voice
        self.sms = africastalking.SMS
        
        # Configuration
        self.triage_phone_number = os.getenv('TRIAGE_PHONE_NUMBER', '+27123456789')
        self.webhook_base_url = os.getenv('WEBHOOK_BASE_URL', 'https://your-domain.com')
        
        logger.info(f"Initialized Africa's Talking service for username: {self.username}")
    
    def setup_voice_triage_number(self) -> bool:
        """
        Configure the triage phone number to handle incoming calls
        This would typically be done through Africa's Talking dashboard
        """
        try:
            # Note: Phone number configuration is usually done via dashboard
            # This method documents the webhook URLs that need to be configured
            
            webhook_urls = {
                'voice_callback_url': f"{self.webhook_base_url}/api/voice-triage/callback",
                'voice_notification_url': f"{self.webhook_base_url}/api/voice-triage/notification",
                'dtmf_callback_url': f"{self.webhook_base_url}/api/voice-triage/dtmf"
            }
            
            logger.info(f"Voice triage webhooks configured: {webhook_urls}")
            return True
            
        except Exception as e:
            logger.error(f"Error setting up voice triage number: {str(e)}")
            return False
    
    def handle_incoming_call(self, call_data: Dict) -> str:
        """
        Handle incoming voice call and return TwiML-like response
        This is called by the webhook when a patient calls the triage number
        """
        try:
            caller_number = call_data.get('callerNumber', '')
            session_id = call_data.get('sessionId', '')
            
            logger.info(f"Incoming triage call from {caller_number}, session: {session_id}")
            
            # Generate voice response XML for Africa's Talking
            response_xml = f"""<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="woman" language="en-US">
        Welcome to Ubuntu Health AI Triage Assistant. 
        I will help assess your symptoms and connect you with appropriate care.
        Please speak clearly and describe your main health concern.
    </Say>
    <Record 
        timeout="30" 
        finishOnKey="#" 
        maxLength="300"
        playBeep="true"
        callbackUrl="{self.webhook_base_url}/api/voice-triage/recording"
    />
    <Say voice="woman" language="en-US">
        Thank you. I am analyzing your symptoms. Please hold on.
    </Say>
</Response>"""
            
            return response_xml
            
        except Exception as e:
            logger.error(f"Error handling incoming call: {str(e)}")
            return self._generate_error_response()
    
    def handle_recording_callback(self, recording_data: Dict) -> str:
        """
        Handle the recording callback from Africa's Talking
        This processes the patient's voice input
        """
        try:
            recording_url = recording_data.get('recordingUrl', '')
            session_id = recording_data.get('sessionId', '')
            duration = recording_data.get('durationInSeconds', 0)
            
            logger.info(f"Recording received: {recording_url}, duration: {duration}s")
            
            # This would trigger the speech-to-text and AI processing
            # For now, return a continuation response
            
            response_xml = f"""<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="woman" language="en-US">
        I understand you are experiencing symptoms. 
        Can you tell me when these symptoms started?
        Please speak after the beep.
    </Say>
    <Record 
        timeout="20" 
        finishOnKey="#" 
        maxLength="180"
        playBeep="true"
        callbackUrl="{self.webhook_base_url}/api/voice-triage/follow-up"
    />
</Response>"""
            
            return response_xml
            
        except Exception as e:
            logger.error(f"Error handling recording callback: {str(e)}")
            return self._generate_error_response()
    
    def make_outbound_call(self, phone_number: str, message: str, call_type: str = "notification") -> CallResponse:
        """
        Make an outbound call (e.g., to notify healthcare providers)
        """
        try:
            # Generate TTS message for the call
            call_xml = f"""<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="woman" language="en-US">{message}</Say>
    <Say voice="woman" language="en-US">
        Press 1 to acknowledge this notification, or press 2 to escalate.
    </Say>
    <GetDigits 
        timeout="10" 
        finishOnKey="#" 
        numDigits="1"
        callbackUrl="{self.webhook_base_url}/api/voice-triage/provider-response"
    />
</Response>"""
            
            # Make the call using Africa's Talking Voice API
            # Use the correct method signature: call(callFrom, callTo)
            response = self.voice.call(
                callFrom=self.triage_phone_number,
                callTo=[phone_number]
            )
            
            if response and len(response) > 0:
                call_info = response[0]
                return CallResponse(
                    success=True,
                    call_id=call_info.get('sessionId', ''),
                    message=f"Call initiated to {phone_number}"
                )
            else:
                return CallResponse(
                    success=False,
                    call_id='',
                    message="Failed to initiate call",
                    error="No response from Africa's Talking API"
                )
                
        except Exception as e:
            logger.error(f"Error making outbound call to {phone_number}: {str(e)}")
            return CallResponse(
                success=False,
                call_id='',
                message="Call failed",
                error=str(e)
            )
    
    def send_sms_notification(self, phone_number: str, message: str, priority: str = "normal") -> SMSResponse:
        """
        Send SMS notification to healthcare providers
        """
        try:
            # Add priority indicator to message
            if priority == "urgent":
                message = f"🚨 URGENT TRIAGE ALERT 🚨\n{message}"
            elif priority == "critical":
                message = f"🆘 CRITICAL TRIAGE ALERT 🆘\n{message}"
            
            # Send SMS using Africa's Talking
            response = self.sms.send(message, [phone_number])
            
            if response and 'SMSMessageData' in response:
                sms_data = response['SMSMessageData']
                recipients = sms_data.get('Recipients', [])
                
                if recipients and len(recipients) > 0:
                    recipient = recipients[0]
                    return SMSResponse(
                        success=True,
                        message_id=recipient.get('messageId', ''),
                        status=recipient.get('status', ''),
                        cost=recipient.get('cost', '0')
                    )
            
            return SMSResponse(
                success=False,
                message_id='',
                status='failed',
                cost='0',
                error="Failed to send SMS"
            )
            
        except Exception as e:
            logger.error(f"Error sending SMS to {phone_number}: {str(e)}")
            return SMSResponse(
                success=False,
                message_id='',
                status='failed',
                cost='0',
                error=str(e)
            )
    
    def send_urgent_notifications(self, triage_report: Dict, nurse_numbers: List[str]) -> List[Dict]:
        """
        Send urgent notifications via both SMS and voice calls
        """
        notifications = []
        
        try:
            patient_name = triage_report.get('patient_name', 'Unknown Patient')
            risk_level = triage_report.get('risk_level', 'unknown')
            symptoms = ', '.join(triage_report.get('reported_symptoms', []))
            
            # Create notification message
            sms_message = f"""
URGENT TRIAGE ALERT
Patient: {patient_name}
Risk Level: {risk_level.upper()}
Symptoms: {symptoms}
Time: {datetime.now().strftime('%Y-%m-%d %H:%M')}
Please review immediately in the provider dashboard.
            """.strip()
            
            voice_message = f"""
This is an urgent triage alert from Ubuntu Health. 
Patient {patient_name} has been assessed as {risk_level} risk.
Reported symptoms include {symptoms}.
Please check your dashboard immediately for full details.
            """
            
            # Send to all nurse numbers
            for phone_number in nurse_numbers:
                # Send SMS first
                sms_result = self.send_sms_notification(phone_number, sms_message, "urgent")
                notifications.append({
                    'type': 'sms',
                    'recipient': phone_number,
                    'success': sms_result.success,
                    'message_id': sms_result.message_id,
                    'error': sms_result.error
                })
                
                # Then make voice call
                call_result = self.make_outbound_call(phone_number, voice_message, "urgent")
                notifications.append({
                    'type': 'voice_call',
                    'recipient': phone_number,
                    'success': call_result.success,
                    'call_id': call_result.call_id,
                    'error': call_result.error
                })
            
            return notifications
            
        except Exception as e:
            logger.error(f"Error sending urgent notifications: {str(e)}")
            return [{'type': 'error', 'error': str(e)}]
    
    def _generate_error_response(self) -> str:
        """Generate error response XML"""
        return """<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="woman" language="en-US">
        I apologize, but I'm experiencing technical difficulties. 
        Please call back in a few minutes or contact emergency services if this is urgent.
    </Say>
    <Hangup/>
</Response>"""
    
    def get_call_status(self, call_id: str) -> Dict:
        """Get the status of a specific call"""
        try:
            # Note: Africa's Talking doesn't have a direct call status API
            # Status updates come through webhooks
            return {
                'call_id': call_id,
                'status': 'unknown',
                'message': 'Call status tracking via webhooks'
            }
        except Exception as e:
            logger.error(f"Error getting call status: {str(e)}")
            return {'error': str(e)}
