#!/usr/bin/env python3
"""
Ubuntu Health Connect SA - Enhanced Startup Script
Ensures proper database connectivity and system initialization
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path
from database_config import (
    ensure_database_directories, 
    sync_database_files, 
    get_database_info,
    get_primary_database_path
)

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    try:
        import flask
        import flask_cors
        import dotenv
        print("✅ Python dependencies OK")
    except ImportError as e:
        print(f"❌ Missing Python dependency: {e}")
        return False
    
    # Check if Node.js is available
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js OK: {result.stdout.strip()}")
        else:
            print("❌ Node.js not found")
            return False
    except FileNotFoundError:
        print("❌ Node.js not found")
        return False
    
    return True

def setup_database():
    """Setup and verify database connectivity"""
    print("🗄️ Setting up Ubuntu Health database...")
    
    # Ensure directories exist
    ensure_database_directories()
    
    # Get database info
    db_info = get_database_info()
    print(f"📊 Primary database: {db_info['primary_database']}")
    print(f"📊 Database exists: {db_info['exists']}")
    print(f"📊 Database size: {db_info['size_mb']} MB")
    
    # Initialize database if it doesn't exist
    if not db_info['exists']:
        print("🔧 Initializing new Ubuntu Health database...")
        try:
            # Import and initialize database
            sys.path.append(os.path.join(os.path.dirname(__file__), 'Health Agent Voice', 'database'))
            from db_manager import DatabaseManager
            
            db_path = get_primary_database_path()
            db_manager = DatabaseManager(db_path=db_path)
            
            if db_manager.test_connection():
                print("✅ Database initialized successfully")
            else:
                print("❌ Database initialization failed")
                return False
        except Exception as e:
            print(f"❌ Error initializing database: {e}")
            return False
    
    # Sync database files
    print("🔄 Syncing database files...")
    if sync_database_files():
        print("✅ Database sync completed")
    else:
        print("⚠️ Database sync had issues")
    
    return True

def start_backend():
    """Start the backend server"""
    print("🚀 Starting Ubuntu Health backend...")
    
    backend_script = os.path.join(os.path.dirname(__file__), 'Health Agent Voice', 'backend_minimal.py')
    
    try:
        # Start backend in a new process
        process = subprocess.Popen([
            sys.executable, backend_script
        ], cwd=os.path.dirname(__file__))
        
        # Wait a moment for backend to start
        time.sleep(3)
        
        # Check if backend is running
        try:
            response = requests.get('http://localhost:5002/health', timeout=5)
            if response.status_code == 200:
                print("✅ Backend started successfully")
                return process
            else:
                print(f"❌ Backend health check failed: {response.status_code}")
                return None
        except requests.exceptions.RequestException as e:
            print(f"❌ Backend not responding: {e}")
            return None
            
    except Exception as e:
        print(f"❌ Error starting backend: {e}")
        return None

def start_frontend():
    """Start the frontend server"""
    print("🌐 Starting Ubuntu Health frontend...")
    
    try:
        # Start frontend in a new process
        process = subprocess.Popen([
            'npm', 'run', 'start:frontend:only'
        ], cwd=os.path.dirname(__file__))
        
        # Wait a moment for frontend to start
        time.sleep(5)
        
        # Check if frontend is running
        try:
            response = requests.get('http://localhost:8085', timeout=5)
            if response.status_code == 200:
                print("✅ Frontend started successfully")
                return process
            else:
                print(f"❌ Frontend health check failed: {response.status_code}")
                return None
        except requests.exceptions.RequestException as e:
            print(f"❌ Frontend not responding: {e}")
            return None
            
    except Exception as e:
        print(f"❌ Error starting frontend: {e}")
        return None

def main():
    """Main startup function"""
    print("🏥 Ubuntu Health Connect SA - Enhanced Startup")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Dependency check failed. Please install missing dependencies.")
        return False
    
    # Setup database
    if not setup_database():
        print("❌ Database setup failed.")
        return False
    
    # Start backend
    backend_process = start_backend()
    if not backend_process:
        print("❌ Failed to start backend.")
        return False
    
    # Start frontend
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ Failed to start frontend.")
        backend_process.terminate()
        return False
    
    print("=" * 60)
    print("✅ Ubuntu Health Connect SA is now running!")
    print("🌐 Frontend: http://localhost:8085")
    print("🔧 Backend: http://localhost:5002")
    print("🗄️ Database: Connected and synced")
    print("=" * 60)
    print("Press Ctrl+C to stop all services")
    
    try:
        # Keep the script running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Stopping Ubuntu Health Connect SA...")
        if backend_process:
            backend_process.terminate()
        if frontend_process:
            frontend_process.terminate()
        print("✅ All services stopped")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
