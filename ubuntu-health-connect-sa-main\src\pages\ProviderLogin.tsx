import React, { useState } from 'react';

interface ProviderLoginProps {
  onBack: () => void;
  onLogin: (employeeId: string, password: string, facility: string, role: string) => void;
}

const ProviderLogin = ({ onBack, onLogin }: ProviderLoginProps) => {
  const [employeeId, setEmployeeId] = useState('');
  const [password, setPassword] = useState('');
  const [facility, setFacility] = useState('');
  const [role, setRole] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ 
    employeeId?: string; 
    password?: string; 
    facility?: string; 
    role?: string; 
  }>({});

  // South African healthcare facilities
  const facilities = [
    'Chris <PERSON> Barag<PERSON>th Academic Hospital - Johannesburg',
    'Groote Schuur Hospital - Cape Town',
    'Charlotte Maxeke Johannesburg Academic Hospital',
    'Inkosi Albert Luthuli Central Hospital - Durban',
    'Tygerberg Hospital - Cape Town',
    'Steve <PERSON> Hospital - Pretoria',
    'Universitas Academic Hospital - Bloemfontein',
    'Kimberley Hospital Complex',
    'Polokwane Hospital - Limpopo',
    'Livingstone Hospital - Port Elizabeth',
    'Grey\'s Hospital - Pietermaritzburg',
    'Addington Hospital - Durban',
    'Red Cross War Memorial Children\'s Hospital - Cape Town',
    'Community Health Centre - Various Locations',
    'Private Practice - Independent'
  ];

  const roles = [
    'Doctor - General Practitioner',
    'Doctor - Specialist',
    'Registered Nurse',
    'Professional Nurse',
    'Clinical Associate',
    'Pharmacist',
    'Physiotherapist',
    'Occupational Therapist',
    'Social Worker',
    'Psychologist',
    'Radiographer',
    'Laboratory Technologist',
    'Healthcare Administrator',
    'Community Health Worker'
  ];

  const validateForm = () => {
    const newErrors: typeof errors = {};
    
    if (!employeeId.trim()) {
      newErrors.employeeId = 'Employee ID is required';
    } else if (employeeId.length < 3) {
      newErrors.employeeId = 'Employee ID must be at least 3 characters';
    }
    
    if (!password.trim()) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    if (!facility) {
      newErrors.facility = 'Please select your healthcare facility';
    }
    
    if (!role) {
      newErrors.role = 'Please select your role';
    }

    return newErrors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsLoading(true);
    
    // Simulate login process
    setTimeout(() => {
      setIsLoading(false);
      onLogin(employeeId, password, facility, role);
    }, 2000);
  };

  const handleContactSupport = () => {
    // Open email client or show contact information
    window.open('mailto:<EMAIL>?subject=Healthcare Provider Access Support', '_blank');
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 50%, #CBD5E1 100%)',
      padding: '1rem 0.8rem',
      fontFamily: 'Ubuntu, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center'
    }}>
      {/* Back Button */}
      <button
        onClick={onBack}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.4rem',
          background: 'none',
          border: 'none',
          color: '#6B7280',
          fontSize: '0.9rem',
          cursor: 'pointer',
          padding: '0.4rem',
          marginBottom: '1rem',
          transition: 'color 0.3s ease',
          alignSelf: 'flex-start',
          maxWidth: '380px',
          margin: '0 auto 1rem auto'
        }}
        onMouseOver={(e) => e.currentTarget.style.color = '#3B82F6'}
        onMouseOut={(e) => e.currentTarget.style.color = '#6B7280'}
      >
        ← Back to Home
      </button>

      {/* Login Form Container */}
      <div style={{
        maxWidth: '380px',
        margin: '0 auto',
        background: 'rgba(255, 255, 255, 0.95)',
        borderRadius: '16px',
        padding: '1.5rem 1.5rem',
        boxShadow: '0 15px 40px rgba(0, 0, 0, 0.08)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        {/* Healthcare Provider Icon */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          marginBottom: '1.2rem'
        }}>
          <div style={{
            width: '60px',
            height: '60px',
            background: 'linear-gradient(135deg, #3B82F6, #1E40AF)',
            borderRadius: '16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '1.5rem',
            color: 'white',
            boxShadow: '0 6px 20px rgba(59, 130, 246, 0.25)'
          }}>
            👩‍⚕️
          </div>
        </div>

        {/* Welcome Text */}
        <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
          <h1 style={{
            fontSize: '1.4rem',
            fontWeight: '700',
            color: '#1F2937',
            marginBottom: '0.3rem'
          }}>
            Healthcare Provider Access
          </h1>
          <p style={{
            color: '#6B7280',
            fontSize: '0.85rem',
            lineHeight: '1.4'
          }}>
            Secure login for medical professionals
          </p>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit}>
          {/* Employee ID */}
          <div style={{ marginBottom: '1rem' }}>
            <label style={{
              display: 'block',
              fontSize: '0.8rem',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '0.4rem'
            }}>
              Employee ID
            </label>
            <input
              type="text"
              value={employeeId}
              onChange={(e) => setEmployeeId(e.target.value)}
              placeholder="Enter your employee ID"
              style={{
                width: '100%',
                padding: '0.6rem 0.8rem',
                border: errors.employeeId ? '2px solid #EF4444' : '2px solid #E5E7EB',
                borderRadius: '10px',
                fontSize: '0.9rem',
                outline: 'none',
                transition: 'border-color 0.3s ease',
                background: '#FAFAFA',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => e.target.style.borderColor = '#3B82F6'}
              onBlur={(e) => e.target.style.borderColor = errors.employeeId ? '#EF4444' : '#E5E7EB'}
            />
            {errors.employeeId && (
              <p style={{ color: '#EF4444', fontSize: '0.75rem', marginTop: '0.2rem' }}>
                {errors.employeeId}
              </p>
            )}
          </div>

          {/* Password */}
          <div style={{ marginBottom: '1rem' }}>
            <label style={{
              display: 'block',
              fontSize: '0.8rem',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '0.4rem'
            }}>
              Password
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              style={{
                width: '100%',
                padding: '0.6rem 0.8rem',
                border: errors.password ? '2px solid #EF4444' : '2px solid #E5E7EB',
                borderRadius: '10px',
                fontSize: '0.9rem',
                outline: 'none',
                transition: 'border-color 0.3s ease',
                background: '#FAFAFA',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => e.target.style.borderColor = '#3B82F6'}
              onBlur={(e) => e.target.style.borderColor = errors.password ? '#EF4444' : '#E5E7EB'}
            />
            {errors.password && (
              <p style={{ color: '#EF4444', fontSize: '0.75rem', marginTop: '0.2rem' }}>
                {errors.password}
              </p>
            )}
          </div>

          {/* Healthcare Facility */}
          <div style={{ marginBottom: '1rem' }}>
            <label style={{
              display: 'block',
              fontSize: '0.8rem',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '0.4rem'
            }}>
              Healthcare Facility
            </label>
            <select
              value={facility}
              onChange={(e) => setFacility(e.target.value)}
              style={{
                width: '100%',
                padding: '0.6rem 0.8rem',
                border: errors.facility ? '2px solid #EF4444' : '2px solid #E5E7EB',
                borderRadius: '10px',
                fontSize: '0.9rem',
                outline: 'none',
                transition: 'border-color 0.3s ease',
                background: '#FAFAFA',
                cursor: 'pointer',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => e.target.style.borderColor = '#3B82F6'}
              onBlur={(e) => e.target.style.borderColor = errors.facility ? '#EF4444' : '#E5E7EB'}
            >
              <option value="">Select your facility</option>
              {facilities.map((fac, index) => (
                <option key={index} value={fac}>{fac}</option>
              ))}
            </select>
            {errors.facility && (
              <p style={{ color: '#EF4444', fontSize: '0.75rem', marginTop: '0.2rem' }}>
                {errors.facility}
              </p>
            )}
          </div>

          {/* Role */}
          <div style={{ marginBottom: '1rem' }}>
            <label style={{
              display: 'block',
              fontSize: '0.8rem',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '0.4rem'
            }}>
              Role
            </label>
            <select
              value={role}
              onChange={(e) => setRole(e.target.value)}
              style={{
                width: '100%',
                padding: '0.6rem 0.8rem',
                border: errors.role ? '2px solid #EF4444' : '2px solid #E5E7EB',
                borderRadius: '10px',
                fontSize: '0.9rem',
                outline: 'none',
                transition: 'border-color 0.3s ease',
                background: '#FAFAFA',
                cursor: 'pointer',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => e.target.style.borderColor = '#3B82F6'}
              onBlur={(e) => e.target.style.borderColor = errors.role ? '#EF4444' : '#E5E7EB'}
            >
              <option value="">Select your role</option>
              {roles.map((r, index) => (
                <option key={index} value={r}>{r}</option>
              ))}
            </select>
            {errors.role && (
              <p style={{ color: '#EF4444', fontSize: '0.75rem', marginTop: '0.2rem' }}>
                {errors.role}
              </p>
            )}
          </div>

          {/* Security Notice */}
          <div style={{
            display: 'flex',
            alignItems: 'flex-start',
            gap: '0.6rem',
            background: 'rgba(59, 130, 246, 0.1)',
            padding: '0.8rem',
            borderRadius: '10px',
            marginBottom: '1rem',
            border: '1px solid rgba(59, 130, 246, 0.2)'
          }}>
            <div style={{
              width: '18px',
              height: '18px',
              borderRadius: '50%',
              background: '#3B82F6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexShrink: 0,
              marginTop: '1px'
            }}>
              <span style={{ color: 'white', fontSize: '0.7rem' }}>ℹ</span>
            </div>
            <p style={{
              fontSize: '0.8rem',
              color: '#1E40AF',
              margin: 0,
              lineHeight: '1.3'
            }}>
              This is a secure healthcare system. All access is logged and monitored.
            </p>
          </div>

          {/* Access Dashboard Button */}
          <button
            type="submit"
            disabled={isLoading}
            style={{
              width: '100%',
              background: isLoading
                ? 'linear-gradient(135deg, #9CA3AF, #6B7280)'
                : 'linear-gradient(135deg, #3B82F6, #228B22)',
              color: 'white',
              padding: '0.8rem',
              fontSize: '1rem',
              fontWeight: '600',
              border: 'none',
              borderRadius: '10px',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              boxShadow: '0 3px 15px rgba(59, 130, 246, 0.25)',
              marginBottom: '1rem'
            }}
            onMouseOver={(e) => {
              if (!isLoading) {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 30px rgba(59, 130, 246, 0.4)';
              }
            }}
            onMouseOut={(e) => {
              if (!isLoading) {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 20px rgba(59, 130, 246, 0.3)';
              }
            }}
          >
            {isLoading ? 'Accessing Dashboard...' : 'Access Dashboard'}
          </button>
        </form>

        {/* IT Support Link */}
        <div style={{ textAlign: 'center' }}>
          <span style={{ color: '#6B7280', fontSize: '0.8rem' }}>
            Need help accessing your account?{' '}
          </span>
          <button
            onClick={handleContactSupport}
            style={{
              background: 'none',
              border: 'none',
              color: '#3B82F6',
              fontSize: '0.8rem',
              fontWeight: '600',
              cursor: 'pointer',
              textDecoration: 'underline'
            }}
          >
            Contact IT Support
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProviderLogin;
