
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Phone, MessageSquare, Bell, MapPin, Heart, Activity, Pill, Sparkles, MessageCircle } from 'lucide-react';
import { LanguageSelector } from '@/components/LanguageSelector';
import { SymptomChecker } from '@/components/SymptomChecker';

import { MonitoringChatInterface } from '@/components/MonitoringChatInterface';
import { monitoringChatbotService } from '@/services/monitoringChatbotService';
import { AnimatedBackground, FloatingElements, PremiumGlassContainer } from '@/components/ui/animated-background';
import { CircularProgress } from '@/components/ui/circular-progress';
import VoiceTriageButton from '@/components/VoiceTriageButton';
import VoiceTriageHistory from '@/components/VoiceTriageHistory';
import { getLanguageText } from '@/utils/translations';

interface PatientDashboardProps {
  onLogout?: () => void;
}

const PatientDashboard = ({ onLogout }: PatientDashboardProps) => {
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [showSymptomChecker, setShowSymptomChecker] = useState(false);
  const [showAIHealthChat, setShowAIHealthChat] = useState(false);
  const [showMonitoringChat, setShowMonitoringChat] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const appointments = [
    {
      id: 1,
      date: '2025-06-15',
      time: '10:00',
      facility: 'Khayelitsha Community Health Centre',
      type: 'Follow-up',
      status: 'confirmed'
    },
    {
      id: 2,
      date: '2025-06-20',
      time: '14:30',
      facility: 'Groote Schuur Hospital',
      type: 'Specialist Consultation',
      status: 'pending'
    }
  ];

  const medications = [
    {
      name: 'Paracetamol 500mg',
      dosage: '2 tablets, 3 times daily',
      status: 'delivered',
      nextDelivery: '2025-06-18'
    },
    {
      name: 'Vitamin D',
      dosage: '1 tablet daily',
      status: 'in_transit',
      nextDelivery: '2025-06-14'
    }
  ];

  const getLanguageText = (key: string) => {
    const translations = {
      en: {
        dashboard: 'Ubuntu Health Dashboard',
        appointments: 'Ubuntu Appointments',
        medications: 'Traditional & Modern Medicine',
        startConsultation: 'Start Ubuntu Consultation',
        callNow: 'Call 10177 (Emergency) / 10010 (Health)',
        chatBot: 'Chat with Ubuntu Health Assistant',
        emergencyContact: 'Emergency Ubuntu Support',
        viewAll: 'View All - Bona Konke',
        greeting: 'Sawubona, Nomsa Mthembu',
        ubuntuPhrase: '"Ubuntu ngumuntu ngabantu" - I am because we are',
        nextAppointment: 'Next Appointment',
        upcomingMeds: 'Upcoming Medications',
        healthStatus: 'Health Status',
        recentActivity: 'Recent Activity',
        voiceTriage: 'Voice Triage',
        chatMonitor: 'Chat Monitor',
        availableLanguages: 'Available in all 11 SA languages',
        confirmed: 'Confirmed',
        pending: 'Pending',
        delivered: 'Delivered',
        inTransit: 'In Transit',
        followUp: 'Follow-up',
        specialistConsultation: 'Specialist Consultation',
        nextDelivery: 'Next Delivery',
        dosage: 'Dosage',
        status: 'Status',
        facility: 'Facility',
        date: 'Date',
        time: 'Time',
        healthScore: 'Ubuntu Health Score',
        excellent: 'Excellent',
        heartRate: 'Heart Rate',
        normalRange: 'Normal range',
        medicationsActive: 'Medications',
        active: 'Active',
        onSchedule: 'On schedule',
        nextVisit: 'Next Visit',
        inDays: 'In 3 days',
        voiceAITriage: 'Voice AI Triage',
        immediateAssistance: 'Immediate assistance',
        scheduledVisits: 'Your scheduled healthcare visits',
        prescriptionsStatus: 'Your current prescriptions and delivery status',
        dailyHealthTips: 'Daily Health Tips',
        personalizedRecommendations: 'Personalized recommendations based on your health profile',
        hydration: 'Hydration',
        drinkWater: 'Drink at least 8 glasses of water daily',
        exercise: 'Exercise',
        takeWalk: 'Take a 30-minute walk today',
        medication: 'Medication',
        eveningMeds: 'Take your evening medication at 8 PM',
        logout: 'Logout',
        logoutConfirm: 'Are you sure you want to logout?'
      },
      zu: {
        dashboard: 'Ideshibhodi Yami Ye-Ubuntu Yezempilo',
        appointments: 'Ama-Appointment E-Ubuntu',
        medications: 'Imithi Yendabuko Neyesimanje',
        startConsultation: 'Qala Ukuxoxisana Kwe-Ubuntu',
        callNow: 'Shayela ku-10177 (Isimo Esiphuthumayo) / 10010',
        chatBot: 'Xoxa noMsizi We-Ubuntu Wezempilo',
        emergencyContact: 'Usizo Lwe-Ubuntu Lwesimo Esiphuthumayo',
        viewAll: 'Bona Konke - Ubuntu Spirit',
        greeting: 'Sawubona, Nomsa Mthembu',
        ubuntuPhrase: '"Ubuntu ngumuntu ngabantu" - Ngiyinto ngoba siyinto',
        nextAppointment: 'I-Appointment Elandelayo',
        upcomingMeds: 'Imithi Ezayo',
        healthStatus: 'Isimo Sezempilo',
        recentActivity: 'Umsebenzi Wakamuva',
        voiceTriage: 'Ukuhlola Ngezwi',
        chatMonitor: 'Umqaphi Wokuxoxa',
        availableLanguages: 'Iyatholakala ngazo zonke izilimi ze-SA ezingu-11',
        confirmed: 'Kuqinisekisiwe',
        pending: 'Kulindile',
        delivered: 'Kulethiwe',
        inTransit: 'Kusendleleni',
        followUp: 'Ukulandelela',
        specialistConsultation: 'Ukubonana Nochwepheshe',
        nextDelivery: 'Ukulethwa Okulandelayo',
        dosage: 'Umthamo',
        status: 'Isimo',
        facility: 'Isikhungo',
        date: 'Usuku',
        time: 'Isikhathi',
        healthScore: 'Amaphuzu Ezempilo E-Ubuntu',
        excellent: 'Okuhle Kakhulu',
        heartRate: 'Inhliziyo',
        normalRange: 'Kulungile',
        medicationsActive: 'Imithi',
        active: 'Esebenzayo',
        onSchedule: 'Ngesikhathi',
        nextVisit: 'Usuku',
        inDays: 'Ezinsukwini ezi-3',
        voiceAITriage: 'Ukuhlola Ngezwi',
        immediateAssistance: 'Usizo Olusheshayo',
        scheduledVisits: 'Ukuvakasha kwakho okuhleliwe kwezempilo',
        prescriptionsStatus: 'Imithi yakho yamanje nesimo sokulethwa',
        dailyHealthTips: 'Amathiphu Ezempilo Ansuku Zonke',
        personalizedRecommendations: 'Izincomo eziqondene nawe ngokusekelwe ephrofayilini yakho yezempilo',
        hydration: 'Amanzi',
        drinkWater: 'Phuza okungenani izinkomishi zamanzi ezi-8 nsuku zonke',
        exercise: 'Ukuzivocavoca',
        takeWalk: 'Thatha ukuhamba kwamaminithi angu-30 namuhla',
        medication: 'Imithi',
        eveningMeds: 'Thatha imithi yakho yakusihlwa ngo-8 PM',
        logout: 'Phuma',
        logoutConfirm: 'Uqinisekile ukuthi ufuna ukuphuma?'
      },
      af: {
        dashboard: 'My Ubuntu Gesondheid Bord',
        appointments: 'Ubuntu Afsprake',
        medications: 'Tradisionele & Moderne Medisyne',
        startConsultation: 'Begin Ubuntu Konsultasie',
        callNow: 'Bel 10177 (Noodgeval) / 10010 (Gesondheid)',
        chatBot: 'Gesels met Ubuntu Gesondheid Assistent',
        emergencyContact: 'Ubuntu Noodkontak',
        viewAll: 'Sien Alles - Ubuntu Gees',
        greeting: 'Sawubona, Nomsa Mthembu',
        ubuntuPhrase: '"Ubuntu ngumuntu ngabantu" - Ek is omdat ons is',
        nextAppointment: 'Volgende Afspraak',
        upcomingMeds: 'Komende Medisyne',
        healthStatus: 'Gesondheid Status',
        recentActivity: 'Onlangse Aktiwiteit',
        voiceTriage: 'Stem Triage',
        chatMonitor: 'Klets Monitor',
        availableLanguages: 'Beskikbaar in al 11 SA tale',
        confirmed: 'Bevestig',
        pending: 'Hangende',
        delivered: 'Afgelewer',
        inTransit: 'Onderweg',
        followUp: 'Opvolg',
        specialistConsultation: 'Spesialis Konsultasie',
        nextDelivery: 'Volgende Aflewering',
        dosage: 'Dosering',
        status: 'Status',
        facility: 'Fasiliteit',
        date: 'Datum',
        time: 'Tyd',
        healthScore: 'Ubuntu Gesondheid Telling',
        excellent: 'Uitstekend',
        heartRate: 'Hartklop',
        normalRange: 'Normale reeks',
        medicationsActive: 'Medisyne',
        active: 'Aktief',
        onSchedule: 'Op skedule',
        nextVisit: 'Volgende Besoek',
        inDays: 'Oor 3 dae',
        voiceAITriage: 'Stem AI Triage',
        immediateAssistance: 'Onmiddellike hulp',
        scheduledVisits: 'Jou geskeduleerde gesondheidsbesoeke',
        prescriptionsStatus: 'Jou huidige voorskrifte en afleweringstatus',
        dailyHealthTips: 'Daaglikse Gesondheid Wenke',
        personalizedRecommendations: 'Gepersonaliseerde aanbevelings gebaseer op jou gesondheidsprofiel',
        hydration: 'Hidrasie',
        drinkWater: 'Drink ten minste 8 glase water daagliks',
        exercise: 'Oefening',
        takeWalk: 'Neem vandag \'n 30-minuut stap',
        medication: 'Medisyne',
        eveningMeds: 'Neem jou aand medisyne om 8 VM',
        logout: 'Teken Uit',
        logoutConfirm: 'Is jy seker jy wil uitteken?'
      },
      xh: {
        dashboard: 'Ideshibhodi Yam Ye-Ubuntu Yempilo',
        appointments: 'Iidinga Ze-Ubuntu',
        medications: 'Amayeza Esintu Nanamhlanje',
        startConsultation: 'Qala Ingxoxo Ye-Ubuntu',
        callNow: 'Biza ku-10177 (Ingxaki) / 10010',
        chatBot: 'Thetha noMncedi We-Ubuntu Wempilo',
        emergencyContact: 'Uncedo Lwe-Ubuntu Lwengxaki',
        viewAll: 'Bona Konke - Ubuntu Umoya',
        greeting: 'Sawubona, Nomsa Mthembu',
        ubuntuPhrase: '"Ubuntu ngumuntu ngabantu" - Ndiyinto kuba siyinto',
        nextAppointment: 'Idinga Elilandelayo',
        upcomingMeds: 'Amayeza Azayo',
        healthStatus: 'Imeko Yempilo',
        recentActivity: 'Umsebenzi Wamva Nje',
        voiceTriage: 'Ukuhlola Ngelizwi',
        chatMonitor: 'Umjongisi Wencoko',
        availableLanguages: 'Iyafumaneka kuzo zonke iilwimi ze-SA ezili-11',
        confirmed: 'Kuqinisekisiwe',
        pending: 'Kulindile',
        delivered: 'Kuhanjisiwe',
        inTransit: 'Kusendleleni',
        followUp: 'Ukulandelela',
        specialistConsultation: 'Ingxoxo Yengcali',
        nextDelivery: 'Ukuhanjiswa Okulandelayo',
        dosage: 'Umthamo',
        status: 'Imeko',
        facility: 'Iziko',
        date: 'Umhla',
        time: 'Ixesha',
        healthScore: 'Amanqaku Empilo E-Ubuntu',
        excellent: 'Kugqwesile',
        heartRate: 'Intliziyo',
        normalRange: 'Kulungile',
        medicationsActive: 'Amayeza',
        active: 'Asebenzayo',
        onSchedule: 'Ngexesha',
        nextVisit: 'Umhla',
        inDays: 'Kwiintsuku ezi-3',
        voiceAITriage: 'Ukuhlola Ngelizwi',
        immediateAssistance: 'Uncedo Olukhawulezayo',
        scheduledVisits: 'Iindwendwe zakho ezicwangcisiweyo zempilo',
        prescriptionsStatus: 'Amayeza akho angoku kunye nesimo sokuhanjiswa',
        dailyHealthTips: 'Iingcebiso Zempilo Zemihla Ngemihla',
        personalizedRecommendations: 'Iingcebiso eziqondene nawe ngokweprofayile yakho yempilo',
        hydration: 'Amanzi',
        drinkWater: 'Sela ubuncinci iinkomishi zamanzi ezi-8 mihla le',
        exercise: 'Ukuzilolonga',
        takeWalk: 'Thatha ukuhamba kwemizuzu engama-30 namhlanje',
        medication: 'Amayeza',
        eveningMeds: 'Thatha amayeza akho akusihlwa ngo-8 PM',
        logout: 'Phuma',
        logoutConfirm: 'Uqinisekile ukuba ufuna ukuphuma?'
      },
      st: {
        dashboard: 'Boto ya ka ya Bophelo ba Ubuntu',
        appointments: 'Dikopano tsa Ubuntu',
        medications: 'Meriana ya Setso le ya Kajeno',
        startConsultation: 'Qala Puisano ya Ubuntu',
        callNow: 'Letsetsa ho 10177 (Tshohanyetso) / 10010',
        chatBot: 'Buisana le Mothusi wa Ubuntu wa Bophelo',
        emergencyContact: 'Thuso ya Ubuntu ya Tshohanyetso',
        viewAll: 'Bona Tsohle - Moya wa Ubuntu',
        greeting: 'Sawubona, Nomsa Mthembu',
        ubuntuPhrase: '"Ubuntu ngumuntu ngabantu" - Ke motho ka batho',
        nextAppointment: 'Kopano e Latelang',
        upcomingMeds: 'Meriana e Tlang',
        healthStatus: 'Boemo ba Bophelo',
        recentActivity: 'Ketso ya Haufinyane',
        voiceTriage: 'Tlhahlobo ka Lentswe',
        chatMonitor: 'Sebatli sa Puisano',
        availableLanguages: 'E fumaneha dipolelong tsohle tse 11 tsa SA',
        confirmed: 'E netefatsitswe',
        pending: 'E emetse',
        delivered: 'E isitswe',
        inTransit: 'E tseleng',
        followUp: 'Tlatsetso',
        specialistConsultation: 'Puisano le Setsebi',
        nextDelivery: 'Ho isa ho latelang',
        dosage: 'Tekanyo',
        status: 'Boemo',
        facility: 'Setsi',
        date: 'Letsatsi',
        time: 'Nako',
        logout: 'Tswa',
        logoutConfirm: 'Na o netefatse hore o batla ho tswa?'
      },
      tn: {
        dashboard: 'Boto ya me ya Boitekanelo ba Ubuntu',
        appointments: 'Dikopano tsa Ubuntu',
        medications: 'Ditwatsi tsa Setso le tsa Segompieno',
        startConsultation: 'Simolola Puisano ya Ubuntu',
        callNow: 'Letsa kwa 10177 (Kgogakgolo) / 10010',
        chatBot: 'Buisana le Mothusi wa Ubuntu wa Boitekanelo',
        emergencyContact: 'Thuso ya Ubuntu ya Kgogakgolo',
        viewAll: 'Bona Tsotlhe - Mowa wa Ubuntu',
        greeting: 'Sawubona, Nomsa Mthembu',
        ubuntuPhrase: '"Ubuntu ngumuntu ngabantu" - Ke motho ka batho',
        nextAppointment: 'Kopano e e Latelang',
        upcomingMeds: 'Ditwatsi tse di Tlang',
        healthStatus: 'Maemo a Boitekanelo',
        recentActivity: 'Tiro ya Bosheng',
        voiceTriage: 'Tlhatlhobo ka Lentswe',
        chatMonitor: 'Sebatli sa Puisano',
        availableLanguages: 'E bonala mo dipuong tsotlhe tse 11 tsa SA',
        confirmed: 'E netefaditswe',
        pending: 'E letile',
        delivered: 'E isiwa',
        inTransit: 'E mo tseleng',
        followUp: 'Tlatsetso',
        specialistConsultation: 'Puisano le Setsebi',
        nextDelivery: 'Go isa go latelang',
        dosage: 'Tekanyo',
        status: 'Maemo',
        facility: 'Lefelo',
        date: 'Letlha',
        time: 'Nako',
        logout: 'Tswa',
        logoutConfirm: 'A o netefaditswe gore o batla go tswa?'
      },
      ss: {
        dashboard: 'Libhodi lami le-Ubuntu Lekuphila',
        appointments: 'Emacuketfo e-Ubuntu',
        medications: 'Imitsi Yesiko Neyesimanje',
        startConsultation: 'Cala Inkhulumisano ye-Ubuntu',
        callNow: 'Shayela ku-10177 (Sigcino) / 10010',
        chatBot: 'Khuluma noMsiti we-Ubuntu Wekuphila',
        emergencyContact: 'Lusito lwe-Ubuntu Lwesigcino',
        viewAll: 'Bona Konke - Umoya we-Ubuntu',
        greeting: 'Sawubona, Nomsa Mthembu',
        ubuntuPhrase: '"Ubuntu ngumuntu ngabantu" - Ngingumuntu ngebantfu',
        nextAppointment: 'Licuketfo Lelilandzelako',
        upcomingMeds: 'Imitsi Letako',
        healthStatus: 'Simo Sekuphila',
        recentActivity: 'Umsebenzi Wakamuva',
        voiceTriage: 'Kuhlola Ngelivi',
        chatMonitor: 'Sibuki Selikhulumisano',
        availableLanguages: 'Siyatfolakala kuwo wonke emalimi e-SA angu-11',
        confirmed: 'Kucinisekisiwe',
        pending: 'Kulindile',
        delivered: 'Kulethiwe',
        inTransit: 'Kusendleleni',
        followUp: 'Kulandzelela',
        specialistConsultation: 'Inkhulumisano Nechwepheshe',
        nextDelivery: 'Kuletsa Okulandzelako',
        dosage: 'Inani',
        status: 'Simo',
        facility: 'Sikhungo',
        date: 'Lusuku',
        time: 'Sikhatsi',
        logout: 'Phuma',
        logoutConfirm: 'Ucinisekile kutsi ufuna kuphuma?'
      },
      ve: {
        dashboard: 'Bodo yanga ya Vhutshilo ha Ubuntu',
        appointments: 'Mishumo ya Ubuntu',
        medications: 'Mishonga ya Ndeme na ya Zwino',
        startConsultation: 'Thoma Ambano ya Ubuntu',
        callNow: 'Vhidzani kha 10177 (Tshiitisi) / 10010',
        chatBot: 'Ambani na Muthusi wa Ubuntu wa Vhutshilo',
        emergencyContact: 'Thuso ya Ubuntu ya Tshiitisi',
        viewAll: 'Vhonani Zwothe - Muya wa Ubuntu',
        greeting: 'Sawubona, Nomsa Mthembu',
        ubuntuPhrase: '"Ubuntu ngumuntu ngabantu" - Ndi muthu nga vhathu',
        nextAppointment: 'Mushumo wa Tevhela',
        upcomingMeds: 'Mishonga ya u da',
        healthStatus: 'Zwithu zwa Vhutshilo',
        recentActivity: 'Mushumo wa Zwino',
        voiceTriage: 'U sedzulusa nga Ipfi',
        chatMonitor: 'Mulanguli wa Ambano',
        availableLanguages: 'Zwi a wanala kha luambo lwothe lwa SA lu-11',
        confirmed: 'Zwo khwathehelwa',
        pending: 'Zwo lindela',
        delivered: 'Zwo iswa',
        inTransit: 'Zwo kha nzila',
        followUp: 'Tevhedzano',
        specialistConsultation: 'Ambano na Tshirunzi',
        nextDelivery: 'U isa ha tevhela',
        dosage: 'Tshikalo',
        status: 'Zwithu',
        facility: 'Fhethu',
        date: 'Datumu',
        time: 'Tshifhinga',
        logout: 'Bva',
        logoutConfirm: 'Ni a khou tenda uri ni khou toda u bva?'
      },
      ts: {
        dashboard: 'Bodo ra mina ra Rihanyu ra Ubuntu',
        appointments: 'Minkarhi ya Ubuntu',
        medications: 'Mirhi ya Ndhavuko na ya Sweswi',
        startConsultation: 'Sungula Vulavurisano bya Ubuntu',
        callNow: 'Ringeta eka 10177 (Xiphiqo) / 10010',
        chatBot: 'Vulavula na Mupfuni wa Ubuntu wa Rihanyu',
        emergencyContact: 'Mpfuno wa Ubuntu wa Xiphiqo',
        viewAll: 'Vona Hinkwaswo - Moya wa Ubuntu',
        greeting: 'Sawubona, Nomsa Mthembu',
        ubuntuPhrase: '"Ubuntu ngumuntu ngabantu" - Ndzi munhu hi vanhu',
        nextAppointment: 'Nkarhi wo Landzelela',
        upcomingMeds: 'Mirhi ya le Mahlweni',
        healthStatus: 'Xiyimo xa Rihanyu',
        recentActivity: 'Nghingiriko wa Sweswi',
        voiceTriage: 'Ku Kambela hi Rito',
        chatMonitor: 'Mulanguti wa Vulavurisano',
        availableLanguages: 'Yi kumeka eka tindzimi hinkwato ta SA ti-11',
        confirmed: 'Swi tiyisisiwe',
        pending: 'Swi rindile',
        delivered: 'Swi hundzisiwe',
        inTransit: 'Swi endleleni',
        followUp: 'Ku Landzelela',
        specialistConsultation: 'Vulavurisano na Nwina-vutivi',
        nextDelivery: 'Ku hundzisa loku landzelaka',
        dosage: 'Mpimo',
        status: 'Xiyimo',
        facility: 'Ndhawu',
        date: 'Siku',
        time: 'Nkarhi',
        logout: 'Huma',
        logoutConfirm: 'Xana u tiyisekile leswaku u lava ku huma?'
      },
      nd: {
        dashboard: 'Ibhodi lami le-Ubuntu Lempilo',
        appointments: 'Ama-Appointment e-Ubuntu',
        medications: 'Imithi Yesiko Neyesimanje',
        startConsultation: 'Qalisa Inkulumo ye-Ubuntu',
        callNow: 'Fonela ku-10177 (Isimo Esiphuthumayo) / 10010',
        chatBot: 'Khuluma noMsizi we-Ubuntu Wempilo',
        emergencyContact: 'Usizo lwe-Ubuntu Lwesimo Esiphuthumayo',
        viewAll: 'Bona Konke - Umoya we-Ubuntu',
        greeting: 'Sawubona, Nomsa Mthembu',
        ubuntuPhrase: '"Ubuntu ngumuntu ngabantu" - Ngingumuntu ngabantu',
        nextAppointment: 'I-Appointment Elandelayo',
        upcomingMeds: 'Imithi Ezayo',
        healthStatus: 'Isimo Sempilo',
        recentActivity: 'Umsebenzi Wakamuva',
        voiceTriage: 'Ukuhlola Ngelizwi',
        chatMonitor: 'Umqaphi Wenkulumo',
        availableLanguages: 'Iyatholakala ngazo zonke izilimi ze-SA ezingu-11',
        confirmed: 'Kuqinisekisiwe',
        pending: 'Kulindile',
        delivered: 'Kulethiwe',
        inTransit: 'Kusendleleni',
        followUp: 'Ukulandelela',
        specialistConsultation: 'Inkulumo Nochwepheshe',
        nextDelivery: 'Ukulethwa Okulandelayo',
        dosage: 'Umthamo',
        status: 'Isimo',
        facility: 'Isikhungo',
        date: 'Usuku',
        time: 'Isikhathi',
        logout: 'Phuma',
        logoutConfirm: 'Uqinisekile ukuthi ufuna ukuphuma?'
      },
      nr: {
        dashboard: 'Boto ya ka ya Bophelo ba Ubuntu',
        appointments: 'Dikopano tsa Ubuntu',
        medications: 'Dihlare tsa Setso le tsa Kajeno',
        startConsultation: 'Thoma Puisano ya Ubuntu',
        callNow: 'Letsetsa ho 10177 (Tshohanyetso) / 10010',
        chatBot: 'Buisana le Mothusi wa Ubuntu wa Bophelo',
        emergencyContact: 'Thuso ya Ubuntu ya Tshohanyetso',
        viewAll: 'Bona Tsohle - Moya wa Ubuntu',
        greeting: 'Sawubona, Nomsa Mthembu',
        ubuntuPhrase: '"Ubuntu ngumuntu ngabantu" - Ke motho ka batho',
        nextAppointment: 'Kopano e Latelang',
        upcomingMeds: 'Dihlare tse di Tlang',
        healthStatus: 'Boemo ba Bophelo',
        recentActivity: 'Ketso ya Haufinyane',
        voiceTriage: 'Tlhahlobo ka Lentswe',
        chatMonitor: 'Sebatli sa Puisano',
        availableLanguages: 'E fumaneha dipolelong tsohle tse 11 tsa SA',
        confirmed: 'E netefatsitswe',
        pending: 'E emetse',
        delivered: 'E isitswe',
        inTransit: 'E tseleng',
        followUp: 'Tlatsetso',
        specialistConsultation: 'Puisano le Setsebi',
        nextDelivery: 'Ho isa ho latelang',
        dosage: 'Tekanyo',
        status: 'Boemo',
        facility: 'Setsi',
        date: 'Letsatsi',
        time: 'Nako',
        logout: 'Tswa',
        logoutConfirm: 'Na o netefatse hore o batla ho tswa?'
      }
    };
    return translations[selectedLanguage as keyof typeof translations]?.[key] || translations.en[key];
  };

  if (showSymptomChecker) {
    return <SymptomChecker onBack={() => setShowSymptomChecker(false)} language={selectedLanguage} />;
  }

  return (
    <div className="min-h-screen relative overflow-hidden" style={{
      background: `
        linear-gradient(135deg,
          rgba(34, 139, 34, 0.9) 0%,    /* Green */
          rgba(255, 215, 0, 0.8) 20%,   /* Gold */
          rgba(255, 69, 0, 0.8) 40%,    /* Orange */
          rgba(220, 20, 60, 0.8) 60%,   /* Red */
          rgba(0, 0, 128, 0.8) 80%,     /* Blue */
          rgba(75, 0, 130, 0.9) 100%    /* Indigo */
        )
      `
    }}>
      {/* Ubuntu Pattern Overlay */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundImage: `
          radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
          radial-gradient(circle at 75% 75%, rgba(255,215,0,0.2) 3px, transparent 3px),
          radial-gradient(circle at 50% 50%, rgba(255,255,255,0.05) 1px, transparent 1px)
        `,
        backgroundSize: '120px 120px, 180px 180px, 90px 90px'
      }} />

      <div className="container mx-auto px-4 py-4 relative z-10">
        <div className="max-w-7xl mx-auto space-y-4">
          {/* Ubuntu Header */}
          <div className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className="flex items-center gap-3">
              <div style={{
                background: 'linear-gradient(135deg, #228B22, #FFD700)',
                padding: '0.5rem',
                borderRadius: '0.75rem',
                boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                border: '1px solid rgba(255,215,0,0.5)'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                  <Heart className="w-4 h-4 text-white" />
                  <span style={{ fontSize: '1rem' }}>🤝</span>
                </div>
              </div>
              <div>
                <h1 style={{
                  fontSize: '1.5rem',
                  fontWeight: 'bold',
                  color: 'white',
                  textShadow: '1px 1px 2px rgba(0,0,0,0.7)',
                  marginBottom: '0.25rem'
                }}>
                  {getLanguageText('dashboard')}
                </h1>
                <p style={{
                  color: '#FFD700',
                  fontSize: '0.875rem',
                  fontWeight: 'bold',
                  textShadow: '1px 1px 2px rgba(0,0,0,0.6)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem'
                }}>
                  <Sparkles className="w-3 h-3" />
                  {getLanguageText('greeting')} 🌍
                </p>
                <p style={{
                  color: 'white',
                  fontSize: '0.75rem',
                  fontStyle: 'italic',
                  textShadow: '1px 1px 1px rgba(0,0,0,0.6)',
                  marginTop: '0.25rem'
                }}>
                  {getLanguageText('ubuntuPhrase')}
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              <div style={{
                background: 'rgba(0,0,0,0.3)',
                padding: '0.5rem',
                borderRadius: '0.75rem',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,215,0,0.5)'
              }}>
                <LanguageSelector selected={selectedLanguage} onSelect={setSelectedLanguage} />
              </div>
              <Button
                onClick={() => setShowMonitoringChat(true)}
                style={{
                  background: 'linear-gradient(135deg, #228B22, #32CD32)',
                  padding: '0.5rem 0.75rem',
                  borderRadius: '0.75rem',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255,215,0,0.5)',
                  color: 'white',
                  fontWeight: 'medium',
                  textShadow: '1px 1px 1px rgba(0,0,0,0.6)',
                  boxShadow: '0 2px 8px rgba(34,139,34,0.2)',
                  transition: 'all 0.2s ease',
                  fontSize: '0.875rem'
                }}
                className="hover:scale-102 hover:shadow-md"
              >
                <MessageCircle className="w-4 h-4 mr-1" />
                Chat Monitor
                <Badge className="ml-1 bg-white/20 text-white text-xs px-1 py-0.5">
                  {monitoringChatbotService.getActiveSessions().length}
                </Badge>
              </Button>
              {onLogout && (
                <Button
                  onClick={() => {
                    if (window.confirm(getLanguageText('logoutConfirm'))) {
                      onLogout();
                    }
                  }}
                  style={{
                    background: 'linear-gradient(135deg, #DC143C, #B22222)',
                    padding: '0.5rem 0.75rem',
                    borderRadius: '0.75rem',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255,215,0,0.5)',
                    color: 'white',
                    fontWeight: 'medium',
                    textShadow: '1px 1px 1px rgba(0,0,0,0.6)',
                    boxShadow: '0 2px 8px rgba(220,20,60,0.2)',
                    transition: 'all 0.2s ease',
                    fontSize: '0.875rem'
                  }}
                  className="hover:scale-102 hover:shadow-md"
                  title={getLanguageText('logout')}
                >
                  <span style={{ fontSize: '0.875rem', marginRight: '0.25rem' }}>🚪</span>
                  {getLanguageText('logout')}
                </Button>
              )}
            </div>
          </div>

          {/* Ubuntu Health Overview Cards */}
          <div className={`grid grid-cols-1 md:grid-cols-4 gap-3 mb-4 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className="group relative">
              <div style={{
                position: 'absolute',
                inset: '-2px',
                background: 'linear-gradient(135deg, #228B22, #32CD32, #FFD700)',
                borderRadius: '0.75rem',
                filter: 'blur(4px)',
                opacity: 0.6,
                transition: 'all 0.5s ease'
              }} className="group-hover:opacity-80"></div>
              <div style={{
                position: 'relative',
                padding: '0.75rem',
                textAlign: 'center',
                background: 'rgba(255,255,255,0.95)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255,215,0,0.6)',
                borderRadius: '0.75rem',
                boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                transition: 'all 0.3s ease'
              }} className="hover:scale-102">
                <div style={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: '1.5rem',
                  height: '1.5rem',
                  background: 'linear-gradient(135deg, rgba(34,139,34,0.2), rgba(255,215,0,0.2))',
                  borderRadius: '50%',
                  marginRight: '-0.75rem',
                  marginTop: '-0.75rem'
                }}></div>
                <CircularProgress value={85} size={50} color="green" className="mb-2" />
                <h3 style={{
                  fontWeight: 'bold',
                  fontSize: '0.875rem',
                  color: '#228B22',
                  marginBottom: '0.25rem',
                  textShadow: '1px 1px 1px rgba(0,0,0,0.2)'
                }}>
                  Ubuntu Health Score
                </h3>
                <p style={{
                  fontSize: '0.75rem',
                  color: '#8B4513',
                  fontWeight: 'medium',
                  textShadow: '1px 1px 1px rgba(0,0,0,0.1)'
                }}>
                  Impilo enhle - Excellent
                </p>
                <div style={{
                  position: 'absolute',
                  bottom: '0.5rem',
                  right: '0.5rem'
                }}>
                  <div style={{
                    width: '0.5rem',
                    height: '0.5rem',
                    background: '#228B22',
                    borderRadius: '50%',
                    animation: 'pulse 2s infinite',
                    boxShadow: '0 0 5px rgba(34,139,34,0.5)'
                  }}></div>
                </div>
              </div>
            </div>

            <div className="group relative">
              <div style={{
                position: 'absolute',
                inset: '-2px',
                background: 'linear-gradient(135deg, #000080, #4169E1, #FFD700)',
                borderRadius: '0.75rem',
                filter: 'blur(4px)',
                opacity: 0.6,
                transition: 'all 0.5s ease'
              }} className="group-hover:opacity-80"></div>
              <div style={{
                position: 'relative',
                padding: '0.75rem',
                textAlign: 'center',
                background: 'rgba(255,255,255,0.95)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255,215,0,0.6)',
                borderRadius: '0.75rem',
                boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                transition: 'all 0.3s ease'
              }} className="hover:scale-102">
                <div style={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: '1.5rem',
                  height: '1.5rem',
                  background: 'linear-gradient(135deg, rgba(0,0,128,0.2), rgba(255,215,0,0.2))',
                  borderRadius: '50%',
                  marginRight: '-0.75rem',
                  marginTop: '-0.75rem'
                }}></div>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', marginBottom: '0.5rem' }}>
                  <div style={{
                    padding: '0.5rem',
                    background: 'linear-gradient(135deg, #000080, #4169E1)',
                    borderRadius: '50%',
                    boxShadow: '0 2px 8px rgba(0,0,128,0.2)'
                  }}>
                    <Activity className="w-5 h-5 text-white filter drop-shadow-sm" />
                  </div>
                </div>
                <h3 style={{
                  fontWeight: 'bold',
                  fontSize: '0.875rem',
                  color: '#000080',
                  marginBottom: '0.25rem',
                  textShadow: '1px 1px 1px rgba(0,0,0,0.2)'
                }}>
                  Inhliziyo - Heart Rate
                </h3>
                <p style={{
                  fontSize: '1.125rem',
                  fontWeight: 'bold',
                  color: '#4169E1',
                  marginBottom: '0.125rem',
                  textShadow: '1px 1px 1px rgba(0,0,0,0.2)'
                }}>
                  72 BPM
                </p>
                <p style={{
                  fontSize: '0.75rem',
                  color: '#8B4513',
                  fontWeight: 'medium',
                  textShadow: '1px 1px 1px rgba(0,0,0,0.1)'
                }}>
                  Kulungile - Normal range
                </p>
                <div style={{
                  position: 'absolute',
                  bottom: '0.5rem',
                  right: '0.5rem'
                }}>
                  <div style={{
                    width: '0.5rem',
                    height: '0.5rem',
                    background: '#000080',
                    borderRadius: '50%',
                    animation: 'pulse 2s infinite',
                    boxShadow: '0 0 5px rgba(0,0,128,0.5)'
                  }}></div>
                </div>
              </div>
            </div>

            <div className="group relative">
              <div style={{
                position: 'absolute',
                inset: '-2px',
                background: 'linear-gradient(135deg, #DC143C, #FF6347, #FFD700)',
                borderRadius: '0.75rem',
                filter: 'blur(4px)',
                opacity: 0.6,
                transition: 'all 0.5s ease'
              }} className="group-hover:opacity-80"></div>
              <div style={{
                position: 'relative',
                padding: '0.75rem',
                textAlign: 'center',
                background: 'rgba(255,255,255,0.95)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255,215,0,0.6)',
                borderRadius: '0.75rem',
                boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                transition: 'all 0.3s ease'
              }} className="hover:scale-102">
                <div style={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: '1.5rem',
                  height: '1.5rem',
                  background: 'linear-gradient(135deg, rgba(220,20,60,0.2), rgba(255,215,0,0.2))',
                  borderRadius: '50%',
                  marginRight: '-0.75rem',
                  marginTop: '-0.75rem'
                }}></div>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', marginBottom: '0.5rem' }}>
                  <div style={{
                    padding: '0.5rem',
                    background: 'linear-gradient(135deg, #DC143C, #FF6347)',
                    borderRadius: '50%',
                    boxShadow: '0 2px 8px rgba(220,20,60,0.2)'
                  }}>
                    <Pill className="w-5 h-5 text-white filter drop-shadow-sm" />
                  </div>
                </div>
                <h3 style={{
                  fontWeight: 'bold',
                  fontSize: '0.875rem',
                  color: '#DC143C',
                  marginBottom: '0.25rem',
                  textShadow: '1px 1px 1px rgba(0,0,0,0.2)'
                }}>
                  Imithi - Medications
                </h3>
                <p style={{
                  fontSize: '1.125rem',
                  fontWeight: 'bold',
                  color: '#FF6347',
                  marginBottom: '0.125rem',
                  textShadow: '1px 1px 1px rgba(0,0,0,0.2)'
                }}>
                  2 Active
                </p>
                <p style={{
                  fontSize: '0.75rem',
                  color: '#8B4513',
                  fontWeight: 'medium',
                  textShadow: '1px 1px 1px rgba(0,0,0,0.1)'
                }}>
                  Ngesikhathi - On schedule
                </p>
                <div style={{
                  position: 'absolute',
                  bottom: '0.5rem',
                  right: '0.5rem'
                }}>
                  <div style={{
                    width: '0.5rem',
                    height: '0.5rem',
                    background: '#DC143C',
                    borderRadius: '50%',
                    animation: 'pulse 2s infinite',
                    boxShadow: '0 0 5px rgba(220,20,60,0.5)'
                  }}></div>
                </div>
              </div>
            </div>

            <div className="group relative">
              <div style={{
                position: 'absolute',
                inset: '-2px',
                background: 'linear-gradient(135deg, #FF8C00, #FFD700, #228B22)',
                borderRadius: '0.75rem',
                filter: 'blur(4px)',
                opacity: 0.6,
                transition: 'all 0.5s ease'
              }} className="group-hover:opacity-80"></div>
              <div style={{
                position: 'relative',
                padding: '0.75rem',
                textAlign: 'center',
                background: 'rgba(255,255,255,0.95)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255,215,0,0.6)',
                borderRadius: '0.75rem',
                boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                transition: 'all 0.3s ease'
              }} className="hover:scale-102">
                <div style={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: '1.5rem',
                  height: '1.5rem',
                  background: 'linear-gradient(135deg, rgba(255,140,0,0.2), rgba(255,215,0,0.2))',
                  borderRadius: '50%',
                  marginRight: '-0.75rem',
                  marginTop: '-0.75rem'
                }}></div>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', marginBottom: '0.5rem' }}>
                  <div style={{
                    padding: '0.5rem',
                    background: 'linear-gradient(135deg, #FF8C00, #FFD700)',
                    borderRadius: '50%',
                    boxShadow: '0 2px 8px rgba(255,140,0,0.2)'
                  }}>
                    <Calendar className="w-5 h-5 text-white filter drop-shadow-sm" />
                  </div>
                </div>
                <h3 style={{
                  fontWeight: 'bold',
                  fontSize: '0.875rem',
                  color: '#FF8C00',
                  marginBottom: '0.25rem',
                  textShadow: '1px 1px 1px rgba(0,0,0,0.2)'
                }}>
                  Usuku - Next Visit
                </h3>
                <p style={{
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  color: '#DAA520',
                  marginBottom: '0.125rem',
                  textShadow: '1px 1px 1px rgba(0,0,0,0.2)'
                }}>
                  June 15
                </p>
                <p style={{
                  fontSize: '0.75rem',
                  color: '#8B4513',
                  fontWeight: 'medium',
                  textShadow: '1px 1px 1px rgba(0,0,0,0.1)'
                }}>
                  Ezinsukwini ezi-3 - In 3 days
                </p>
                <div style={{
                  position: 'absolute',
                  bottom: '0.5rem',
                  right: '0.5rem'
                }}>
                  <div style={{
                    width: '0.5rem',
                    height: '0.5rem',
                    background: '#FF8C00',
                    borderRadius: '50%',
                    animation: 'pulse 2s infinite',
                    boxShadow: '0 0 5px rgba(255,140,0,0.5)'
                  }}></div>
                </div>
              </div>
            </div>
          </div>

        {/* Quick Actions - Compact Professional Design */}
        <div className={`grid grid-cols-1 md:grid-cols-3 gap-3 transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="group relative" onClick={() => setShowSymptomChecker(true)}>
            <div className="absolute -inset-0.5 bg-gradient-to-r from-green-400 via-emerald-500 to-teal-500 rounded-xl blur opacity-60 group-hover:opacity-80 transition duration-500"></div>
            <div className="relative cursor-pointer">
              <Card className="bg-white/95 backdrop-blur-xl border border-green-300/60 hover:scale-102 transition-all duration-300 shadow-lg hover:shadow-green-500/20 rounded-xl overflow-hidden">
                <CardContent className="p-4 text-center relative">
                  <div className="absolute top-0 right-0 w-12 h-12 bg-gradient-to-br from-green-400/20 to-emerald-500/20 rounded-full -mr-6 -mt-6"></div>
                  <div className="absolute top-0 left-0 w-8 h-8 bg-gradient-to-br from-teal-400/15 to-green-500/15 rounded-full -ml-4 -mt-4"></div>

                  <div className="relative z-10">
                    <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full shadow-md mb-2 mx-auto w-fit">
                      <MessageSquare className="w-6 h-6 text-white filter drop-shadow-sm" />
                    </div>
                    <h3 className="font-black text-sm mb-1 text-gray-900" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.2)'}}>{getLanguageText('chatBot')}</h3>
                    <p className="text-gray-700 font-medium text-xs" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>{getLanguageText('availableLanguages')}</p>
                    <div className="absolute bottom-2 right-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-ping shadow-md"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="group relative">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-400 via-cyan-500 to-blue-600 rounded-xl blur opacity-60 group-hover:opacity-80 transition duration-500"></div>
            <div className="relative">
              <Card className="bg-white/95 backdrop-blur-xl border border-blue-300/60 hover:scale-102 transition-all duration-300 shadow-lg hover:shadow-blue-500/20 rounded-xl overflow-hidden">
                <CardContent className="p-4 text-center relative">
                  <div className="absolute top-0 right-0 w-8 h-8 bg-gradient-to-br from-blue-400/20 to-cyan-500/20 rounded-full -mr-4 -mt-4"></div>
                  <div className="absolute top-0 left-0 w-6 h-6 bg-gradient-to-br from-cyan-400/15 to-blue-500/15 rounded-full -ml-3 -mt-3"></div>

                  <div className="relative z-10">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-full shadow-md mb-2 mx-auto w-fit">
                      <Phone className="w-5 h-5 text-white filter drop-shadow-sm" />
                    </div>
                    <h3 className="font-black text-sm mb-2 text-gray-900" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.2)'}}>Voice AI Triage</h3>
                    <VoiceTriageButton className="w-full text-xs py-1.5" />
                    <div className="absolute bottom-2 right-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse shadow-md"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="group relative">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-red-500 via-pink-500 to-red-600 rounded-xl blur opacity-60 group-hover:opacity-80 transition duration-500"></div>
            <div className="relative cursor-pointer">
              <Card className="bg-gradient-to-br from-red-500 to-red-600 text-white border-0 hover:scale-102 transition-all duration-300 shadow-lg hover:shadow-red-500/30 rounded-xl overflow-hidden">
                <CardContent className="p-4 text-center relative">
                  <div className="absolute top-0 right-0 w-8 h-8 bg-white/10 rounded-full -mr-4 -mt-4"></div>
                  <div className="absolute top-0 left-0 w-6 h-6 bg-white/5 rounded-full -ml-3 -mt-3"></div>

                  <div className="relative z-10">
                    <div className="p-2 bg-white/20 backdrop-blur-sm rounded-full shadow-md mb-2 mx-auto w-fit">
                      <Bell className="w-5 h-5 text-white filter drop-shadow-sm animate-pulse-slow" />
                    </div>
                    <h3 className="font-black text-sm mb-1 text-white" style={{textShadow: '1px 1px 2px rgba(0,0,0,0.7)'}}>{getLanguageText('emergencyContact')}</h3>
                    <p className="text-white font-medium text-xs" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.6)'}}>Immediate assistance</p>
                    <div className="absolute bottom-2 right-2">
                      <div className="w-2 h-2 bg-white rounded-full animate-ping shadow-md"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Main Content - Compact Professional Design */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Appointments */}
          <div className="group relative">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-400 via-indigo-500 to-purple-500 rounded-xl blur opacity-50 group-hover:opacity-70 transition duration-500"></div>
            <div className="relative p-0 overflow-hidden bg-white/95 backdrop-blur-xl border border-blue-200/60 rounded-xl shadow-lg hover:shadow-blue-500/20 transition-all duration-300">
              <div className="p-3 border-b border-blue-200/50 bg-gradient-to-r from-blue-50/80 to-indigo-50/80 backdrop-blur-sm">
                <div className="flex items-center gap-2 mb-1">
                  <div className="p-1.5 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full shadow-md">
                    <Calendar className="w-4 h-4 text-white filter drop-shadow-sm" />
                  </div>
                  <h3 className="text-base font-black text-gray-900" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.2)'}}>{getLanguageText('appointments')}</h3>
                </div>
                <p className="text-gray-700 font-medium text-xs" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>Your scheduled healthcare visits</p>
              </div>
              <div className="p-3 space-y-2">
                {appointments.map((appointment) => (
                  <div key={appointment.id} className="group/item relative">
                    <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-300 to-indigo-300 rounded-lg blur opacity-40 group-hover/item:opacity-60 transition duration-300"></div>
                    <div className="relative flex items-center justify-between p-2 bg-white/90 backdrop-blur-sm rounded-lg border border-blue-200/60 shadow-md hover:shadow-blue-400/15 transition-all duration-200 hover:scale-101">
                      <div className="flex-1">
                        <h4 className="font-black text-sm text-gray-900 mb-0.5" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>{getLanguageText(appointment.type === 'Follow-up' ? 'followUp' : 'specialistConsultation')}</h4>
                        <p className="text-xs text-gray-700 font-medium flex items-center gap-1 mt-0.5" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>
                          <MapPin className="w-3 h-3 text-blue-600 filter drop-shadow-sm" />
                          {appointment.facility}
                        </p>
                        <p className="text-xs text-gray-700 font-medium flex items-center gap-1 mt-0.5" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>
                          <Clock className="w-3 h-3 text-indigo-600 filter drop-shadow-sm" />
                          {appointment.date} at {appointment.time}
                        </p>
                      </div>
                      <Badge variant={appointment.status === 'confirmed' ? 'default' : 'secondary'} className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0 font-medium shadow-md text-xs px-2 py-0.5">
                        {getLanguageText(appointment.status)}
                      </Badge>
                    </div>
                  </div>
                ))}
                <Button variant="outline" className="w-full bg-white/90 border border-blue-300 text-blue-700 hover:bg-blue-50 font-medium shadow-md text-sm py-2 rounded-lg transition-all duration-200 hover:scale-102">{getLanguageText('viewAll')}</Button>
              </div>
            </div>
          </div>

          {/* Medications */}
          <div className="group relative">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-green-400 via-emerald-500 to-teal-500 rounded-xl blur opacity-50 group-hover:opacity-70 transition duration-500"></div>
            <div className="relative p-0 overflow-hidden bg-white/95 backdrop-blur-xl border border-green-200/60 rounded-xl shadow-lg hover:shadow-green-500/20 transition-all duration-300">
              <div className="p-3 border-b border-green-200/50 bg-gradient-to-r from-green-50/80 to-emerald-50/80 backdrop-blur-sm">
                <div className="flex items-center gap-2 mb-1">
                  <div className="p-1.5 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full shadow-md">
                    <Pill className="w-4 h-4 text-white filter drop-shadow-sm" />
                  </div>
                  <h3 className="text-base font-black text-gray-900" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.2)'}}>{getLanguageText('medications')}</h3>
                </div>
                <p className="text-gray-700 font-medium text-xs" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>Your current prescriptions and delivery status</p>
              </div>
              <div className="p-3 space-y-2">
                {medications.map((medication, index) => (
                  <div key={index} className="group/item relative">
                    <div className="absolute -inset-0.5 bg-gradient-to-r from-green-300 to-emerald-300 rounded-lg blur opacity-40 group-hover/item:opacity-60 transition duration-300"></div>
                    <div className="relative p-2 bg-white/90 backdrop-blur-sm rounded-lg border border-green-200/60 shadow-md hover:shadow-green-400/15 transition-all duration-200 hover:scale-101">
                      <div className="flex justify-between items-start mb-1">
                        <h4 className="font-black text-sm text-gray-900" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>{medication.name}</h4>
                        <Badge variant={medication.status === 'delivered' ? 'default' : 'secondary'} className="bg-gradient-to-r from-green-500 to-emerald-600 text-white border-0 font-medium shadow-md text-xs px-2 py-0.5">
                          {getLanguageText(medication.status === 'delivered' ? 'delivered' : 'inTransit')}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-700 font-medium mb-0.5" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>{medication.dosage}</p>
                      <p className="text-xs text-gray-700 font-medium" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>
                        {getLanguageText('nextDelivery')}: {medication.nextDelivery}
                      </p>
                    </div>
                  </div>
                ))}
                <Button variant="outline" className="w-full bg-white/90 border border-green-300 text-green-700 hover:bg-green-50 font-medium shadow-md text-sm py-2 rounded-lg transition-all duration-200 hover:scale-102">{getLanguageText('viewAll')}</Button>
              </div>
            </div>
          </div>
        </div>

        {/* Health Tips - Compact Professional Design */}
        <div className="group relative">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-400 via-pink-500 to-indigo-500 rounded-xl blur opacity-50 group-hover:opacity-70 transition duration-500"></div>
          <div className="relative p-0 overflow-hidden bg-white/95 backdrop-blur-xl border border-purple-200/60 rounded-xl shadow-lg hover:shadow-purple-500/20 transition-all duration-300">
            <div className="p-3 border-b border-purple-200/50 bg-gradient-to-r from-purple-50/80 to-pink-50/80 backdrop-blur-sm">
              <div className="flex items-center gap-2 mb-1">
                <div className="p-1.5 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full shadow-md">
                  <Sparkles className="w-4 h-4 text-white filter drop-shadow-sm" />
                </div>
                <h3 className="text-base font-black text-gray-900" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.2)'}}>Daily Health Tips</h3>
              </div>
              <p className="text-gray-700 font-medium text-xs" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>Personalized recommendations based on your health profile</p>
            </div>
            <div className="p-3">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                <div className="group/tip relative">
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-300 to-cyan-300 rounded-lg blur opacity-40 group-hover/tip:opacity-60 transition duration-300"></div>
                  <div className="relative p-2 bg-gradient-to-br from-blue-50/90 to-cyan-50/90 backdrop-blur-sm rounded-lg border border-blue-300/70 shadow-md hover:shadow-blue-400/20 transition-all duration-200 hover:scale-102">
                    <h4 className="font-black text-sm text-blue-900 mb-1" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>💧 Hydration</h4>
                    <p className="text-xs text-blue-800 font-medium" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>Drink at least 8 glasses of water daily</p>
                  </div>
                </div>
                <div className="group/tip relative">
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-green-300 to-emerald-300 rounded-lg blur opacity-40 group-hover/tip:opacity-60 transition duration-300"></div>
                  <div className="relative p-2 bg-gradient-to-br from-green-50/90 to-emerald-50/90 backdrop-blur-sm rounded-lg border border-green-300/70 shadow-md hover:shadow-green-400/20 transition-all duration-200 hover:scale-102">
                    <h4 className="font-black text-sm text-green-900 mb-1" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>🏃‍♂️ Exercise</h4>
                    <p className="text-xs text-green-800 font-medium" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>Take a 30-minute walk today</p>
                  </div>
                </div>
                <div className="group/tip relative">
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-300 to-pink-300 rounded-lg blur opacity-40 group-hover/tip:opacity-60 transition duration-300"></div>
                  <div className="relative p-2 bg-gradient-to-br from-purple-50/90 to-pink-50/90 backdrop-blur-sm rounded-lg border border-purple-300/70 shadow-md hover:shadow-purple-400/20 transition-all duration-200 hover:scale-102">
                    <h4 className="font-black text-sm text-purple-900 mb-1" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>💊 Medication</h4>
                    <p className="text-xs text-purple-800 font-medium" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>Take your evening medication at 8 PM</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Voice Triage History - Compact Professional Design */}
        <div className={`transition-all duration-1000 delay-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <VoiceTriageHistory patientId="nomsa-mthembu-001" maxItems={5} />
        </div>
        </div>
      </div>

      {/* AI Health Assistant Chat */}
      {showSymptomChecker && (
        <SymptomChecker
          onBack={() => setShowSymptomChecker(false)}
          language={selectedLanguage}
        />
      )}

      {/* Monitoring Chat Interface */}
      {showMonitoringChat && (
        <MonitoringChatInterface onClose={() => setShowMonitoringChat(false)} />
      )}
    </div>
  );
};

export default PatientDashboard;
