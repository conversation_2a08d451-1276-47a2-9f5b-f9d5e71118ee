#!/usr/bin/env python3
"""
Ubuntu Health Connect SA - Simple Backend API
Database-focused backend for patient management and AI interactions
"""

import os
import json
import logging
import uuid
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS

# Import database manager
from database.db_manager import DatabaseManager

# Initialize Flask app
app = Flask(__name__)

# Enable CORS for frontend-backend communication
CORS(app, origins=['http://localhost:8081', 'http://localhost:8082', 'http://127.0.0.1:8081', 'http://127.0.0.1:8082'])

# Initialize database
db = DatabaseManager()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

logger.info("🚀 Ubuntu Health Backend API Server starting...")
logger.info("✅ Database manager initialized")
logger.info("✅ CORS enabled for frontend connections")

@app.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint with database status
    """
    try:
        # Test database connection
        db_connected = db.test_connection()
        db_stats = db.get_database_stats() if db_connected else {}
        
        return jsonify({
            "status": "healthy" if db_connected else "degraded",
            "service": "HealthConnect SA Voice Assistant",
            "timestamp": datetime.now().isoformat(),
            "database": {
                "connected": db_connected,
                "stats": db_stats
            },
            "features": {
                "patient_api": True,
                "frontend_backend_integration": True,
                "database_persistence": db_connected
            },
            "endpoints": {
                "health": "/health",
                "create_patient": "POST /api/patients",
                "get_patient": "GET /api/patients/<id_number>",
                "get_all_patients": "GET /api/patients",
                "create_ai_interaction": "POST /api/patients/<id>/ai-interactions",
                "get_ai_interactions": "GET /api/patients/<id>/ai-interactions"
            }
        })
    except Exception as e:
        logger.error(f"❌ Health check error: {e}")
        return jsonify({
            "status": "unhealthy",
            "service": "HealthConnect SA Voice Assistant",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }), 500

@app.route('/', methods=['GET'])
def index():
    """
    Basic info endpoint
    """
    return jsonify({
        "service": "HealthConnect SA Backend API",
        "description": "Database backend for Ubuntu Health Connect SA",
        "version": "1.0.0",
        "endpoints": {
            "/health": "Health check with database status",
            "/api/patients": "Patient management endpoints",
            "/api/patients/<id>/ai-interactions": "AI interaction endpoints"
        }
    })

# Patient API Endpoints
@app.route('/api/patients', methods=['POST'])
def create_patient():
    """
    Create a new patient record in database
    """
    try:
        data = request.get_json()
        
        # Log patient registration
        logger.info(f"📝 Registering new patient: {data.get('first_name')} {data.get('last_name')} (ID: {data.get('id_number')})")
        
        # Prepare patient data for database
        patient_data = {
            'id': data.get('patient_id'),
            'first_name': data.get('first_name'),
            'last_name': data.get('last_name'),
            'id_number': data.get('id_number'),
            'phone_number': data.get('phone_number'),
            'email': data.get('email', ''),
            'date_of_birth': data.get('date_of_birth'),
            'age': data.get('age'),
            'gender': data.get('gender'),
            'address': data.get('address', ''),
            'emergency_contact_name': data.get('emergency_contact_name', ''),
            'emergency_contact_phone': data.get('emergency_contact_phone', ''),
            'emergency_contact_relationship': data.get('emergency_contact_relationship', '')
        }
        
        # Check if patient already exists
        existing_patient = db.get_patient_by_id_number(patient_data['id_number'])
        if existing_patient:
            # Update existing patient
            updates = {k: v for k, v in patient_data.items() if k != 'id'}
            success = db.update_patient(existing_patient['id'], updates)
            
            if success:
                logger.info(f"✅ Updated existing patient: {existing_patient['id']}")
                return jsonify({
                    'success': True,
                    'patient_id': existing_patient['id'],
                    'message': 'Patient updated successfully in database',
                    'action': 'updated'
                }), 200
            else:
                raise Exception("Failed to update existing patient")
        else:
            # Create new patient
            success = db.create_patient(patient_data)
            
            if success:
                logger.info(f"✅ Patient registered in database: {patient_data['id']}")
                
                # Add medical history if provided
                medical_history = data.get('medical_history', [])
                for condition in medical_history:
                    if isinstance(condition, dict) and 'condition_name' in condition:
                        db.add_medical_condition(patient_data['id'], condition)
                
                return jsonify({
                    'success': True,
                    'patient_id': patient_data['id'],
                    'message': 'Patient registered successfully in database',
                    'action': 'created'
                }), 201
            else:
                raise Exception("Failed to create patient in database")
        
    except Exception as e:
        logger.error(f"❌ Error registering patient: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/patients/<id_number>', methods=['GET'])
def get_patient(id_number):
    """
    Get patient by SA ID number from database
    """
    try:
        logger.info(f"🔍 Looking up patient by ID: {id_number}")
        
        # Get patient from database
        patient_data = db.get_patient_by_id_number(id_number)
        
        if patient_data:
            # Get additional data
            medical_history = db.get_medical_history(patient_data['id'])
            ai_interactions = db.get_ai_interactions_by_patient(patient_data['id'])
            
            # Format response
            response_data = {
                'patient_id': patient_data['id'],
                'first_name': patient_data['first_name'],
                'last_name': patient_data['last_name'],
                'id_number': patient_data['id_number'],
                'phone_number': patient_data['phone_number'],
                'email': patient_data['email'],
                'date_of_birth': patient_data['date_of_birth'],
                'age': patient_data['age'],
                'gender': patient_data['gender'],
                'address': patient_data['address'],
                'emergency_contact_name': patient_data['emergency_contact_name'],
                'emergency_contact_phone': patient_data['emergency_contact_phone'],
                'emergency_contact_relationship': patient_data['emergency_contact_relationship'],
                'medical_history': medical_history,
                'ai_interactions': ai_interactions,
                'created_at': patient_data['created_at'],
                'updated_at': patient_data['updated_at'],
                'found_in_backend': True
            }
            
            logger.info(f"✅ Patient found in database: {patient_data['id']}")
            return jsonify(response_data), 200
        else:
            logger.info(f"❌ Patient not found in database: {id_number}")
            return jsonify({
                'success': False,
                'error': 'Patient not found',
                'found_in_backend': False
            }), 404
        
    except Exception as e:
        logger.error(f"❌ Error getting patient: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/patients', methods=['GET'])
def get_all_patients():
    """
    Get all patients with optional search
    """
    try:
        search_term = request.args.get('search', '')
        provider_id = request.args.get('provider_id', 'PROV001')
        
        logger.info(f"🔍 Getting all patients (search: '{search_term}', provider: {provider_id})")
        
        # Get patients from database
        patients = db.search_patients(search_term, provider_id)
        
        # Format response
        response_data = []
        for patient in patients:
            # Get additional data for each patient
            medical_history = db.get_medical_history(patient['id'])
            ai_interactions = db.get_ai_interactions_by_patient(patient['id'])
            
            patient_data = {
                'patient_id': patient['id'],
                'first_name': patient['first_name'],
                'last_name': patient['last_name'],
                'id_number': patient['id_number'],
                'phone_number': patient['phone_number'],
                'email': patient['email'],
                'age': patient['age'],
                'gender': patient['gender'],
                'address': patient['address'],
                'medical_history_count': len(medical_history),
                'ai_interactions_count': len(ai_interactions),
                'created_at': patient['created_at'],
                'updated_at': patient['updated_at']
            }
            response_data.append(patient_data)
        
        logger.info(f"✅ Found {len(response_data)} patients")
        
        return jsonify({
            'success': True,
            'patients': response_data,
            'count': len(response_data),
            'search_term': search_term
        }), 200
        
    except Exception as e:
        logger.error(f"❌ Error getting patients: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/patients/<patient_id>/ai-interactions', methods=['POST'])
def create_ai_interaction(patient_id):
    """
    Create AI interaction record
    """
    try:
        data = request.get_json()
        
        logger.info(f"📝 Creating AI interaction for patient: {patient_id}")
        
        # Prepare interaction data
        interaction_data = {
            'id': str(uuid.uuid4()),
            'patient_id': patient_id,
            'interaction_type': data.get('interaction_type', 'chat'),
            'summary': data.get('summary', ''),
            'full_conversation': data.get('full_conversation', ''),
            'ai_assessment': data.get('ai_assessment', ''),
            'severity': data.get('severity', 'Low'),
            'recommendations': data.get('recommendations', ''),
            'urgent_care': data.get('urgent_care', False)
        }
        
        # Save to database
        success = db.create_ai_interaction(interaction_data)
        
        if success:
            logger.info(f"✅ AI interaction created: {interaction_data['id']}")
            return jsonify({
                'success': True,
                'interaction_id': interaction_data['id'],
                'message': 'AI interaction recorded successfully'
            }), 201
        else:
            raise Exception("Failed to create AI interaction")
        
    except Exception as e:
        logger.error(f"❌ Error creating AI interaction: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/patients/<patient_id>/ai-interactions', methods=['GET'])
def get_ai_interactions(patient_id):
    """
    Get AI interactions for a patient
    """
    try:
        logger.info(f"🔍 Getting AI interactions for patient: {patient_id}")
        
        interactions = db.get_ai_interactions_by_patient(patient_id)
        
        logger.info(f"✅ Found {len(interactions)} AI interactions")
        
        return jsonify({
            'success': True,
            'interactions': interactions,
            'count': len(interactions)
        }), 200
        
    except Exception as e:
        logger.error(f"❌ Error getting AI interactions: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# General AI Interactions Endpoints (for urgent cases dashboard)
@app.route('/api/ai-interactions', methods=['GET'])
def get_all_ai_interactions():
    """
    Get all AI interactions (for urgent cases dashboard)
    """
    try:
        logger.info("🔍 Getting all AI interactions")

        interactions = db.get_all_ai_interactions()

        logger.info(f"✅ Found {len(interactions)} AI interactions")

        return jsonify({
            'success': True,
            'data': {
                'interactions': interactions,
                'count': len(interactions)
            }
        }), 200

    except Exception as e:
        logger.error(f"❌ Error getting all AI interactions: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/ai-interactions', methods=['POST'])
def create_general_ai_interaction():
    """
    Create AI interaction (general endpoint)
    """
    try:
        data = request.get_json()

        logger.info(f"📝 Creating AI interaction for patient: {data.get('patient_id')}")

        # Prepare interaction data
        interaction_data = {
            'id': data.get('id', str(uuid.uuid4())),
            'patient_id': data.get('patient_id'),
            'interaction_type': data.get('interaction_type', 'chat'),
            'summary': data.get('summary', ''),
            'full_conversation': data.get('full_conversation', ''),
            'ai_assessment': data.get('ai_assessment', ''),
            'severity': data.get('severity', 'Low'),
            'recommendations': data.get('recommendations', ''),
            'urgent_care': data.get('urgent_care', False)
        }

        # Save to database
        success = db.create_ai_interaction(interaction_data)

        if success:
            logger.info(f"✅ AI interaction created: {interaction_data['id']}")
            return jsonify({
                'success': True,
                'interaction_id': interaction_data['id'],
                'message': 'AI interaction recorded successfully'
            }), 201
        else:
            raise Exception("Failed to create AI interaction")

    except Exception as e:
        logger.error(f"❌ Error creating AI interaction: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/ai-interactions', methods=['DELETE'])
def clear_all_ai_interactions():
    """
    Clear all AI interactions (for testing)
    """
    try:
        logger.info("🗑️ Clearing all AI interactions")

        success = db.clear_all_ai_interactions()

        if success:
            logger.info("✅ All AI interactions cleared")
            return jsonify({
                'success': True,
                'message': 'All AI interactions cleared successfully'
            }), 200
        else:
            raise Exception("Failed to clear AI interactions")

    except Exception as e:
        logger.error(f"❌ Error clearing AI interactions: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    logger.info("🚀 Starting Ubuntu Health Backend API Server...")
    logger.info("📊 Database initialized with sample data")
    logger.info("🌐 Server starting on http://localhost:5000")
    logger.info("✅ Ready for frontend connections!")
    
    app.run(
        host='127.0.0.1',
        port=5001,
        debug=True
    )
