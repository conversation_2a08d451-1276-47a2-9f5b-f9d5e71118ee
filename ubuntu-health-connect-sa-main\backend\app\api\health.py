"""
Health check endpoints for Ubuntu Health Connect SA
"""

from flask import Blueprint, jsonify
from datetime import datetime
from app.database import get_db

bp = Blueprint('health', __name__)

@bp.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint with database status
    """
    try:
        # Test database connection
        db = get_db()
        db_connected = db.test_connection()
        db_stats = db.get_database_stats() if db_connected else {}
        
        return jsonify({
            "status": "healthy" if db_connected else "degraded",
            "service": "Ubuntu Health Connect SA API",
            "version": "2.0.0",
            "timestamp": datetime.now().isoformat(),
            "database": {
                "connected": db_connected,
                "stats": db_stats
            },
            "features": {
                "patient_api": True,
                "ai_interactions": True,
                "frontend_backend_integration": True,
                "database_persistence": db_connected
            },
            "endpoints": {
                "health": "/health",
                "patients": "/api/patients",
                "patient_by_id": "/api/patients/<id_number>",
                "ai_interactions": "/api/patients/<id>/ai-interactions"
            }
        })
    except Exception as e:
        return jsonify({
            "status": "unhealthy",
            "service": "Ubuntu Health Connect SA API",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }), 500

@bp.route('/', methods=['GET'])
def index():
    """
    API information endpoint
    """
    return jsonify({
        "service": "Ubuntu Health Connect SA API",
        "description": "Healthcare management system API for South African healthcare providers",
        "version": "2.0.0",
        "documentation": "/docs",
        "endpoints": {
            "/health": "Health check with database status",
            "/api/patients": "Patient management endpoints",
            "/api/patients/<id>/ai-interactions": "AI interaction endpoints"
        },
        "features": [
            "Patient registration and management",
            "AI-powered health assessments",
            "Medical history tracking",
            "Healthcare provider integration",
            "South African healthcare compliance"
        ]
    })
