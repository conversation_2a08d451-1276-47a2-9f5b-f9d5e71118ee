/**
 * Shared Types Index
 * Central export point for all shared types
 */

// Export API types
export * from './api';

// Export constants
export const API_ENDPOINTS = {
  HEALTH: '/health',
  PATIENTS: '/api/patients',
  AI_INTERACTIONS: '/api/ai-interactions',
} as const;

export const PATIENT_GENDERS = ['Male', 'Female', 'Other'] as const;
export const MEDICAL_SEVERITIES = ['Mild', 'Moderate', 'Severe', 'Critical'] as const;
export const MEDICAL_STATUSES = ['Active', 'Chronic', 'Under Treatment', 'Resolved'] as const;
export const AI_SEVERITIES = ['Low', 'Moderate', 'High', 'Critical'] as const;
export const INTERACTION_TYPES = ['chat', 'voice', 'assessment', 'monitoring'] as const;

// South African specific constants
export const SA_LANGUAGES = [
  'en', 'af', 'zu', 'xh', 'st', 'tn', 'ts', 've', 'ss', 'nr', 'nd'
] as const;

export const SA_PROVINCES = [
  'Eastern Cape',
  'Free State', 
  'Gauteng',
  'KwaZulu-Natal',
  'Limpopo',
  'Mpumalanga',
  'Northern Cape',
  'North West',
  'Western Cape'
] as const;
