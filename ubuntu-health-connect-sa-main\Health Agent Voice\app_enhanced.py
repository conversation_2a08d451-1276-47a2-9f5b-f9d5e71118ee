#!/usr/bin/env python3
"""
Enhanced Ubuntu Health Connect SA Backend
Improved version with better structure and error handling
"""

import os
import sys
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import database manager
from database.db_manager import DatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'ubuntu-health-connect-sa-secret-key')

# Configure CORS - Allow all origins for development
CORS(app,
     origins=['*'],  # Allow all origins for development
     allow_headers=['Content-Type', 'Authorization'],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

# Initialize database
db_manager = DatabaseManager()

# Enhanced response helpers
def success_response(data=None, status_code=200):
    """Create standardized success response"""
    response = {
        'success': True,
        'data': data,
        'timestamp': datetime.now().isoformat()
    }
    return jsonify(response), status_code

def error_response(message, status_code=400):
    """Create standardized error response"""
    response = {
        'success': False,
        'error': message,
        'timestamp': datetime.now().isoformat()
    }
    return jsonify(response), status_code

# Enhanced validation
def validate_patient_data(data):
    """Validate patient data"""
    errors = []
    
    # Required fields
    required_fields = ['first_name', 'last_name', 'id_number', 'phone_number']
    for field in required_fields:
        if not data.get(field):
            errors.append(f'{field} is required')
    
    # SA ID number validation (13 digits)
    id_number = data.get('id_number', '')
    if id_number and not (id_number.isdigit() and len(id_number) == 13):
        errors.append('SA ID number must be 13 digits')
    
    # Phone number validation
    phone = data.get('phone_number', '')
    if phone and not (phone.startswith(('0', '+27')) and len(phone.replace('+27', '0')) == 10):
        errors.append('Phone number must be valid SA format')
    
    return errors

# Enhanced patient service
class EnhancedPatientService:
    def __init__(self, db_manager):
        self.db = db_manager
    
    def create_or_update_patient(self, patient_data):
        """Create or update patient with enhanced logic"""
        try:
            # Generate patient ID if not provided
            if not patient_data.get('patient_id'):
                timestamp = int(datetime.now().timestamp() * 1000)
                patient_data['patient_id'] = f"PAT_{timestamp}"

            # Map patient_id to id for database compatibility
            db_patient_data = dict(patient_data)
            db_patient_data['id'] = db_patient_data.get('patient_id')

            # Check if patient exists
            existing_patient = self.db.get_patient_by_id_number(patient_data['id_number'])

            if existing_patient:
                # Update existing patient
                updates = {k: v for k, v in db_patient_data.items() if k not in ['id', 'patient_id']}
                success = self.db.update_patient(existing_patient['id'], updates)

                return {
                    'success': success,
                    'patient_id': existing_patient['id'],
                    'action': 'updated',
                    'message': 'Patient updated successfully'
                }
            else:
                # Create new patient
                success = self.db.create_patient(db_patient_data)

                return {
                    'success': success,
                    'patient_id': db_patient_data['id'],
                    'action': 'created',
                    'message': 'Patient created successfully'
                }

        except Exception as e:
            logger.error(f"Patient service error: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_patient_with_details(self, id_number):
        """Get patient with all related data"""
        try:
            patient = self.db.get_patient_by_id_number(id_number)
            if not patient:
                return None
            
            # Get additional data
            medical_history = self.db.get_medical_history(patient['id'])
            ai_interactions = self.db.get_ai_interactions_by_patient(patient['id'])
            
            # Format response
            patient_data = dict(patient)
            patient_data['medical_history'] = medical_history
            patient_data['ai_interactions'] = ai_interactions
            patient_data['found_in_backend'] = True
            
            return patient_data
        
        except Exception as e:
            logger.error(f"Get patient error: {e}")
            return None
    
    def search_patients_enhanced(self, search_term="", provider_id="PROV001"):
        """Enhanced patient search"""
        try:
            patients = self.db.search_patients(search_term, provider_id)
            
            # Add summary data for each patient
            enhanced_patients = []
            for patient in patients:
                patient_data = dict(patient)
                
                # Get counts
                medical_history = self.db.get_medical_history(patient['id'])
                ai_interactions = self.db.get_ai_interactions_by_patient(patient['id'])
                
                patient_data['medical_history_count'] = len(medical_history)
                patient_data['ai_interactions_count'] = len(ai_interactions)
                
                enhanced_patients.append(patient_data)
            
            return enhanced_patients
        
        except Exception as e:
            logger.error(f"Search patients error: {e}")
            return []

# Initialize enhanced service
patient_service = EnhancedPatientService(db_manager)

# ENHANCED API ROUTES

@app.route('/health', methods=['GET'])
def enhanced_health_check():
    """Enhanced health check with detailed status"""
    try:
        # Test database connection
        db_connected = db_manager.test_connection()
        db_stats = db_manager.get_database_stats() if db_connected else {}
        
        return jsonify({
            "status": "healthy" if db_connected else "degraded",
            "service": "Ubuntu Health Connect SA API - Enhanced",
            "version": "2.0.0-enhanced",
            "timestamp": datetime.now().isoformat(),
            "database": {
                "connected": db_connected,
                "stats": db_stats
            },
            "features": {
                "patient_api": True,
                "ai_interactions": True,
                "enhanced_validation": True,
                "caching_support": True,
                "error_handling": True
            },
            "endpoints": {
                "health": "/health",
                "patients": "/api/patients",
                "patient_by_id": "/api/patients/<id_number>",
                "ai_interactions": "/api/patients/<id>/ai-interactions"
            }
        })
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return jsonify({
            "status": "unhealthy",
            "service": "Ubuntu Health Connect SA API - Enhanced",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }), 500

@app.route('/api/patients', methods=['POST'])
def enhanced_create_patient():
    """Enhanced patient creation with validation"""
    try:
        data = request.get_json()
        if not data:
            return error_response('No data provided', 400)
        
        # Validate input data
        validation_errors = validate_patient_data(data)
        if validation_errors:
            return error_response(validation_errors, 400)
        
        # Use enhanced patient service
        result = patient_service.create_or_update_patient(data)
        
        if result['success']:
            logger.info(f"✅ Patient {result['action']}: {result['patient_id']}")
            return success_response(result, 201 if result['action'] == 'created' else 200)
        else:
            return error_response(result.get('error', 'Unknown error'), 500)
        
    except Exception as e:
        logger.error(f"❌ Error in enhanced_create_patient: {str(e)}")
        return error_response(str(e), 500)

@app.route('/api/patients/<id_number>', methods=['GET'])
def enhanced_get_patient(id_number):
    """Enhanced patient retrieval with full details"""
    try:
        logger.info(f"🔍 Enhanced: Looking up patient by ID: {id_number}")
        
        patient_data = patient_service.get_patient_with_details(id_number)
        
        if patient_data:
            logger.info(f"✅ Enhanced: Patient found: {patient_data['id']}")
            return success_response(patient_data)
        else:
            logger.info(f"❌ Enhanced: Patient not found: {id_number}")
            return error_response('Patient not found', 404)
        
    except Exception as e:
        logger.error(f"❌ Enhanced: Error getting patient: {str(e)}")
        return error_response(str(e), 500)

@app.route('/api/patients', methods=['GET'])
def enhanced_get_all_patients():
    """Enhanced patient listing with search"""
    try:
        search_term = request.args.get('search', '')
        provider_id = request.args.get('provider_id', 'PROV001')
        
        logger.info(f"🔍 Enhanced: Getting patients (search: '{search_term}', provider: {provider_id})")
        
        patients = patient_service.search_patients_enhanced(search_term, provider_id)
        
        logger.info(f"✅ Enhanced: Found {len(patients)} patients")
        
        return success_response({
            'patients': patients,
            'count': len(patients),
            'search_term': search_term
        })
        
    except Exception as e:
        logger.error(f"❌ Enhanced: Error getting patients: {str(e)}")
        return error_response(str(e), 500)

@app.route('/api/patients/<patient_id>/ai-interactions', methods=['POST'])
def enhanced_create_ai_interaction(patient_id):
    """Enhanced AI interaction creation"""
    try:
        data = request.get_json()
        if not data:
            return error_response('No data provided', 400)
        
        logger.info(f"📝 Enhanced: Creating AI interaction for patient: {patient_id}")
        
        # Prepare interaction data
        interaction_data = {
            'id': f"AI_{int(datetime.now().timestamp() * 1000)}",
            'patient_id': patient_id,
            'interaction_type': data.get('interaction_type', 'chat'),
            'summary': data.get('summary', ''),
            'full_conversation': data.get('full_conversation', ''),
            'ai_assessment': data.get('ai_assessment', ''),
            'severity': data.get('severity', 'Low'),
            'recommendations': data.get('recommendations', ''),
            'urgent_care': data.get('urgent_care', False)
        }
        
        # Save to database
        success = db_manager.create_ai_interaction(interaction_data)
        
        if success:
            logger.info(f"✅ Enhanced: AI interaction created: {interaction_data['id']}")
            return success_response({
                'interaction_id': interaction_data['id'],
                'message': 'AI interaction created successfully'
            }, 201)
        else:
            return error_response('Failed to create AI interaction', 500)
        
    except Exception as e:
        logger.error(f"❌ Enhanced: Error creating AI interaction: {str(e)}")
        return error_response(str(e), 500)

@app.route('/api/patients/<patient_id>/ai-interactions', methods=['GET'])
def enhanced_get_ai_interactions(patient_id):
    """Enhanced AI interaction retrieval"""
    try:
        logger.info(f"🤖 Enhanced: Getting AI interactions for patient: {patient_id}")
        
        interactions = db_manager.get_ai_interactions_by_patient(patient_id)
        
        logger.info(f"✅ Enhanced: Found {len(interactions)} AI interactions")
        
        return success_response({
            'interactions': interactions,
            'count': len(interactions)
        })
        
    except Exception as e:
        logger.error(f"❌ Enhanced: Error getting AI interactions: {str(e)}")
        return error_response(str(e), 500)

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return error_response('Endpoint not found', 404)

@app.errorhandler(500)
def internal_error(error):
    return error_response('Internal server error', 500)

def main():
    """Main application entry point"""
    logger.info("🚀 Ubuntu Health Connect SA - Enhanced Backend starting...")
    logger.info("📊 Environment: Enhanced Development")
    logger.info("🔧 Debug mode: True")
    logger.info("✅ Enhanced backend API server ready for frontend connections!")
    
    return app

if __name__ == '__main__':
    # Test database connection
    if not db_manager.test_connection():
        logger.error("❌ Database connection failed - exiting")
        sys.exit(1)
    
    logger.info("✅ Database connection successful")
    
    # Start the server
    print("🌐 Starting enhanced server on http://localhost:5000")
    print("📱 Frontend can connect from http://localhost:8081")
    print("🔧 Enhanced features: validation, caching, error handling")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=False
    )
