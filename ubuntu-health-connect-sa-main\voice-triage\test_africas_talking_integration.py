"""
Test Africa's Talking Integration
Verifies that the voice calling setup is properly configured
"""

import os
import sys
import logging
from datetime import datetime
import africastalking
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AfricasTalkingTester:
    """Test class for Africa's Talking voice integration"""
    
    def __init__(self):
        # Get credentials from environment
        self.username = os.getenv('AFRICAS_TALKING_USERNAME', 'sandbox')
        self.api_key = os.getenv('AFRICAS_TALKING_API_KEY')
        self.triage_number = os.getenv('TRIAGE_PHONE_NUMBER', '+27727803582')
        self.test_number = os.getenv('TEST_CALLER_NUMBER', '+27727803582')
        
        print(f"🔧 Testing Africa's Talking Integration")
        print(f"Username: {self.username}")
        print(f"API Key: {self.api_key[:10]}..." if self.api_key else "API Key: Not set")
        print(f"Triage Number: {self.triage_number}")
        print(f"Test Number: {self.test_number}")
        print("-" * 50)
        
        if not self.api_key:
            raise ValueError("AFRICAS_TALKING_API_KEY environment variable is required")
        
        # Initialize Africa's Talking
        try:
            africastalking.initialize(self.username, self.api_key)
            self.voice = africastalking.Voice
            self.sms = africastalking.SMS
            print("✅ Africa's Talking SDK initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize Africa's Talking SDK: {e}")
            raise
    
    def test_voice_call_setup(self):
        """Test voice call configuration"""
        print("\n📞 Testing Voice Call Setup...")
        
        try:
            # Test the voice service is accessible
            print(f"Voice service object: {self.voice}")
            print("✅ Voice service is accessible")
            
            # Validate phone number formats
            if not self.triage_number.startswith('+'):
                print(f"⚠️ Warning: Triage number should start with '+': {self.triage_number}")
            
            if not self.test_number.startswith('+'):
                print(f"⚠️ Warning: Test number should start with '+': {self.test_number}")
            
            return True
            
        except Exception as e:
            print(f"❌ Voice call setup test failed: {e}")
            return False
    
    def test_sms_service(self):
        """Test SMS service configuration"""
        print("\n📱 Testing SMS Service...")
        
        try:
            # Test the SMS service is accessible
            print(f"SMS service object: {self.sms}")
            print("✅ SMS service is accessible")
            return True
            
        except Exception as e:
            print(f"❌ SMS service test failed: {e}")
            return False
    
    def test_make_test_call(self, dry_run=True):
        """Test making an actual voice call"""
        print(f"\n📞 Testing Voice Call (Dry Run: {dry_run})...")
        
        if dry_run:
            print("🔍 This is a dry run - no actual call will be made")
            print(f"Would call from: {self.triage_number}")
            print(f"Would call to: {self.test_number}")
            print("✅ Dry run completed successfully")
            return True
        
        try:
            print(f"Making test call from {self.triage_number} to {self.test_number}")
            
            # Make the actual call using correct method signature
            response = self.voice.call(
                callFrom=self.triage_number,
                callTo=[self.test_number]
            )
            
            print(f"Call response: {response}")
            
            if response and len(response) > 0:
                call_info = response[0]
                print(f"✅ Call initiated successfully!")
                print(f"Session ID: {call_info.get('sessionId', 'N/A')}")
                print(f"Status: {call_info.get('status', 'N/A')}")
                return True
            else:
                print("❌ Call failed - no response from API")
                return False
                
        except Exception as e:
            print(f"❌ Call test failed: {e}")
            return False
    
    def test_send_test_sms(self, dry_run=True):
        """Test sending an SMS"""
        print(f"\n📱 Testing SMS (Dry Run: {dry_run})...")
        
        message = f"Ubuntu Health Test SMS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        if dry_run:
            print("🔍 This is a dry run - no actual SMS will be sent")
            print(f"Would send to: {self.test_number}")
            print(f"Message: {message}")
            print("✅ SMS dry run completed successfully")
            return True
        
        try:
            print(f"Sending test SMS to {self.test_number}")
            
            # Send the SMS
            response = self.sms.send(message, [self.test_number])
            
            print(f"SMS response: {response}")
            
            if response and 'SMSMessageData' in response:
                sms_data = response['SMSMessageData']
                recipients = sms_data.get('Recipients', [])
                
                if recipients and len(recipients) > 0:
                    recipient = recipients[0]
                    print(f"✅ SMS sent successfully!")
                    print(f"Message ID: {recipient.get('messageId', 'N/A')}")
                    print(f"Status: {recipient.get('status', 'N/A')}")
                    print(f"Cost: {recipient.get('cost', 'N/A')}")
                    return True
            
            print("❌ SMS failed - no valid response")
            return False
            
        except Exception as e:
            print(f"❌ SMS test failed: {e}")
            return False
    
    def test_webhook_urls(self):
        """Test webhook URL configuration"""
        print("\n🔗 Testing Webhook Configuration...")
        
        webhook_base = os.getenv('WEBHOOK_BASE_URL', 'https://your-domain.com')
        
        webhook_urls = {
            'voice_callback': f"{webhook_base}/api/voice-triage/callback",
            'recording_callback': f"{webhook_base}/api/voice-triage/recording",
            'dtmf_callback': f"{webhook_base}/api/voice-triage/provider-response",
            'sms_delivery': f"{webhook_base}/api/voice-triage/sms-delivery"
        }
        
        print("Required webhook URLs:")
        for name, url in webhook_urls.items():
            print(f"  {name}: {url}")
        
        if webhook_base == 'https://your-domain.com':
            print("⚠️ Warning: WEBHOOK_BASE_URL is not configured for production")
            print("   You need to set this to your actual domain or ngrok URL")
        else:
            print("✅ Webhook base URL is configured")
        
        return True
    
    def run_all_tests(self, include_real_calls=False):
        """Run all tests"""
        print("🚀 Starting Africa's Talking Integration Tests")
        print("=" * 60)
        
        results = {}
        
        # Test 1: Voice call setup
        results['voice_setup'] = self.test_voice_call_setup()
        
        # Test 2: SMS service
        results['sms_setup'] = self.test_sms_service()
        
        # Test 3: Webhook configuration
        results['webhooks'] = self.test_webhook_urls()
        
        # Test 4: Voice call (dry run by default)
        results['voice_call'] = self.test_make_test_call(dry_run=not include_real_calls)
        
        # Test 5: SMS (dry run by default)
        results['sms_send'] = self.test_send_test_sms(dry_run=not include_real_calls)
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name.replace('_', ' ').title()}: {status}")
            if result:
                passed += 1
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Your Africa's Talking integration is ready.")
        else:
            print("⚠️ Some tests failed. Please check the configuration.")
        
        return results

def main():
    """Main test function"""
    try:
        tester = AfricasTalkingTester()
        
        # Ask user if they want to make real calls/SMS
        print("\n" + "⚠️" * 20)
        print("IMPORTANT: This test can make real calls and send real SMS")
        print("This will use your Africa's Talking credits!")
        print("⚠️" * 20)
        
        user_input = input("\nDo you want to make REAL calls/SMS? (y/N): ").lower().strip()
        include_real_calls = user_input in ['y', 'yes']
        
        if include_real_calls:
            print("🚨 Making REAL calls and sending REAL SMS...")
        else:
            print("🔍 Running in DRY RUN mode (no real calls/SMS)")
        
        # Run tests
        results = tester.run_all_tests(include_real_calls=include_real_calls)
        
        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f'africas_talking_test_results_{timestamp}.json'
        
        import json
        with open(results_file, 'w') as f:
            json.dump({
                'timestamp': timestamp,
                'include_real_calls': include_real_calls,
                'results': results,
                'configuration': {
                    'username': tester.username,
                    'triage_number': tester.triage_number,
                    'test_number': tester.test_number
                }
            }, f, indent=2)
        
        print(f"\n📄 Test results saved to: {results_file}")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
