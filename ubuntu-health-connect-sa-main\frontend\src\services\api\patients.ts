/**
 * Patient API Service
 * Handles all patient-related API calls
 */

import { apiClient, ApiResponse } from './client';

// Patient Types
export interface Patient {
  patient_id: string;
  first_name: string;
  last_name: string;
  id_number: string;
  phone_number: string;
  email?: string;
  date_of_birth?: string;
  age?: number;
  gender?: 'Male' | 'Female' | 'Other';
  address?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  medical_history?: MedicalCondition[];
  ai_interactions?: AIInteraction[];
  created_at?: string;
  updated_at?: string;
  found_in_backend?: boolean;
}

export interface MedicalCondition {
  id?: number;
  condition_name: string;
  severity?: 'Mild' | 'Moderate' | 'Severe' | 'Critical';
  status?: 'Active' | 'Chronic' | 'Under Treatment' | 'Resolved';
  diagnosed_date?: string;
  notes?: string;
}

export interface AIInteraction {
  id: string;
  patient_id: string;
  interaction_type: 'chat' | 'voice' | 'assessment' | 'monitoring';
  summary?: string;
  full_conversation?: string;
  ai_assessment?: string;
  severity?: 'Low' | 'Moderate' | 'High' | 'Critical';
  recommendations?: string;
  urgent_care?: boolean;
  timestamp?: string;
}

export interface CreatePatientRequest {
  patient_id?: string;
  first_name: string;
  last_name: string;
  id_number: string;
  phone_number: string;
  email?: string;
  date_of_birth?: string;
  age?: number;
  gender?: 'Male' | 'Female' | 'Other';
  address?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  medical_history?: MedicalCondition[];
}

export interface PatientSearchResponse {
  patients: Patient[];
  count: number;
  search_term: string;
}

// Patient API Service Class
class PatientApiService {
  private readonly basePath = '/api/patients';

  /**
   * Create or update a patient
   */
  async createPatient(patientData: CreatePatientRequest): Promise<ApiResponse<{
    patient_id: string;
    action: 'created' | 'updated';
    message: string;
  }>> {
    console.log('📝 Creating/updating patient:', patientData.first_name, patientData.last_name);
    
    return apiClient.post(this.basePath, patientData);
  }

  /**
   * Get patient by SA ID number
   */
  async getPatientByIdNumber(idNumber: string): Promise<ApiResponse<Patient>> {
    console.log('🔍 Getting patient by ID number:', idNumber);
    
    return apiClient.get(`${this.basePath}/${idNumber}`);
  }

  /**
   * Search patients
   */
  async searchPatients(searchTerm: string = '', providerId: string = 'PROV001'): Promise<ApiResponse<PatientSearchResponse>> {
    console.log('🔍 Searching patients:', searchTerm);
    
    const params = new URLSearchParams();
    if (searchTerm) params.append('search', searchTerm);
    if (providerId) params.append('provider_id', providerId);
    
    return apiClient.get(`${this.basePath}?${params.toString()}`);
  }

  /**
   * Get all patients
   */
  async getAllPatients(providerId: string = 'PROV001'): Promise<ApiResponse<PatientSearchResponse>> {
    console.log('📋 Getting all patients for provider:', providerId);
    
    return this.searchPatients('', providerId);
  }

  /**
   * Update patient
   */
  async updatePatient(patientId: string, updates: Partial<Patient>): Promise<ApiResponse<{
    patient_id: string;
    message: string;
  }>> {
    console.log('✏️ Updating patient:', patientId);
    
    return apiClient.put(`${this.basePath}/${patientId}`, updates);
  }

  /**
   * Delete patient (soft delete)
   */
  async deletePatient(patientId: string): Promise<ApiResponse<{
    patient_id: string;
    message: string;
  }>> {
    console.log('🗑️ Deleting patient:', patientId);
    
    return apiClient.delete(`${this.basePath}/${patientId}`);
  }

  /**
   * Create AI interaction for patient
   */
  async createAIInteraction(patientId: string, interactionData: Omit<AIInteraction, 'id' | 'patient_id'>): Promise<ApiResponse<{
    interaction_id: string;
    message: string;
  }>> {
    console.log('🤖 Creating AI interaction for patient:', patientId);
    
    return apiClient.post(`${this.basePath}/${patientId}/ai-interactions`, interactionData);
  }

  /**
   * Get AI interactions for patient
   */
  async getPatientAIInteractions(patientId: string): Promise<ApiResponse<{
    interactions: AIInteraction[];
    count: number;
  }>> {
    console.log('🤖 Getting AI interactions for patient:', patientId);
    
    return apiClient.get(`${this.basePath}/${patientId}/ai-interactions`);
  }
}

// Create singleton instance
export const patientApiService = new PatientApiService();

// Export for debugging
(window as any).patientApiService = patientApiService;
