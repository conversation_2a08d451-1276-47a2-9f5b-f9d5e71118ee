#!/usr/bin/env python3
"""
Minimal Ubuntu Health Connect SA Backend
Simple working version for patient registration
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import os
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Create Flask app
app = Flask(__name__)

# Configure CORS - Allow all origins for development
CORS(app, origins=['*'], allow_headers=['Content-Type'], methods=['GET', 'POST', 'OPTIONS'])

# Simple in-memory storage for testing
patients_db = {}
ai_interactions_db = []

@app.route('/health', methods=['GET'])
def health_check():
    """Simple health check"""
    return jsonify({
        "status": "healthy",
        "service": "Ubuntu Health Connect SA API - Minimal",
        "timestamp": datetime.now().isoformat(),
        "database": {"connected": True}
    })

@app.route('/api/patients', methods=['POST', 'OPTIONS'])
def create_patient():
    """Simple patient creation"""
    if request.method == 'OPTIONS':
        return '', 200
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided',
                'timestamp': datetime.now().isoformat()
            }), 400
        
        # Generate patient ID if not provided
        patient_id = data.get('patient_id', f"PAT_{int(datetime.now().timestamp())}")
        
        # Store patient data
        patients_db[patient_id] = data
        
        # Return success response in expected format
        response = {
            'success': True,
            'data': {
                'patient_id': patient_id,
                'action': 'created',
                'message': 'Patient registered successfully'
            },
            'timestamp': datetime.now().isoformat()
        }
        
        print(f"✅ Patient registered: {patient_id}")
        return jsonify(response), 201
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/patients', methods=['GET'])
def get_patients():
    """Get all patients"""
    return jsonify({
        'success': True,
        'data': {
            'patients': list(patients_db.values()),
            'count': len(patients_db)
        },
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/patients/login', methods=['POST', 'OPTIONS'])
def patient_login():
    """Patient login endpoint"""
    if request.method == 'OPTIONS':
        return '', 200

    try:
        data = request.get_json()
        patient_id = data.get('patientId') or data.get('id_number')
        phone = data.get('phone') or data.get('phoneNumber')

        print(f"Patient login attempt: ID {patient_id}, Phone {phone}")

        # Check if patient exists
        if patient_id in patients_db:
            patient = patients_db[patient_id]
            print(f"Authenticating patient with database...")
            return jsonify({
                'success': True,
                'data': {
                    'patient': patient,
                    'authenticated': True
                },
                'timestamp': datetime.now().isoformat()
            })
        else:
            print(f"Patient not found, redirecting to registration")
            return jsonify({
                'success': False,
                'error': 'Patient not found. Please register first.',
                'redirect_to_registration': True,
                'timestamp': datetime.now().isoformat()
            }), 404

    except Exception as e:
        print(f"Login error: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/patients/lookup/<patient_id>', methods=['GET'])
def patient_lookup(patient_id):
    """Patient lookup by ID"""
    try:
        print(f"Patient lookup: {patient_id}")

        if patient_id in patients_db:
            patient = patients_db[patient_id]
            return jsonify({
                'success': True,
                'data': {
                    'patient': patient,
                    'found': True
                },
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Patient not found',
                'found': False,
                'timestamp': datetime.now().isoformat()
            }), 404

    except Exception as e:
        print(f"Lookup error: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/ai-interactions', methods=['POST', 'OPTIONS'])
def create_ai_interaction():
    """Create AI interaction (for urgent cases)"""
    if request.method == 'OPTIONS':
        return '', 200

    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided',
                'timestamp': datetime.now().isoformat()
            }), 400

        # Add timestamp and ID
        interaction = {
            **data,
            'id': f"ai_{int(datetime.now().timestamp())}",
            'timestamp': datetime.now().isoformat()
        }

        # Store interaction
        ai_interactions_db.append(interaction)

        print(f"✅ AI Interaction created: {interaction['id']} - Patient: {data.get('patient_id', 'Unknown')}")
        print(f"   Urgent: {data.get('urgent_care', False)}, Severity: {data.get('severity', 'Unknown')}")

        return jsonify({
            'success': True,
            'data': {
                'id': interaction['id'],
                'message': 'AI interaction created successfully'
            },
            'timestamp': datetime.now().isoformat()
        }), 201

    except Exception as e:
        print(f"❌ Error creating AI interaction: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/ai-interactions', methods=['GET'])
def get_ai_interactions():
    """Get all AI interactions"""
    return jsonify({
        'success': True,
        'data': {
            'interactions': ai_interactions_db,
            'count': len(ai_interactions_db)
        },
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/ai-interactions', methods=['DELETE'])
def clear_ai_interactions():
    """Clear all AI interactions (for testing)"""
    global ai_interactions_db
    ai_interactions_db = []

    print("🗑️ All AI interactions cleared")

    return jsonify({
        'success': True,
        'data': {
            'message': 'All AI interactions cleared',
            'count': 0
        },
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    # Get configuration from environment variables
    backend_port = int(os.getenv('FLASK_PORT', '5002'))  # Changed to 5002 to avoid conflicts
    backend_host = os.getenv('FLASK_HOST', '127.0.0.1')  # Changed to localhost to avoid permission issues
    frontend_port = os.getenv('VITE_FRONTEND_PORT', '8085')
    debug_mode = os.getenv('FLASK_DEBUG', 'true').lower() == 'true'

    print("Ubuntu Health Connect SA - Dynamic Backend")
    print(f"Starting backend on http://localhost:{backend_port}")
    print(f"Frontend expected on http://localhost:{frontend_port}")
    print(f"Debug mode: {debug_mode}")
    print("AI Interactions endpoint: /api/ai-interactions")
    print("Patients endpoint: /api/patients")
    print("Health check: /health")
    print("Backend ready for frontend connections!")

    app.run(
        host=backend_host,
        port=backend_port,
        debug=debug_mode,
        use_reloader=False
    )
