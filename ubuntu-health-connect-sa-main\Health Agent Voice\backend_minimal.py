#!/usr/bin/env python3
"""
Ubuntu Health Connect SA Backend
Enhanced version with proper database connectivity
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Add the database module to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))
from db_manager import DatabaseManager

# Load environment variables
load_dotenv()

# Create Flask app
app = Flask(__name__)

# Configure CORS - Allow all origins for development
CORS(app, origins=['*'], allow_headers=['Content-Type'], methods=['GET', 'POST', 'OPTIONS'])

# Initialize database manager with proper path
DB_PATH = os.path.join(os.path.dirname(__file__), '..', 'database', 'ubuntu_health.db')
db_manager = DatabaseManager(db_path=DB_PATH)

print(f"🗄️ Database initialized at: {DB_PATH}")
print(f"🔗 Database connection test: {'✅ Success' if db_manager.test_connection() else '❌ Failed'}")

@app.route('/health', methods=['GET'])
def health_check():
    """Enhanced health check with database connectivity"""
    db_connected = db_manager.test_connection()
    db_stats = db_manager.get_database_stats() if db_connected else {}

    return jsonify({
        "status": "healthy" if db_connected else "degraded",
        "service": "Ubuntu Health Connect SA API - Enhanced",
        "timestamp": datetime.now().isoformat(),
        "database": {
            "connected": db_connected,
            "path": DB_PATH,
            "stats": db_stats
        }
    })

@app.route('/api/patients', methods=['POST', 'OPTIONS'])
def create_patient():
    """Enhanced patient creation with database storage"""
    if request.method == 'OPTIONS':
        return '', 200

    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided',
                'timestamp': datetime.now().isoformat()
            }), 400

        # Prepare patient data for database
        patient_data = {
            'first_name': data.get('firstName', ''),
            'last_name': data.get('lastName', ''),
            'id_number': data.get('idNumber', ''),
            'phone_number': data.get('phone', ''),
            'email': data.get('email', ''),
            'date_of_birth': data.get('dateOfBirth', ''),
            'age': data.get('age', 0),
            'gender': data.get('gender', ''),
            'address': data.get('address', ''),
            'emergency_contact_name': data.get('emergencyContact', {}).get('name', ''),
            'emergency_contact_phone': data.get('emergencyContact', {}).get('phone', ''),
            'emergency_contact_relationship': data.get('emergencyContact', {}).get('relationship', '')
        }

        # Create patient in database
        patient_id = db_manager.create_patient(patient_data)

        if patient_id:
            response = {
                'success': True,
                'data': {
                    'patient_id': patient_id,
                    'action': 'created',
                    'message': 'Patient registered successfully in Ubuntu Health database'
                },
                'timestamp': datetime.now().isoformat()
            }

            print(f"✅ Patient registered in database: {patient_id}")
            return jsonify(response), 201
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to create patient in database',
                'timestamp': datetime.now().isoformat()
            }), 500

    except Exception as e:
        print(f"❌ Error creating patient: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/patients', methods=['GET'])
def get_patients():
    """Get all patients from database"""
    try:
        patients = db_manager.get_all_patients()
        return jsonify({
            'success': True,
            'data': {
                'patients': patients,
                'count': len(patients)
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        print(f"❌ Error getting patients: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/patients/login', methods=['POST', 'OPTIONS'])
def patient_login():
    """Patient login endpoint with database lookup"""
    if request.method == 'OPTIONS':
        return '', 200

    try:
        data = request.get_json()
        patient_id = data.get('patientId') or data.get('id_number')
        phone = data.get('phone') or data.get('phoneNumber')

        print(f"Patient login attempt: ID {patient_id}, Phone {phone}")

        # Check if patient exists in database
        patient = db_manager.get_patient_by_id_number(patient_id) if patient_id else None

        if patient:
            print(f"Authenticating patient from Ubuntu Health database...")
            return jsonify({
                'success': True,
                'data': {
                    'patient': patient,
                    'authenticated': True
                },
                'timestamp': datetime.now().isoformat()
            })
        else:
            print(f"Patient not found in database, redirecting to registration")
            return jsonify({
                'success': False,
                'error': 'Patient not found in Ubuntu Health database. Please register first.',
                'redirect_to_registration': True,
                'timestamp': datetime.now().isoformat()
            }), 404

    except Exception as e:
        print(f"Login error: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/patients/lookup/<patient_id>', methods=['GET'])
def patient_lookup(patient_id):
    """Patient lookup by ID from database"""
    try:
        print(f"Patient lookup in database: {patient_id}")

        patient = db_manager.get_patient_by_id_number(patient_id)

        if patient:
            return jsonify({
                'success': True,
                'data': {
                    'patient': patient,
                    'found': True
                },
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Patient not found in Ubuntu Health database',
                'found': False,
                'timestamp': datetime.now().isoformat()
            }), 404

    except Exception as e:
        print(f"Lookup error: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/ai-interactions', methods=['POST', 'OPTIONS'])
def create_ai_interaction():
    """Create AI interaction with database storage"""
    if request.method == 'OPTIONS':
        return '', 200

    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided',
                'timestamp': datetime.now().isoformat()
            }), 400

        # Prepare AI interaction data for database
        interaction_data = {
            'patient_id': data.get('patient_id', ''),
            'interaction_type': data.get('interaction_type', 'assessment'),
            'summary': data.get('summary', ''),
            'full_conversation': json.dumps(data.get('conversation', [])),
            'ai_assessment': data.get('ai_assessment', ''),
            'severity': data.get('severity', 'Low'),
            'recommendations': data.get('recommendations', ''),
            'urgent_care': data.get('urgent_care', False)
        }

        # Create AI interaction in database
        interaction_id = db_manager.create_ai_interaction(interaction_data)

        if interaction_id:
            print(f"✅ AI Interaction created in database: {interaction_id} - Patient: {data.get('patient_id', 'Unknown')}")
            print(f"   Urgent: {data.get('urgent_care', False)}, Severity: {data.get('severity', 'Unknown')}")

            return jsonify({
                'success': True,
                'data': {
                    'id': interaction_id,
                    'message': 'AI interaction created successfully in Ubuntu Health database'
                },
                'timestamp': datetime.now().isoformat()
            }), 201
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to create AI interaction in database',
                'timestamp': datetime.now().isoformat()
            }), 500

    except Exception as e:
        print(f"❌ Error creating AI interaction: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/ai-interactions', methods=['GET'])
def get_ai_interactions():
    """Get all AI interactions from database"""
    try:
        interactions = db_manager.get_all_ai_interactions()
        return jsonify({
            'success': True,
            'data': {
                'interactions': interactions,
                'count': len(interactions)
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        print(f"❌ Error getting AI interactions: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/ai-interactions', methods=['DELETE'])
def clear_ai_interactions():
    """Clear all AI interactions (for testing)"""
    try:
        # This would require implementing a clear method in db_manager
        # For now, just return success
        print("🗑️ AI interactions clear requested (not implemented for safety)")

        return jsonify({
            'success': True,
            'data': {
                'message': 'AI interactions clear requested (database preserved for safety)',
                'count': 0
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        print(f"❌ Error clearing AI interactions: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    # Get configuration from environment variables
    backend_port = int(os.getenv('FLASK_PORT', '5000'))
    backend_host = os.getenv('FLASK_HOST', '127.0.0.1')
    frontend_port = os.getenv('VITE_FRONTEND_PORT', '8085')
    debug_mode = os.getenv('FLASK_DEBUG', 'true').lower() == 'true'

    print("🏥 Ubuntu Health Connect SA - Enhanced Backend")
    print("=" * 60)
    print(f"🚀 Starting backend on http://localhost:{backend_port}")
    print(f"🌐 Frontend expected on http://localhost:{frontend_port}")
    print(f"🗄️ Database: {DB_PATH}")
    print(f"🔧 Debug mode: {debug_mode}")
    print("📋 Available endpoints:")
    print("   • Health check: /health")
    print("   • Patients: /api/patients")
    print("   • AI Interactions: /api/ai-interactions")
    print("   • Patient Login: /api/patients/login")
    print("   • Patient Lookup: /api/patients/lookup/<id>")
    print("=" * 60)
    print("✅ Backend ready for frontend connections!")
    print("🔗 All data will be stored in Ubuntu Health database")

    app.run(
        host=backend_host,
        port=backend_port,
        debug=debug_mode,
        use_reloader=False
    )
