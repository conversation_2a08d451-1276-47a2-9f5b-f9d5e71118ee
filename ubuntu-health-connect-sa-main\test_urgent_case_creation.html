<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Urgent Case Creation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .step {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .urgent {
            border-left-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
        }
        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .urgent-btn {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        .result {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Urgent Case Creation Test</h1>
        <p>This test will simulate creating an urgent case through the patient portal and verify it appears in the provider dashboard.</p>
        
        <div class="step">
            <h3>Step 1: Simulate Patient Registration</h3>
            <p>Set up a real patient session (not test data)</p>
            <button onclick="setupPatientSession()">Setup Real Patient Session</button>
            <div id="step1-result" class="result"></div>
        </div>
        
        <div class="step urgent">
            <h3>Step 2: Create Urgent Medical Report</h3>
            <p>Generate an urgent case using OpenAI with severe symptoms</p>
            <button class="urgent-btn" onclick="createUrgentCase()">Create Urgent Case</button>
            <div id="step2-result" class="result"></div>
        </div>
        
        <div class="step">
            <h3>Step 3: Verify Database Storage</h3>
            <p>Check if the urgent case was stored in the database</p>
            <button onclick="checkDatabase()">Check Database</button>
            <div id="step3-result" class="result"></div>
        </div>
        
        <div class="step">
            <h3>Step 4: Test Provider Dashboard</h3>
            <p>Verify the urgent case appears in the provider dashboard</p>
            <button onclick="testProviderDashboard()">Test Provider Dashboard</button>
            <div id="step4-result" class="result"></div>
        </div>
        
        <div class="step urgent">
            <h3>Step 5: Complete Flow Test</h3>
            <p>Run the complete urgent case flow end-to-end</p>
            <button class="urgent-btn" onclick="runCompleteTest()">Run Complete Test</button>
            <div id="step5-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
        }
        
        async function setupPatientSession() {
            log('step1-result', '🔄 Setting up real patient session...', 'info');
            
            try {
                // Simulate a real patient login
                const patientData = {
                    patientId: 'PAT_' + Date.now(),
                    idNumber: '9001015555083',
                    firstName: 'Thabo',
                    lastName: 'Mthembu',
                    cellNumber: '**********',
                    age: 35,
                    location: 'Johannesburg, Gauteng'
                };
                
                // Store in localStorage like the real app does
                localStorage.setItem('currentPatientId', patientData.patientId);
                localStorage.setItem('currentPatientIdNumber', patientData.idNumber);
                localStorage.setItem('currentPatientName', `${patientData.firstName} ${patientData.lastName}`);
                localStorage.setItem('currentPatientAge', patientData.age.toString());
                localStorage.setItem('currentPatientLocation', patientData.location);
                
                log('step1-result', '✅ Patient session created:', 'success');
                log('step1-result', `   Patient ID: ${patientData.patientId}`, 'success');
                log('step1-result', `   Name: ${patientData.firstName} ${patientData.lastName}`, 'success');
                log('step1-result', `   ID Number: ${patientData.idNumber}`, 'success');
                log('step1-result', '✅ Ready for urgent case creation', 'success');
                
            } catch (error) {
                log('step1-result', `❌ Error: ${error.message}`, 'error');
            }
        }
        
        async function createUrgentCase() {
            log('step2-result', '🚨 Creating urgent case with severe symptoms...', 'warning');
            
            try {
                // Simulate urgent conversation
                const urgentConversation = [
                    {
                        role: 'system',
                        content: 'You are a medical AI assistant. Analyze symptoms and provide medical assessment.'
                    },
                    {
                        role: 'user',
                        content: 'I am having severe chest pain that started 30 minutes ago. The pain is crushing and radiates to my left arm. I am also sweating and feel nauseous. The pain is 9/10.'
                    },
                    {
                        role: 'assistant',
                        content: 'These symptoms are very concerning and could indicate a heart attack. You need immediate emergency medical attention. Please call emergency services or have someone take you to the nearest hospital emergency department right away.'
                    },
                    {
                        role: 'user',
                        content: 'The pain is getting worse and I am having trouble breathing. Should I call an ambulance?'
                    },
                    {
                        role: 'assistant',
                        content: 'Yes, you should call an ambulance immediately. These are classic signs of a heart attack and require urgent medical intervention. Call emergency services now.'
                    }
                ];
                
                log('step2-result', '📤 Sending urgent case to OpenAI for analysis...', 'info');
                
                // Call OpenAI API to generate medical report
                const openaiResponse = await fetch(`${API_BASE}/api/openai/chat`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        model: 'gpt-4o-mini',
                        messages: [
                            {
                                role: 'system',
                                content: 'You are a professional medical AI assistant. Analyze this patient conversation and generate a comprehensive medical assessment report in JSON format. The patient is describing symptoms that require urgent medical attention.'
                            },
                            {
                                role: 'user',
                                content: `Please analyze this urgent patient conversation and generate a medical assessment report:\n\n${urgentConversation.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n\n')}`
                            }
                        ],
                        max_tokens: 1500,
                        temperature: 0.3
                    })
                });
                
                if (!openaiResponse.ok) {
                    throw new Error(`OpenAI API error: ${openaiResponse.status}`);
                }
                
                const openaiResult = await openaiResponse.json();
                if (!openaiResult.success) {
                    throw new Error(`OpenAI API failed: ${openaiResult.error}`);
                }
                
                const aiAnalysis = openaiResult.data.choices[0].message.content;
                log('step2-result', '✅ OpenAI analysis completed', 'success');
                log('step2-result', `📝 Analysis preview: ${aiAnalysis.substring(0, 200)}...`, 'info');
                
                // Create AI interaction record
                const patientId = localStorage.getItem('currentPatientId');
                const patientName = localStorage.getItem('currentPatientName');
                
                if (!patientId || !patientName) {
                    throw new Error('Patient session not found. Please run Step 1 first.');
                }
                
                const interactionData = {
                    patient_id: patientId,
                    interaction_type: 'urgent_medical_report',
                    summary: 'URGENT: Patient reported severe chest pain with classic heart attack symptoms - requires immediate emergency care',
                    full_conversation: JSON.stringify(urgentConversation),
                    ai_assessment: aiAnalysis,
                    severity: 'Critical',
                    recommendations: 'Call emergency services immediately, go to nearest hospital emergency department',
                    urgent_care: true
                };
                
                log('step2-result', '💾 Storing urgent case in database...', 'info');
                
                const storeResponse = await fetch(`${API_BASE}/api/ai-interactions`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(interactionData)
                });
                
                if (!storeResponse.ok) {
                    throw new Error(`Storage failed: ${storeResponse.status}`);
                }
                
                const storeResult = await storeResponse.json();
                if (!storeResult.success) {
                    throw new Error(`Storage error: ${storeResult.error}`);
                }
                
                log('step2-result', '✅ Urgent case created successfully!', 'success');
                log('step2-result', `🆔 Interaction ID: ${storeResult.data.interaction_id}`, 'success');
                log('step2-result', `👤 Patient: ${patientName}`, 'success');
                log('step2-result', '🚨 Severity: Critical (urgent_care = true)', 'warning');
                
            } catch (error) {
                log('step2-result', `❌ Error: ${error.message}`, 'error');
            }
        }
        
        async function checkDatabase() {
            log('step3-result', '🔍 Checking database for urgent cases...', 'info');
            
            try {
                // Check AI interactions
                const interactionsResponse = await fetch(`${API_BASE}/api/ai-interactions`);
                if (interactionsResponse.ok) {
                    const interactionsResult = await interactionsResponse.json();
                    if (interactionsResult.success) {
                        const count = interactionsResult.data.count;
                        log('step3-result', `📊 Total AI interactions: ${count}`, 'info');
                        
                        if (count > 0) {
                            const urgentCount = interactionsResult.data.interactions.filter(i => i.urgent_care).length;
                            log('step3-result', `🚨 Urgent interactions: ${urgentCount}`, urgentCount > 0 ? 'success' : 'warning');
                        }
                    }
                }
                
                // Check urgent cases endpoint
                const urgentResponse = await fetch(`${API_BASE}/api/urgent-cases`);
                if (urgentResponse.ok) {
                    const urgentResult = await urgentResponse.json();
                    if (urgentResult.success) {
                        const urgentCount = urgentResult.data.count;
                        log('step3-result', `🚨 Urgent cases endpoint: ${urgentCount} cases`, urgentCount > 0 ? 'success' : 'warning');
                        
                        if (urgentCount > 0) {
                            urgentResult.data.urgent_cases.forEach((case_, index) => {
                                log('step3-result', `   ${index + 1}. ${case_.patient_name}: ${case_.severity}`, 'success');
                            });
                        } else {
                            log('step3-result', '⚠️ No urgent cases found - may be filtered as test data', 'warning');
                        }
                    }
                } else {
                    log('step3-result', `❌ Urgent cases endpoint error: ${urgentResponse.status}`, 'error');
                }
                
            } catch (error) {
                log('step3-result', `❌ Error: ${error.message}`, 'error');
            }
        }
        
        async function testProviderDashboard() {
            log('step4-result', '🏥 Testing provider dashboard integration...', 'info');
            
            try {
                // Test frontend access
                const frontendResponse = await fetch('http://localhost:8085');
                if (frontendResponse.ok) {
                    log('step4-result', '✅ Frontend accessible', 'success');
                } else {
                    log('step4-result', '❌ Frontend not accessible', 'error');
                    return;
                }
                
                // Check if urgent cases would appear in provider dashboard
                log('step4-result', '🔍 Checking provider dashboard data sources...', 'info');
                
                // The provider dashboard gets data from patientCaseService
                // which loads from the AI interactions endpoint
                log('step4-result', '📋 Provider dashboard should show urgent cases from:', 'info');
                log('step4-result', '   1. patientCaseService.getUrgentCases()', 'info');
                log('step4-result', '   2. Filters: urgentCare=true OR severity=critical/high', 'info');
                log('step4-result', '   3. Real patient data only (no test data)', 'info');
                
                log('step4-result', '✅ Provider dashboard integration ready', 'success');
                log('step4-result', '🔄 Refresh provider dashboard to see urgent cases', 'warning');
                
            } catch (error) {
                log('step4-result', `❌ Error: ${error.message}`, 'error');
            }
        }
        
        async function runCompleteTest() {
            log('step5-result', '🚀 Running complete urgent case flow test...', 'info');
            
            try {
                log('step5-result', '1️⃣ Setting up patient session...', 'info');
                await setupPatientSession();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                log('step5-result', '2️⃣ Creating urgent case...', 'warning');
                await createUrgentCase();
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                log('step5-result', '3️⃣ Checking database...', 'info');
                await checkDatabase();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                log('step5-result', '4️⃣ Testing provider dashboard...', 'info');
                await testProviderDashboard();
                
                log('step5-result', '🎉 COMPLETE TEST FINISHED!', 'success');
                log('step5-result', '📋 Next steps:', 'info');
                log('step5-result', '   1. Open provider dashboard: http://localhost:8085', 'info');
                log('step5-result', '   2. Navigate to "Urgent Ubuntu Care" tab', 'info');
                log('step5-result', '   3. Verify urgent case appears with red flag', 'info');
                log('step5-result', '   4. Check AI monitoring is activated', 'info');
                
            } catch (error) {
                log('step5-result', `❌ Complete test failed: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
