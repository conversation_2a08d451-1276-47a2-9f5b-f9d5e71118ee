Stack trace:
Frame         Function      Args
0007FFFF7610  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF6510) msys-2.0.dll+0x2118E
0007FFFF7610  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF7610  0002100469F2 (00021028DF99, 0007FFFF74C8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF7610  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF7610  00021006A545 (0007FFFF7620, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF7620, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF34500000 ntdll.dll
7FFF33810000 KERNEL32.DLL
7FFF31C80000 KERNELBASE.dll
7FFF2E050000 apphelp.dll
7FFF33C00000 USER32.dll
7FFF32070000 win32u.dll
7FFF33730000 GDI32.dll
7FFF31650000 gdi32full.dll
7FFF320A0000 msvcp_win.dll
7FFF32150000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF33B40000 advapi32.dll
7FFF34070000 msvcrt.dll
7FFF322A0000 sechost.dll
7FFF325A0000 RPCRT4.dll
7FFF30C50000 CRYPTBASE.DLL
7FFF31910000 bcryptPrimitives.dll
7FFF34020000 IMM32.DLL
