"""
Patient Service - Business logic for patient management
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from app.database.db_manager import DatabaseManager

class PatientService:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def create_or_update_patient(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create new patient or update existing one
        """
        try:
            # Generate patient ID if not provided
            if not patient_data.get('patient_id'):
                patient_data['patient_id'] = f"PAT_{int(datetime.now().timestamp() * 1000)}_{uuid.uuid4().hex[:5].upper()}"
            
            # Prepare patient data for database
            db_patient_data = {
                'id': patient_data['patient_id'],
                'first_name': patient_data.get('first_name', ''),
                'last_name': patient_data.get('last_name', ''),
                'id_number': patient_data.get('id_number', ''),
                'phone_number': patient_data.get('phone_number', ''),
                'email': patient_data.get('email', ''),
                'date_of_birth': patient_data.get('date_of_birth'),
                'age': patient_data.get('age'),
                'gender': patient_data.get('gender'),
                'address': patient_data.get('address', ''),
                'emergency_contact_name': patient_data.get('emergency_contact_name', ''),
                'emergency_contact_phone': patient_data.get('emergency_contact_phone', ''),
                'emergency_contact_relationship': patient_data.get('emergency_contact_relationship', '')
            }
            
            # Check if patient already exists
            existing_patient = self.db.get_patient_by_id_number(db_patient_data['id_number'])
            
            if existing_patient:
                # Update existing patient
                updates = {k: v for k, v in db_patient_data.items() if k != 'id'}
                success = self.db.update_patient(existing_patient['id'], updates)
                
                if success:
                    return {
                        'success': True,
                        'patient_id': existing_patient['id'],
                        'action': 'updated',
                        'message': 'Patient updated successfully'
                    }
                else:
                    return {
                        'success': False,
                        'error': 'Failed to update existing patient'
                    }
            else:
                # Create new patient
                success = self.db.create_patient(db_patient_data)
                
                if success:
                    # Add medical history if provided
                    medical_history = patient_data.get('medical_history', [])
                    for condition in medical_history:
                        if isinstance(condition, dict) and 'condition_name' in condition:
                            self.db.add_medical_condition(db_patient_data['id'], condition)
                    
                    return {
                        'success': True,
                        'patient_id': db_patient_data['id'],
                        'action': 'created',
                        'message': 'Patient created successfully'
                    }
                else:
                    return {
                        'success': False,
                        'error': 'Failed to create patient'
                    }
        
        except Exception as e:
            return {
                'success': False,
                'error': f'Patient service error: {str(e)}'
            }
    
    def get_patient_by_id_number(self, id_number: str) -> Optional[Dict[str, Any]]:
        """
        Get complete patient data by SA ID number
        """
        try:
            # Get patient from database
            patient_data = self.db.get_patient_by_id_number(id_number)
            
            if patient_data:
                # Get additional data
                medical_history = self.db.get_medical_history(patient_data['id'])
                ai_interactions = self.db.get_ai_interactions_by_patient(patient_data['id'])
                
                # Format response
                return {
                    'patient_id': patient_data['id'],
                    'first_name': patient_data['first_name'],
                    'last_name': patient_data['last_name'],
                    'id_number': patient_data['id_number'],
                    'phone_number': patient_data['phone_number'],
                    'email': patient_data['email'],
                    'date_of_birth': patient_data['date_of_birth'],
                    'age': patient_data['age'],
                    'gender': patient_data['gender'],
                    'address': patient_data['address'],
                    'emergency_contact_name': patient_data['emergency_contact_name'],
                    'emergency_contact_phone': patient_data['emergency_contact_phone'],
                    'emergency_contact_relationship': patient_data['emergency_contact_relationship'],
                    'medical_history': medical_history,
                    'ai_interactions': ai_interactions,
                    'created_at': patient_data['created_at'],
                    'updated_at': patient_data['updated_at'],
                    'found_in_backend': True
                }
            else:
                return None
        
        except Exception as e:
            raise Exception(f'Error getting patient: {str(e)}')
    
    def search_patients(self, search_term: str = "", provider_id: str = "PROV001") -> List[Dict[str, Any]]:
        """
        Search patients with enhanced data
        """
        try:
            # Get patients from database
            patients = self.db.search_patients(search_term, provider_id)
            
            # Format response
            response_data = []
            for patient in patients:
                # Get additional data for each patient
                medical_history = self.db.get_medical_history(patient['id'])
                ai_interactions = self.db.get_ai_interactions_by_patient(patient['id'])
                
                patient_data = {
                    'patient_id': patient['id'],
                    'first_name': patient['first_name'],
                    'last_name': patient['last_name'],
                    'id_number': patient['id_number'],
                    'phone_number': patient['phone_number'],
                    'email': patient['email'],
                    'age': patient['age'],
                    'gender': patient['gender'],
                    'address': patient['address'],
                    'medical_history_count': len(medical_history),
                    'ai_interactions_count': len(ai_interactions),
                    'created_at': patient['created_at'],
                    'updated_at': patient['updated_at']
                }
                response_data.append(patient_data)
            
            return response_data
        
        except Exception as e:
            raise Exception(f'Error searching patients: {str(e)}')
    
    def update_patient(self, patient_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update patient information
        """
        try:
            success = self.db.update_patient(patient_id, updates)
            
            if success:
                return {
                    'success': True,
                    'patient_id': patient_id,
                    'message': 'Patient updated successfully'
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to update patient'
                }
        
        except Exception as e:
            return {
                'success': False,
                'error': f'Update error: {str(e)}'
            }
    
    def delete_patient(self, patient_id: str) -> Dict[str, Any]:
        """
        Soft delete patient (mark as inactive)
        """
        try:
            # Implement soft delete by updating status
            updates = {'status': 'inactive', 'deleted_at': datetime.now().isoformat()}
            success = self.db.update_patient(patient_id, updates)
            
            if success:
                return {
                    'success': True,
                    'patient_id': patient_id,
                    'message': 'Patient deleted successfully'
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to delete patient'
                }
        
        except Exception as e:
            return {
                'success': False,
                'error': f'Delete error: {str(e)}'
            }
