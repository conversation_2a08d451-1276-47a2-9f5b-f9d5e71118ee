// Chat Interaction Service - Connects real chat data to medical records
// This service captures conversations from AI Health Assistant and Chat Monitor
// and stores them as proper medical record interactions

import { healthcareDatabase, ChatInteraction } from './healthcareDatabase';
import { patientRegistrationService } from './patientRegistrationService';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

export interface ChatSession {
  id: string;
  patientId?: string;
  patientIdNumber?: string;
  patientPhone?: string;
  type: 'AI Health Assistant Chat' | 'Chat Monitor' | 'Voice Triage';
  startTime: Date;
  endTime?: Date;
  language: string;
  messages: ChatMessage[];
  status: 'active' | 'completed' | 'escalated';
  summary?: string;
  aiAssessment?: {
    symptoms: string[];
    severity: 'Low' | 'Medium' | 'High' | 'Critical';
    recommendations: string[];
    triageDecision: 'Self-Care' | 'Schedule Appointment' | 'Urgent Care' | 'Emergency';
  };
}

class ChatInteractionService {
  private activeSessions = new Map<string, ChatSession>();
  private completedSessions = new Map<string, ChatSession>();

  constructor() {
    console.log('🔗 ChatInteractionService initialized - Connecting real chat data to medical records');
  }

  /**
   * Start a new chat session
   */
  startChatSession(
    type: 'AI Health Assistant Chat' | 'Chat Monitor' | 'Voice Triage',
    language: string = 'en',
    patientInfo?: {
      patientId?: string;
      idNumber?: string;
      phone?: string;
    }
  ): string {
    const sessionId = `chat_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    const session: ChatSession = {
      id: sessionId,
      patientId: patientInfo?.patientId,
      patientIdNumber: patientInfo?.idNumber,
      patientPhone: patientInfo?.phone,
      type,
      startTime: new Date(),
      language,
      messages: [],
      status: 'active'
    };

    this.activeSessions.set(sessionId, session);
    
    console.log(`💬 Started ${type} session: ${sessionId}`);
    return sessionId;
  }

  /**
   * Add a message to an active chat session
   */
  addMessageToSession(sessionId: string, message: ChatMessage): boolean {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.warn(`Session ${sessionId} not found`);
      return false;
    }

    session.messages.push(message);
    console.log(`📝 Added message to session ${sessionId}: ${message.role} - ${message.content.substring(0, 50)}...`);
    
    return true;
  }

  /**
   * End a chat session and convert it to a medical record interaction
   */
  async endChatSession(sessionId: string, summary?: string): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.warn(`Session ${sessionId} not found`);
      return false;
    }

    session.endTime = new Date();
    session.status = 'completed';
    session.summary = summary || this.generateSummary(session);
    session.aiAssessment = this.generateAIAssessment(session);

    // Move to completed sessions
    this.completedSessions.set(sessionId, session);
    this.activeSessions.delete(sessionId);

    // Find the patient and add this interaction to their medical record
    const patientId = await this.findPatientId(session);
    if (patientId) {
      await this.saveToMedicalRecord(session, patientId);
    } else {
      console.warn(`Could not find patient for session ${sessionId}`);
    }

    console.log(`✅ Ended chat session: ${sessionId}`);
    return true;
  }

  /**
   * Find patient ID from session information
   */
  private async findPatientId(session: ChatSession): Promise<string | null> {
    // Try to find by patient ID first
    if (session.patientId) {
      return session.patientId;
    }

    // Try to find by ID number
    if (session.patientIdNumber) {
      const patientResult = patientRegistrationService.getPatientByIdNumber(session.patientIdNumber);
      if (patientResult) {
        return patientResult.patient.id;
      }
    }

    // Try to find by phone number
    if (session.patientPhone) {
      const allPatients = patientRegistrationService.getAllRegisteredPatients();
      for (const patientResult of allPatients) {
        if (patientResult.patient.personalInfo.phone === session.patientPhone) {
          return patientResult.patient.id;
        }
      }
    }

    return null;
  }

  /**
   * Save chat session as a medical record interaction to database
   */
  private async saveToMedicalRecord(session: ChatSession, patientId: string): Promise<void> {
    try {
      // Prepare interaction data for database
      const interactionData = {
        id: `interaction_${session.id}`,
        patient_id: patientId,
        interaction_type: session.type === 'AI Health Assistant Chat' ? 'chat' :
                         session.type === 'Chat Monitor' ? 'monitoring' : 'assessment',
        summary: session.summary || `${session.type} session completed`,
        full_conversation: JSON.stringify(session.messages),
        ai_assessment: session.aiAssessment ? JSON.stringify(session.aiAssessment) : '',
        severity: session.aiAssessment?.severity || 'Low',
        recommendations: session.aiAssessment?.recommendations ?
                        JSON.stringify(session.aiAssessment.recommendations) : '',
        urgent_care: session.aiAssessment?.triageDecision === 'Emergency' ||
                    session.aiAssessment?.triageDecision === 'Urgent Care'
      };

      // Save to database via API
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';
      const response = await fetch(`${apiBaseUrl}/api/ai-interactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(interactionData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Chat interaction saved to database:', result);
      } else {
        console.error('❌ Failed to save chat interaction to database');
        // Fallback to healthcare database service
        await this.saveToHealthcareDatabase(session, patientId);
      }
    } catch (error) {
      console.error('❌ Error saving to database:', error);
      // Fallback to healthcare database service
      await this.saveToHealthcareDatabase(session, patientId);
    }
  }

  /**
   * Fallback: Save to healthcare database service
   */
  private async saveToHealthcareDatabase(session: ChatSession, patientId: string): Promise<void> {
    const interaction: ChatInteraction = {
      id: `interaction_${session.id}`,
      patientId,
      type: session.type,
      timestamp: session.startTime.toISOString(),
      duration: this.calculateDuration(session),
      language: session.language,
      messages: session.messages.map(msg => ({
        id: msg.id,
        sender: msg.role === 'user' ? 'patient' : 'ai',
        content: msg.content,
        timestamp: msg.timestamp.toISOString(),
        type: 'text'
      })),
      summary: session.summary || '',
      aiAssessment: session.aiAssessment,
      status: session.status
    };

    const success = await healthcareDatabase.addAIInteraction(interaction);

    if (success) {
      console.log(`✅ Chat interaction saved to medical records: ${interaction.id}`);
    } else {
      console.error(`❌ Failed to save chat interaction: ${interaction.id}`);
    }
  }

  /**
   * Generate summary from chat messages
   */
  private generateSummary(session: ChatSession): string {
    const patientMessages = session.messages.filter(msg => msg.role === 'user');
    const aiMessages = session.messages.filter(msg => msg.role === 'assistant');

    if (patientMessages.length === 0) {
      return 'No patient messages recorded.';
    }

    const firstMessage = patientMessages[0].content;
    const lastMessage = patientMessages[patientMessages.length - 1].content;

    return `${session.type} session with ${patientMessages.length} patient messages and ${aiMessages.length} AI responses. ` +
           `Patient initially reported: "${firstMessage.substring(0, 100)}..." ` +
           `Session concluded with: "${lastMessage.substring(0, 100)}..."`;
  }

  /**
   * Generate AI assessment from chat content
   */
  private generateAIAssessment(session: ChatSession): ChatInteraction['aiAssessment'] {
    const patientMessages = session.messages.filter(msg => msg.role === 'user');
    const content = patientMessages.map(msg => msg.content.toLowerCase()).join(' ');

    // Extract symptoms from patient messages
    const symptoms: string[] = [];
    const symptomKeywords = [
      'headache', 'pain', 'fever', 'cough', 'dizzy', 'nausea', 'tired', 'fatigue',
      'chest pain', 'shortness of breath', 'stomach ache', 'sore throat'
    ];

    symptomKeywords.forEach(keyword => {
      if (content.includes(keyword)) {
        symptoms.push(keyword.charAt(0).toUpperCase() + keyword.slice(1));
      }
    });

    // Determine severity based on keywords
    let severity: 'Low' | 'Medium' | 'High' | 'Critical' = 'Low';
    const highSeverityKeywords = ['severe', 'intense', 'unbearable', 'emergency', 'urgent'];
    const mediumSeverityKeywords = ['moderate', 'concerning', 'worried', 'getting worse'];

    if (highSeverityKeywords.some(keyword => content.includes(keyword))) {
      severity = 'High';
    } else if (mediumSeverityKeywords.some(keyword => content.includes(keyword))) {
      severity = 'Medium';
    }

    // Generate recommendations
    const recommendations: string[] = [];
    if (symptoms.length > 0) {
      recommendations.push('Monitor symptoms closely');
      recommendations.push('Stay hydrated and rest');
    }
    if (severity === 'High') {
      recommendations.push('Seek immediate medical attention');
    } else if (severity === 'Medium') {
      recommendations.push('Schedule appointment with healthcare provider');
    }

    // Determine triage decision
    let triageDecision: 'Self-Care' | 'Schedule Appointment' | 'Urgent Care' | 'Emergency' = 'Self-Care';
    if (severity === 'Critical') {
      triageDecision = 'Emergency';
    } else if (severity === 'High') {
      triageDecision = 'Urgent Care';
    } else if (severity === 'Medium') {
      triageDecision = 'Schedule Appointment';
    }

    return {
      symptoms: symptoms.length > 0 ? symptoms : ['General health inquiry'],
      severity,
      recommendations: recommendations.length > 0 ? recommendations : ['Continue monitoring health'],
      triageDecision
    };
  }

  /**
   * Calculate session duration
   */
  private calculateDuration(session: ChatSession): string {
    if (!session.endTime) {
      return 'Ongoing';
    }

    const durationMs = session.endTime.getTime() - session.startTime.getTime();
    const minutes = Math.floor(durationMs / 60000);
    const seconds = Math.floor((durationMs % 60000) / 1000);

    if (minutes > 0) {
      return `${minutes} minutes`;
    } else {
      return `${seconds} seconds`;
    }
  }

  /**
   * Get active sessions
   */
  getActiveSessions(): ChatSession[] {
    return Array.from(this.activeSessions.values());
  }

  /**
   * Get completed sessions
   */
  getCompletedSessions(): ChatSession[] {
    return Array.from(this.completedSessions.values());
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): ChatSession | undefined {
    return this.activeSessions.get(sessionId) || this.completedSessions.get(sessionId);
  }

  /**
   * Get all sessions (active and completed)
   */
  getAllSessions(): ChatSession[] {
    return [...this.getActiveSessions(), ...this.getCompletedSessions()];
  }
}

export const chatInteractionService = new ChatInteractionService();
export default chatInteractionService;

// Make available globally for debugging
(window as any).debugChatInteractions = () => {
  console.log('=== Chat Interaction Service Debug ===');
  console.log('Active sessions:', chatInteractionService.getActiveSessions().length);
  console.log('All sessions:', chatInteractionService.getAllSessions().length);
  console.log('Sessions:', chatInteractionService.getAllSessions());
  console.log('======================================');
};

// Test function to verify AI Assistant Interactions data flow
(window as any).testAIInteractionsFlow = async () => {
  console.log('🧪 Testing AI Assistant Interactions Data Flow...');

  // 1. Simulate patient login
  localStorage.setItem('currentPatientId', 'TEST_PATIENT_001');
  localStorage.setItem('currentPatientIdNumber', '9001015800087');
  localStorage.setItem('currentPatientName', 'Test Patient');
  localStorage.setItem('currentPatientAge', '35');
  localStorage.setItem('currentPatientLocation', 'Cape Town, Western Cape');
  console.log('✅ Step 1: Patient login simulated');

  // 2. Simulate AI Health Assistant Chat session
  const chatSessionId = chatInteractionService.startChatSession(
    'AI Health Assistant Chat',
    'en',
    {
      patientId: 'TEST_PATIENT_001',
      idNumber: '9001015800087'
    }
  );

  // Add some test messages
  chatInteractionService.addMessageToSession(chatSessionId, {
    id: 'test_msg_1',
    role: 'user',
    content: 'I have been experiencing chest pain and shortness of breath',
    timestamp: new Date()
  });

  chatInteractionService.addMessageToSession(chatSessionId, {
    id: 'test_msg_2',
    role: 'assistant',
    content: 'I understand your concern about chest pain and shortness of breath. These symptoms can be serious. Let me ask you some questions to better assess your situation.',
    timestamp: new Date()
  });

  // End session with AI assessment
  const session = chatInteractionService.getSession(chatSessionId);
  if (session) {
    session.aiAssessment = {
      symptoms: ['Chest pain', 'Shortness of breath'],
      severity: 'High',
      recommendations: ['Seek immediate medical attention', 'Monitor symptoms closely', 'Avoid physical exertion'],
      triageDecision: 'Urgent Care'
    };
  }

  await chatInteractionService.endChatSession(chatSessionId, 'AI Health Assistant Chat completed with urgent care recommendation due to chest pain and breathing difficulties.');
  console.log('✅ Step 2: AI Health Assistant Chat session completed');

  // 3. Check if the interaction appears in healthcare database
  const { healthcareDatabase } = await import('./healthcareDatabase');
  const interactions = await healthcareDatabase.getAIInteractionsByPatient('TEST_PATIENT_001');
  console.log('✅ Step 3: AI interactions retrieved from database:', interactions.length);

  if (interactions.length > 0) {
    console.log('🎉 SUCCESS: AI Assistant Interactions data flow is working!');
    console.log('Latest interaction:', interactions[interactions.length - 1]);
  } else {
    console.log('❌ ISSUE: No AI interactions found in database');
  }

  console.log('🧪 Test completed. Check the Medical Records section to see the AI Assistant Interactions.');
};
