"""
Database initialization and management for Ubuntu Health Connect SA
"""

import sqlite3
import os
from flask import current_app, g
from app.database.db_manager import DatabaseManager

def get_db():
    """Get database connection for current request"""
    if 'db' not in g:
        g.db = DatabaseManager()
    return g.db

def close_db(e=None):
    """Close database connection"""
    db = g.pop('db', None)
    if db is not None:
        db.close()

def init_db(app):
    """Initialize database with Flask app"""
    app.teardown_appcontext(close_db)
    
    # Create database tables if they don't exist
    with app.app_context():
        db = DatabaseManager()
        if db.test_connection():
            app.logger.info("✅ Database initialized successfully")
        else:
            app.logger.error("❌ Database initialization failed")

def init_app(app):
    """Initialize database with app factory pattern"""
    init_db(app)
