// Intelligent Monitoring Agent - Automatically monitors urgent patients with AI-powered check-ins
import { patientCaseService, PatientCase, ChatMessage } from './patientCaseService';
import { chatInteractionService } from './chatInteractionService';

export interface MonitoringSession {
  id: string;
  patientId: string;
  patientName: string;
  caseId: string;
  chatSessionId?: string; // Added to track chat interactions
  status: 'active' | 'paused' | 'completed' | 'doctor_intervened';
  startedAt: Date;
  lastCheckIn: Date;
  nextCheckIn: Date;
  checkInCount: number;
  patientResponses: PatientResponse[];
  language: string;
  medicalContext: {
    symptoms: string[];
    severity: string;
    urgentCare: boolean;
    riskFactors: string[];
  };
}

export interface PatientResponse {
  id: string;
  content: string;
  timestamp: Date;
  sentiment: 'stable' | 'concerning' | 'urgent';
  aiAnalysis: string;
  followUpNeeded: boolean;
}

class IntelligentMonitoringAgent {
  private sessions: MonitoringSession[] = [];
  private checkInIntervals: Map<string, NodeJS.Timeout> = new Map();
  private readonly CHECK_IN_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private readonly STORAGE_KEY = 'ubuntu-health-monitoring-sessions';

  constructor() {
    this.loadSessionsFromStorage();
    this.subscribeToPatientCases();
    this.resumeActiveMonitoring();
  }

  // Subscribe to patient case updates to automatically start monitoring
  private subscribeToPatientCases() {
    patientCaseService.subscribe((cases) => {
      const urgentCases = cases.filter(case_ => 
        (case_.reportData.assessmentSummary.urgentCare || 
         case_.reportData.assessmentSummary.severity === 'critical' ||
         case_.reportData.assessmentSummary.severity === 'high') &&
        case_.status === 'new'
      );

      // Start monitoring for new urgent cases
      urgentCases.forEach(urgentCase => {
        if (!this.isPatientBeingMonitored(urgentCase.patientId)) {
          console.log(`🤖 Auto-starting monitoring for urgent case: ${urgentCase.patientName}`);
          this.startMonitoringForCase(urgentCase);
        }
      });

      // Stop monitoring when doctor marks case as in_progress
      cases.forEach(case_ => {
        if (case_.status === 'in_progress') {
          const activeSession = this.sessions.find(s => s.caseId === case_.id && s.status === 'active');
          if (activeSession) {
            console.log(`👨‍⚕️ Doctor intervened for case ${case_.id}, stopping monitoring`);
            this.stopMonitoringWithDoctorMessage(activeSession.id);
          }
        }
      });
    });
  }

  // Start monitoring for a specific patient case
  startMonitoringForCase(patientCase: PatientCase): string {
    const sessionId = this.generateSessionId();

    // Create a chat session for this monitoring session
    const chatSessionId = chatInteractionService.startChatSession(
      'Chat Monitor',
      patientCase.language,
      {
        patientId: patientCase.patientId,
        idNumber: patientCase.patientId, // Using patientId as ID number for now
      }
    );

    const session: MonitoringSession = {
      id: sessionId,
      patientId: patientCase.patientId,
      patientName: patientCase.patientName,
      caseId: patientCase.id,
      chatSessionId, // Store the chat session ID
      status: 'active',
      startedAt: new Date(),
      lastCheckIn: new Date(),
      nextCheckIn: new Date(Date.now() + this.CHECK_IN_INTERVAL),
      checkInCount: 0,
      patientResponses: [],
      language: patientCase.language,
      medicalContext: {
        symptoms: patientCase.reportData.symptomsReported.primary,
        severity: patientCase.reportData.assessmentSummary.severity,
        urgentCare: patientCase.reportData.assessmentSummary.urgentCare,
        riskFactors: patientCase.reportData.riskFactors
      }
    };

    this.sessions.push(session);
    this.startCheckInTimer(sessionId);
    this.saveSessionsToStorage();

    console.log(`🤖 Started monitoring session for ${patientCase.patientName} (${sessionId}) with chat session (${chatSessionId})`);
    return sessionId;
  }

  // Start the check-in timer for a session
  private startCheckInTimer(sessionId: string) {
    // Clear existing timer if any
    const existingTimer = this.checkInIntervals.get(sessionId);
    if (existingTimer) {
      clearInterval(existingTimer);
    }

    const timer = setInterval(() => {
      this.performCheckIn(sessionId);
    }, this.CHECK_IN_INTERVAL);

    this.checkInIntervals.set(sessionId, timer);
    
    // Perform first check-in immediately
    setTimeout(() => this.performCheckIn(sessionId), 1000);
  }

  // Perform a check-in with the patient
  private async performCheckIn(sessionId: string) {
    const session = this.sessions.find(s => s.id === sessionId);
    if (!session || session.status !== 'active') {
      this.clearCheckInTimer(sessionId);
      return;
    }

    // Check if doctor has intervened
    const patientCase = patientCaseService.getCaseById(session.caseId);
    if (patientCase?.status === 'in_progress') {
      this.stopMonitoringWithDoctorMessage(sessionId);
      return;
    }

    session.checkInCount++;
    session.lastCheckIn = new Date();
    session.nextCheckIn = new Date(Date.now() + this.CHECK_IN_INTERVAL);

    const checkInMessage = this.generateCheckInMessage(session);

    // Add message to patient case conversation
    const aiMessage: ChatMessage = {
      id: `monitoring_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      role: 'assistant',
      content: checkInMessage,
      timestamp: new Date()
    };

    patientCaseService.addMessageToCase(session.caseId, aiMessage);

    // Also add to chat session for medical records
    if (session.chatSessionId) {
      chatInteractionService.addMessageToSession(session.chatSessionId, {
        id: aiMessage.id,
        role: 'assistant',
        content: checkInMessage,
        timestamp: new Date()
      });
    }

    console.log(`💬 Check-in #${session.checkInCount} sent to ${session.patientName}: ${checkInMessage}`);
    this.saveSessionsToStorage();
  }

  // Generate contextual check-in message based on patient's language and medical context
  private generateCheckInMessage(session: MonitoringSession): string {
    const messages = {
      en: [
        `Hello ${session.patientName}! This is Ubuntu Health AI checking on you. How are you feeling right now? Please describe any changes in your symptoms.`,
        `Hi there! It's been ${session.checkInCount * 5} minutes since our last check. How is your ${session.medicalContext.symptoms.join(' and ')} now?`,
        `Ubuntu Health AI here 🤖 How are you doing? Any improvement or worsening of your symptoms?`,
        `Checking in on you, ${session.patientName}. Please let me know how you're feeling and if anything has changed.`,
        `Hello! This is your Ubuntu Health assistant. How are your symptoms now? Any new concerns?`
      ],
      zu: [
        `Sawubona ${session.patientName}! Lena yi-Ubuntu Health AI ekubona ukuthi unjani. Uzizwa kanjani manje? Sicela uchaze noma yikuphi ukuguquka ezimpawini zakho.`,
        `Sawubona! Sekudlule imizuzu engu-${session.checkInCount * 5} selokhu sikhuluma. Kunjani manje nge${session.medicalContext.symptoms.join(' ne')}?`,
        `Ubuntu Health AI lapha 🤖 Unjani? Kukhona ukuthuthuka noma ukuba kubi kwezimpawini zakho?`,
        `Ngiyakubona, ${session.patientName}. Sicela ungazise ukuthi uzizwa kanjani futhi ukuthi kukhona okushintshile.`
      ],
      af: [
        `Hallo ${session.patientName}! Dit is Ubuntu Health AI wat jou kom sien. Hoe voel jy nou? Beskryf asseblief enige veranderinge in jou simptome.`,
        `Hallo daar! Dit is nou ${session.checkInCount * 5} minute sedert ons laaste gesprek. Hoe gaan dit nou met jou ${session.medicalContext.symptoms.join(' en ')}?`,
        `Ubuntu Health AI hier 🤖 Hoe gaan dit? Enige verbetering of verswakking van jou simptome?`
      ],
      st: [
        `Dumela ${session.patientName}! Ke Ubuntu Health AI e o bonang. O ikutlwa jwang hona jwale? Ka kopo hlalosa diphetoho tse itseng matshwenyehong a hao.`,
        `Dumela! Ho fetile metsotso e ${session.checkInCount * 5} ho tloha puong ya rona ya ho qetela. Ho jwang hona jwale ka ${session.medicalContext.symptoms.join(' le ')}?`
      ]
    };

    const langMessages = messages[session.language as keyof typeof messages] || messages.en;
    const messageIndex = (session.checkInCount - 1) % langMessages.length;
    return langMessages[messageIndex];
  }

  // Add patient response and analyze it
  addPatientResponse(sessionId: string, responseContent: string): PatientResponse {
    const session = this.sessions.find(s => s.id === sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    const response: PatientResponse = {
      id: `response_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      content: responseContent,
      timestamp: new Date(),
      sentiment: this.analyzeSentiment(responseContent),
      aiAnalysis: this.generateAIAnalysis(responseContent, session.medicalContext),
      followUpNeeded: this.determineFollowUpNeed(responseContent)
    };

    session.patientResponses.push(response);

    // Add patient response to case conversation
    const patientMessage: ChatMessage = {
      id: `patient_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      role: 'user',
      content: responseContent,
      timestamp: new Date()
    };

    patientCaseService.addMessageToCase(session.caseId, patientMessage);

    // Also add to chat session for medical records
    if (session.chatSessionId) {
      chatInteractionService.addMessageToSession(session.chatSessionId, {
        id: patientMessage.id,
        role: 'user',
        content: responseContent,
        timestamp: new Date()
      });
    }

    // Generate AI follow-up response
    const followUpMessage = this.generateFollowUpMessage(response, session);
    if (followUpMessage) {
      const aiFollowUp: ChatMessage = {
        id: `followup_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        role: 'assistant',
        content: followUpMessage,
        timestamp: new Date()
      };
      patientCaseService.addMessageToCase(session.caseId, aiFollowUp);

      // Also add AI follow-up to chat session
      if (session.chatSessionId) {
        chatInteractionService.addMessageToSession(session.chatSessionId, {
          id: aiFollowUp.id,
          role: 'assistant',
          content: followUpMessage,
          timestamp: new Date()
        });
      }
    }

    this.saveSessionsToStorage();
    console.log(`📝 Patient response analyzed: ${response.sentiment} - ${response.aiAnalysis}`);
    
    return response;
  }

  // Analyze patient response sentiment
  private analyzeSentiment(content: string): 'stable' | 'concerning' | 'urgent' {
    const urgentKeywords = ['worse', 'terrible', 'unbearable', 'emergency', 'help', 'dying', 'can\'t breathe', 'chest pain', 'severe'];
    const concerningKeywords = ['worried', 'pain', 'uncomfortable', 'dizzy', 'nauseous', 'tired', 'weak'];
    const stableKeywords = ['better', 'good', 'fine', 'okay', 'stable', 'improving', 'manageable'];

    const lowerContent = content.toLowerCase();
    
    if (urgentKeywords.some(keyword => lowerContent.includes(keyword))) {
      return 'urgent';
    }
    if (concerningKeywords.some(keyword => lowerContent.includes(keyword))) {
      return 'concerning';
    }
    if (stableKeywords.some(keyword => lowerContent.includes(keyword))) {
      return 'stable';
    }
    
    return 'concerning'; // Default to concerning if unclear
  }

  // Generate AI analysis of patient response
  private generateAIAnalysis(content: string, medicalContext: MonitoringSession['medicalContext']): string {
    const sentiment = this.analyzeSentiment(content);
    
    switch (sentiment) {
      case 'urgent':
        return `Patient reports worsening symptoms. Immediate medical attention may be required. Symptoms align with ${medicalContext.severity} severity case.`;
      case 'concerning':
        return `Patient shows some concerning signs. Continued monitoring recommended. Symptoms should be evaluated in context of ${medicalContext.symptoms.join(', ')}.`;
      case 'stable':
        return `Patient appears stable with current symptoms. Continue monitoring as planned. Positive response to current care approach.`;
      default:
        return 'Patient response requires further evaluation.';
    }
  }

  // Determine if follow-up is needed
  private determineFollowUpNeed(content: string): boolean {
    const urgentKeywords = ['worse', 'terrible', 'emergency', 'help', 'severe', 'can\'t'];
    return urgentKeywords.some(keyword => content.toLowerCase().includes(keyword));
  }

  // Generate follow-up message based on patient response
  private generateFollowUpMessage(response: PatientResponse, session: MonitoringSession): string | null {
    switch (response.sentiment) {
      case 'urgent':
        return `I'm concerned about your symptoms getting worse. Please consider seeking immediate medical attention or calling emergency services. Ubuntu - we care about your wellbeing! 🚨❤️`;
      case 'concerning':
        return `Thank you for the update. I'll continue monitoring you closely. If symptoms worsen, please don't hesitate to seek medical help. Ubuntu - we are here for you! 💚`;
      case 'stable':
        return `I'm glad to hear you're feeling better! I'll keep checking on you. Ubuntu - together we heal! 🌟💚`;
      default:
        return null;
    }
  }

  // Stop monitoring with doctor intervention message
  stopMonitoringWithDoctorMessage(sessionId: string) {
    const session = this.sessions.find(s => s.id === sessionId);
    if (!session) return;

    session.status = 'doctor_intervened';
    this.clearCheckInTimer(sessionId);

    const finalMessages = {
      en: `Hello ${session.patientName}! I can see that a doctor is now treating you. I hope you get well soon and know that we love you and wish you all the best! Ubuntu - I am because we are! 💚🤝 I will stop sending messages now, but I'll be here if you need urgent care again in the future.`,
      zu: `Sawubona ${session.patientName}! Ngiyabona ukuthi udokotela manje uyakwelapha. Ngithemba uzoba ngcono futhi yazi ukuthi siyakuthanda futhi sikufisela konke okuhle! Ubuntu - ngikhona ngoba sikhona! 💚🤝 Ngizoyeka ukuthumela imiyalezo manje, kodwa ngizoba lapha uma udinga ukunakekelwa okuphuthumayo futhi esikhathini esizayo.`,
      af: `Hallo ${session.patientName}! Ek kan sien dat 'n dokter jou nou behandel. Ek hoop jy word gou gesond en weet dat ons jou liefhet en jou al die beste toewens! Ubuntu - ek is omdat ons is! 💚🤝 Ek sal nou ophou boodskappe stuur, maar ek sal hier wees as jy weer dringende sorg nodig het in die toekoms.`,
      st: `Dumela ${session.patientName}! Ke bona hore ngaka o o alafang hona jwale. Ke tshepisa o tla phola kapele mme o tsebe hore re o rata mme re o lakatsa tse molemo tsohle! Ubuntu - ke teng hobane re teng! 💚🤝 Ke tla emisa ho romela melaetsa hona jwale, empa ke tla ba mona haeba o hloka tlhokomelo e potlakileng hape nakong e tlang.`
    };

    const finalMessage = finalMessages[session.language as keyof typeof finalMessages] || finalMessages.en;

    const aiMessage: ChatMessage = {
      id: `final_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      role: 'assistant',
      content: finalMessage,
      timestamp: new Date()
    };

    patientCaseService.addMessageToCase(session.caseId, aiMessage);

    // Add final message to chat session and end it with comprehensive medical record
    if (session.chatSessionId) {
      chatInteractionService.addMessageToSession(session.chatSessionId, {
        id: aiMessage.id,
        role: 'assistant',
        content: finalMessage,
        timestamp: new Date()
      });

      // Calculate monitoring duration and create comprehensive summary
      const monitoringDuration = new Date().getTime() - session.startedAt.getTime();
      const durationMinutes = Math.round(monitoringDuration / (1000 * 60));

      const summary = `Ubuntu Health AI Monitoring completed - Doctor intervention. Patient: ${session.patientName}. Duration: ${durationMinutes} minutes. Total check-ins sent: ${session.checkInCount}. Patient responses received: ${session.patientResponses.length}. Medical context: ${session.medicalContext.symptoms.join(', ')} (${session.medicalContext.severity} severity). Successfully transferred to healthcare provider care.`;

      // Create comprehensive AI assessment for medical records
      const responsivenessSummary = session.patientResponses.length > 0
        ? `Patient was responsive (${session.patientResponses.length} responses). Sentiment analysis: ${session.patientResponses.filter(r => r.sentiment === 'stable').length} stable, ${session.patientResponses.filter(r => r.sentiment === 'concerning').length} concerning, ${session.patientResponses.filter(r => r.sentiment === 'urgent').length} urgent responses.`
        : 'Patient did not respond to monitoring check-ins.';

      const aiAssessment = {
        symptoms: session.medicalContext.symptoms,
        severity: session.medicalContext.severity === 'critical' ? 'Critical' as const :
                 session.medicalContext.severity === 'high' ? 'High' as const :
                 session.medicalContext.severity === 'moderate' ? 'Medium' as const : 'Low' as const,
        recommendations: [
          `AI monitoring completed successfully over ${durationMinutes} minutes`,
          `${session.checkInCount} automated check-ins performed`,
          responsivenessSummary,
          'Case successfully escalated to healthcare provider',
          session.medicalContext.urgentCare ? 'Urgent care protocol followed' : 'Standard monitoring protocol followed'
        ],
        triageDecision: 'Urgent Care' as const
      };

      // Update the session with AI assessment before ending
      const chatSession = chatInteractionService.getSession(session.chatSessionId);
      if (chatSession) {
        chatSession.aiAssessment = aiAssessment;
      }

      chatInteractionService.endChatSession(session.chatSessionId, summary);
      console.log(`✅ Chat session ended with comprehensive medical record for doctor handover`);
    }

    this.saveSessionsToStorage();

    console.log(`👨‍⚕️ Monitoring stopped for ${session.patientName} - Doctor intervention`);
  }

  // Utility methods
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private clearCheckInTimer(sessionId: string) {
    const timer = this.checkInIntervals.get(sessionId);
    if (timer) {
      clearInterval(timer);
      this.checkInIntervals.delete(sessionId);
    }
  }

  private saveSessionsToStorage() {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.sessions));
    } catch (error) {
      console.error('Error saving monitoring sessions:', error);
    }
  }

  private loadSessionsFromStorage() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsedSessions = JSON.parse(stored);
        this.sessions = parsedSessions.map((session: any) => ({
          ...session,
          startedAt: new Date(session.startedAt),
          lastCheckIn: new Date(session.lastCheckIn),
          nextCheckIn: new Date(session.nextCheckIn),
          patientResponses: session.patientResponses.map((response: any) => ({
            ...response,
            timestamp: new Date(response.timestamp)
          }))
        }));
      }
    } catch (error) {
      console.error('Error loading monitoring sessions:', error);
      this.sessions = [];
    }
  }

  private resumeActiveMonitoring() {
    this.sessions
      .filter(session => session.status === 'active')
      .forEach(session => {
        console.log(`🔄 Resuming monitoring for ${session.patientName}`);
        this.startCheckInTimer(session.id);
      });
  }

  // Public methods for external access
  getActiveSessions(): MonitoringSession[] {
    return this.sessions.filter(s => s.status === 'active');
  }

  getAllSessions(): MonitoringSession[] {
    return [...this.sessions];
  }

  getSessionById(sessionId: string): MonitoringSession | undefined {
    return this.sessions.find(s => s.id === sessionId);
  }

  isPatientBeingMonitored(patientId: string): boolean {
    return this.sessions.some(s => s.patientId === patientId && s.status === 'active');
  }

  stopMonitoringForCase(caseId: string, reason: string = 'manual') {
    const session = this.sessions.find(s => s.caseId === caseId && s.status === 'active');
    if (session) {
      if (reason === 'doctor_intervened') {
        this.stopMonitoringWithDoctorMessage(session.id);
      } else {
        session.status = 'completed';
        this.clearCheckInTimer(session.id);
        this.saveSessionsToStorage();
      }
    }
  }

  // Generate monitoring report for healthcare providers
  generateMonitoringReport(sessionId: string): string {
    const session = this.sessions.find(s => s.id === sessionId);
    if (!session) return '';

    const responsesSummary = session.patientResponses
      .map(r => `${r.timestamp.toLocaleString()}: ${r.sentiment.toUpperCase()} - ${r.content}`)
      .join('\n');

    return `
UBUNTU HEALTH AI MONITORING REPORT
==================================
Session ID: ${session.id}
Patient: ${session.patientName} (${session.patientId})
Case ID: ${session.caseId}
Language: ${session.language}
Status: ${session.status}
Started: ${session.startedAt.toLocaleString()}
Duration: ${Math.round((new Date().getTime() - session.startedAt.getTime()) / (1000 * 60))} minutes
Check-ins Performed: ${session.checkInCount}
Patient Responses: ${session.patientResponses.length}

MEDICAL CONTEXT
===============
Symptoms: ${session.medicalContext.symptoms.join(', ')}
Severity: ${session.medicalContext.severity}
Urgent Care: ${session.medicalContext.urgentCare ? 'Yes' : 'No'}
Risk Factors: ${session.medicalContext.riskFactors.join(', ')}

PATIENT RESPONSES SUMMARY
========================
${responsesSummary || 'No responses recorded'}

SENTIMENT ANALYSIS
==================
Stable responses: ${session.patientResponses.filter(r => r.sentiment === 'stable').length}
Concerning responses: ${session.patientResponses.filter(r => r.sentiment === 'concerning').length}
Urgent responses: ${session.patientResponses.filter(r => r.sentiment === 'urgent').length}

Generated: ${new Date().toLocaleString()}
Ubuntu Health AI - Caring for South Africa 🇿🇦💚
    `.trim();
  }
}

// Create singleton instance
export const intelligentMonitoringAgent = new IntelligentMonitoringAgent();

// Make available globally for debugging
(window as any).debugMonitoringAgent = () => {
  console.log('=== Intelligent Monitoring Agent Debug ===');
  console.log('Active sessions:', intelligentMonitoringAgent.getActiveSessions().length);
  console.log('Total sessions:', intelligentMonitoringAgent.getAllSessions().length);
  console.log('Sessions:', intelligentMonitoringAgent.getAllSessions());
  console.log('==========================================');
};
