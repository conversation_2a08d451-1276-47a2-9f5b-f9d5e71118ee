#!/usr/bin/env python3
"""
Voice Call Simulation Testing Script
Tests the voice call workflow and callback handling
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# Test configuration
BASE_URL = 'http://localhost:5000'
TEST_PHONE_NUMBERS = [
    '+27727803582',  # Your actual number
    '+27987654321',
    '+27111222333',
    '+27444555666'
]

def test_voice_call_simulation(phone_number: str) -> Dict[str, Any]:
    """Test voice call simulation"""
    try:
        payload = {
            'phone_number': phone_number
        }
        
        print(f"📞 Testing call to: {phone_number}")
        
        response = requests.post(
            f"{BASE_URL}/api/voice-triage/mock-call",
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            call_id = data.get('call_id', 'unknown')
            
            print(f"   {'✅' if success else '❌'} Call {'successful' if success else 'failed'}")
            print(f"   Call ID: {call_id}")
            print(f"   Status: {data.get('status', 'unknown')}")
            
            return {
                'phone_number': phone_number,
                'success': success,
                'call_id': call_id,
                'response_time': response.elapsed.total_seconds(),
                'data': data
            }
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            return {
                'phone_number': phone_number,
                'success': False,
                'error': f"HTTP {response.status_code}",
                'response_time': response.elapsed.total_seconds()
            }
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return {
            'phone_number': phone_number,
            'success': False,
            'error': str(e),
            'response_time': 0
        }

def test_voice_callback_simulation():
    """Test voice callback handling"""
    try:
        # Simulate incoming call data
        callback_data = {
            'callerNumber': '+27727803582',  # Your actual number
            'sessionId': f'test_session_{int(time.time())}',
            'callDirection': 'inbound'
        }
        
        print(f"📞 Testing voice callback simulation")
        print(f"   Caller: {callback_data['callerNumber']}")
        print(f"   Session: {callback_data['sessionId']}")
        
        response = requests.post(
            f"{BASE_URL}/api/voice-triage/callback",
            json=callback_data,
            timeout=10
        )
        
        if response.status_code == 200:
            # Check if response is XML (expected for voice callbacks)
            content_type = response.headers.get('content-type', '')
            is_xml = 'xml' in content_type.lower()
            
            print(f"   ✅ Callback successful")
            print(f"   Content-Type: {content_type}")
            print(f"   Response format: {'XML' if is_xml else 'Other'}")
            
            if is_xml:
                print(f"   XML Response length: {len(response.text)} characters")
            else:
                print(f"   Response: {response.text[:100]}...")
            
            return {
                'success': True,
                'content_type': content_type,
                'is_xml': is_xml,
                'response_length': len(response.text),
                'response_time': response.elapsed.total_seconds()
            }
        else:
            print(f"   ❌ Callback failed: HTTP {response.status_code}")
            return {
                'success': False,
                'error': f"HTTP {response.status_code}",
                'response_time': response.elapsed.total_seconds()
            }
            
    except Exception as e:
        print(f"   ❌ Callback error: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'response_time': 0
        }

def test_recording_simulation():
    """Test recording processing simulation"""
    try:
        # Simulate recording data
        recording_data = {
            'sessionId': f'test_session_{int(time.time())}',
            'recordingUrl': 'https://example.com/test-recording.wav',
            'duration': 30
        }
        
        print(f"🎙️ Testing recording processing")
        print(f"   Session: {recording_data['sessionId']}")
        print(f"   Recording URL: {recording_data['recordingUrl']}")
        
        response = requests.post(
            f"{BASE_URL}/api/voice-triage/recording",
            json=recording_data,
            timeout=10
        )
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            is_xml = 'xml' in content_type.lower()
            
            print(f"   ✅ Recording processing successful")
            print(f"   Content-Type: {content_type}")
            print(f"   Response format: {'XML' if is_xml else 'Other'}")
            
            return {
                'success': True,
                'content_type': content_type,
                'is_xml': is_xml,
                'response_time': response.elapsed.total_seconds()
            }
        else:
            print(f"   ❌ Recording processing failed: HTTP {response.status_code}")
            return {
                'success': False,
                'error': f"HTTP {response.status_code}",
                'response_time': response.elapsed.total_seconds()
            }
            
    except Exception as e:
        print(f"   ❌ Recording error: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'response_time': 0
        }

def test_provider_response_simulation():
    """Test provider DTMF response simulation"""
    try:
        # Test different DTMF responses
        dtmf_scenarios = [
            {'digits': '1', 'description': 'Acknowledge case'},
            {'digits': '2', 'description': 'Escalate case'},
            {'digits': '9', 'description': 'Invalid response'}
        ]
        
        results = []
        
        for scenario in dtmf_scenarios:
            response_data = {
                'sessionId': f'test_session_{int(time.time())}',
                'dtmfDigits': scenario['digits']
            }
            
            print(f"📱 Testing DTMF response: {scenario['digits']} ({scenario['description']})")
            
            response = requests.post(
                f"{BASE_URL}/api/voice-triage/provider-response",
                json=response_data,
                timeout=10
            )
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                is_xml = 'xml' in content_type.lower()
                
                print(f"   ✅ DTMF response successful")
                print(f"   Response format: {'XML' if is_xml else 'Other'}")
                
                results.append({
                    'digits': scenario['digits'],
                    'success': True,
                    'is_xml': is_xml,
                    'response_time': response.elapsed.total_seconds()
                })
            else:
                print(f"   ❌ DTMF response failed: HTTP {response.status_code}")
                results.append({
                    'digits': scenario['digits'],
                    'success': False,
                    'error': f"HTTP {response.status_code}",
                    'response_time': response.elapsed.total_seconds()
                })
            
            time.sleep(0.5)  # Small delay between tests
        
        return results
        
    except Exception as e:
        print(f"   ❌ DTMF error: {str(e)}")
        return [{'success': False, 'error': str(e)}]

def run_voice_call_tests():
    """Run comprehensive voice call tests"""
    print("📞 Voice Call Simulation Testing Suite")
    print("🌍 Ubuntu Philosophy: 'I am because we are'")
    print("=" * 70)
    print(f"📅 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔗 Testing against: {BASE_URL}")
    print("=" * 70)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Voice Triage server is running")
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        print("💡 Make sure the voice triage server is running")
        return
    
    all_results = {}
    
    # Test 1: Voice Call Simulation
    print(f"\n📞 Testing Voice Call Simulation")
    print("-" * 40)
    
    call_results = []
    for phone in TEST_PHONE_NUMBERS:
        result = test_voice_call_simulation(phone)
        call_results.append(result)
        time.sleep(0.5)
    
    all_results['voice_calls'] = call_results
    
    # Test 2: Voice Callback Handling
    print(f"\n📞 Testing Voice Callback Handling")
    print("-" * 40)
    
    callback_result = test_voice_callback_simulation()
    all_results['callback'] = callback_result
    
    # Test 3: Recording Processing
    print(f"\n🎙️ Testing Recording Processing")
    print("-" * 40)
    
    recording_result = test_recording_simulation()
    all_results['recording'] = recording_result
    
    # Test 4: Provider DTMF Response
    print(f"\n📱 Testing Provider DTMF Response")
    print("-" * 40)
    
    dtmf_results = test_provider_response_simulation()
    all_results['dtmf_responses'] = dtmf_results
    
    # Calculate statistics
    call_success_rate = sum(1 for r in call_results if r.get('success', False)) / len(call_results) * 100
    callback_success = callback_result.get('success', False)
    recording_success = recording_result.get('success', False)
    dtmf_success_rate = sum(1 for r in dtmf_results if r.get('success', False)) / len(dtmf_results) * 100
    
    # Print summary
    print("\n" + "=" * 70)
    print("📊 VOICE CALL TEST RESULTS")
    print("=" * 70)
    
    print(f"📞 Voice Call Simulation:")
    print(f"   Success Rate: {call_success_rate:.1f}% ({sum(1 for r in call_results if r.get('success', False))}/{len(call_results)})")
    
    print(f"\n📞 Voice Callback Handling:")
    print(f"   Status: {'✅ Success' if callback_success else '❌ Failed'}")
    
    print(f"\n🎙️ Recording Processing:")
    print(f"   Status: {'✅ Success' if recording_success else '❌ Failed'}")
    
    print(f"\n📱 Provider DTMF Response:")
    print(f"   Success Rate: {dtmf_success_rate:.1f}% ({sum(1 for r in dtmf_results if r.get('success', False))}/{len(dtmf_results)})")
    
    # Overall assessment
    overall_success = (call_success_rate + (100 if callback_success else 0) + 
                      (100 if recording_success else 0) + dtmf_success_rate) / 4
    
    print(f"\n🎯 Overall Voice System Performance: {overall_success:.1f}%")
    
    print("\n" + "=" * 70)
    
    if overall_success >= 80:
        print("🎉 Voice Call System is working excellently!")
    elif overall_success >= 60:
        print("✅ Voice Call System is working well!")
    else:
        print("⚠️ Voice Call System needs attention")
    
    print("=" * 70)
    
    return all_results

if __name__ == '__main__':
    try:
        results = run_voice_call_tests()
        
        # Save results to file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"voice_call_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'results': results
            }, f, indent=2)
        
        print(f"📄 Detailed results saved to: {filename}")
        
    except KeyboardInterrupt:
        print("\n🛑 Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Testing failed: {e}")
