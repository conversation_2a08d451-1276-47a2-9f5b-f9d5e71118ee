
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Users, Calendar, Bell, Search, User, Clock, MapPin, Phone, Activity, TrendingUp, AlertTriangle, Stethoscope, MessageCircle, Bot, Filter } from 'lucide-react';
import { PatientDetailModal } from '@/components/PatientDetailModal';
import { MonitoringDashboard } from '@/components/MonitoringDashboard';

import { AnimatedBackground, FloatingElements, PremiumGlassContainer } from '@/components/ui/animated-background';
import { CircularProgress } from '@/components/ui/circular-progress';
import WhatsAppCommunicationBoard from '@/components/WhatsAppCommunicationBoard';
import { patientCaseService, type PatientCase } from '@/services/patientCaseService';
import { healthcareMonitoringService } from '@/services/healthcareMonitoringService';


// Ubuntu Tab Styles
const ubuntuTabStyles = `
  .ubuntu-tabs [data-state="active"] {
    background: linear-gradient(135deg, #228B22, #FFD700) !important;
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.9) !important;
    border: 2px solid #FFD700 !important;
    box-shadow: 0 4px 15px rgba(255,215,0,0.4) !important;
    transform: scale(1.05) !important;
  }

  .ubuntu-tabs [data-state="active"]:hover {
    background: linear-gradient(135deg, #32CD32, #FFD700) !important;
    box-shadow: 0 6px 20px rgba(255,215,0,0.6) !important;
  }

  .ubuntu-tabs button {
    color: white !important;
    font-weight: bold !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;
  }

  .ubuntu-tabs button:hover {
    background: rgba(255,215,0,0.2) !important;
    color: #FFD700 !important;
    transform: scale(1.02) !important;
  }

  /* Custom scrollbar for urgent cases */
  .urgent-cases-scroll::-webkit-scrollbar {
    width: 8px;
  }

  .urgent-cases-scroll::-webkit-scrollbar-track {
    background: rgba(254, 202, 202, 0.3);
    border-radius: 10px;
  }

  .urgent-cases-scroll::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #ef4444, #f87171);
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  .urgent-cases-scroll::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #dc2626, #ef4444);
  }
`;

interface ProviderDashboardProps {
  onNavigateToStatistics?: () => void;
  onLogout?: () => void;
}

const ProviderDashboard = ({ onNavigateToStatistics, onLogout }: ProviderDashboardProps = {}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [isVisible, setIsVisible] = useState(false);
  const [dynamicUrgentCases, setDynamicUrgentCases] = useState<any[]>([]);
  const [allCases, setAllCases] = useState<PatientCase[]>([]);
  const [newCaseAlert, setNewCaseAlert] = useState(false);
  const [statusFilter, setStatusFilter] = useState<'all' | 'new' | 'reviewed' | 'in_progress'>('all');
  const [showMonitoringDashboard, setShowMonitoringDashboard] = useState(false);


  useEffect(() => {
    setIsVisible(true);

    // Subscribe to patient case updates
    const unsubscribe = patientCaseService.subscribe((cases) => {
      console.log('Provider Dashboard received case update:', cases.length, 'total cases');

      setAllCases(cases);

      // Convert urgent cases to provider dashboard format
      const urgent = patientCaseService.getUrgentCases().map(case_ =>
        patientCaseService.convertToProviderFormat(case_)
      );
      console.log('Provider Dashboard urgent cases:', urgent.length, urgent);

      // Update urgent cases state
      setDynamicUrgentCases(prevUrgent => {
        const previousUrgentCount = prevUrgent.length;

        // Show alert for new urgent cases and start monitoring
        if (urgent.length > previousUrgentCount && urgent.length > 0) {
          setNewCaseAlert(true);
          setTimeout(() => setNewCaseAlert(false), 5000); // Hide after 5 seconds

          // Start monitoring for new urgent cases that aren't already being monitored
          urgent.forEach(urgentCase => {
            if (urgentCase.status === 'new' && !healthcareMonitoringService.isPatientBeingMonitored(urgentCase.id)) {
              console.log(`🤖 Starting monitoring for new urgent case: ${urgentCase.name} (${urgentCase.id})`);
              healthcareMonitoringService.startMonitoring(urgentCase.id, urgentCase.caseId);
            }
          });
        }

        return urgent;
      });
    });

    // Load initial cases
    const initialCases = patientCaseService.getAllCases();
    setAllCases(initialCases);
    const initialUrgent = patientCaseService.getUrgentCases().map(case_ =>
      patientCaseService.convertToProviderFormat(case_)
    );
    setDynamicUrgentCases(initialUrgent);

    console.log('Provider Dashboard initialized with', initialCases.length, 'cases');

    return unsubscribe;
  }, []);

  // Only use real urgent cases from patient portal
  const urgentCases = dynamicUrgentCases;

  const recentConsultations = [
    {
      id: '7808201234567',
      name: 'Nomsa Mthembu',
      age: 46,
      location: 'Khayelitsha, Western Cape Province',
      symptoms: 'Headache, fatigue - "Ikhanda liyabuhlungu"',
      severity: 'mild',
      timeReported: '3 hours ago',
      language: 'isiXhosa',
      status: 'prescription_sent',
      followUp: 'scheduled'
    },
    {
      id: '8905156789012',
      name: 'Sipho Sithole',
      age: 35,
      location: 'Alexandra Township, Gauteng Province',
      symptoms: 'Cough, fever - Traditional remedies tried first',
      severity: 'moderate',
      timeReported: '5 hours ago',
      language: 'isiZulu/English',
      status: 'appointment_booked',
      followUp: 'pending'
    },
    {
      id: '9104157890123',
      name: 'Palesa Mokoena',
      age: 33,
      location: 'Bloemfontein, Free State Province',
      symptoms: 'Diabetes follow-up, blood sugar monitoring',
      severity: 'mild',
      timeReported: '4 hours ago',
      language: 'Sesotho',
      status: 'prescription_sent',
      followUp: 'scheduled'
    }
  ];

  const nonResponsiveCases = [
    {
      id: '6712091234567',
      name: 'Gogo Elizabeth Mahlangu',
      age: 67,
      location: 'Rustenburg, North West Province',
      lastContact: '3 days ago',
      condition: 'Diabetes follow-up - Traditional healer consultation',
      language: 'Sepedi/Setswana',
      homeVisitAssigned: true
    },
    {
      id: '5509142345678',
      name: 'Mandla Dlamini',
      age: 69,
      location: 'Pietermaritzburg, KwaZulu-Natal Province',
      lastContact: '5 days ago',
      condition: 'Hypertension monitoring - Rural area',
      language: 'isiZulu',
      homeVisitAssigned: false
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-600';
      case 'severe': return 'bg-orange-500';
      case 'moderate': return 'bg-yellow-500';
      case 'mild': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'prescription_sent': return 'bg-blue-500';
      case 'appointment_booked': return 'bg-green-500';
      case 'pending': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const getCaseStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-red-500';
      case 'reviewed': return 'bg-blue-500';
      case 'in_progress': return 'bg-yellow-500';
      case 'resolved': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getCaseStatusText = (status: string) => {
    switch (status) {
      case 'new': return 'NEW';
      case 'reviewed': return 'REVIEWED';
      case 'in_progress': return 'IN PROGRESS';
      case 'resolved': return 'RESOLVED';
      default: return status.toUpperCase();
    }
  };

  const handleMarkAsReviewed = (caseId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent opening the modal
    const success = patientCaseService.updateCaseStatus(caseId, 'reviewed', 'Dr. Nomsa Mthembu');
    if (success) {
      console.log('Case marked as reviewed:', caseId);
    }
  };

  const handleMarkInProgress = (caseId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent opening the modal
    const success = patientCaseService.updateCaseStatus(caseId, 'in_progress', 'Dr. Nomsa Mthembu');
    if (success) {
      console.log('Case marked as in progress:', caseId);

      // Stop monitoring for this case and send final message
      const activeSessions = healthcareMonitoringService.getActiveSessions();
      const relevantSession = activeSessions.find(session => session.caseId === caseId);
      if (relevantSession) {
        console.log(`🤖 Stopping monitoring for case ${caseId} - doctor intervention`);
        healthcareMonitoringService.stopMonitoringWithDoctorMessage(relevantSession.sessionId);
      }

    }
  };

  // Sort cases by priority and time
  const sortCasesByPriority = (cases: any[]) => {
    const statusPriority = {
      'new': 1,
      'reviewed': 2,
      'in_progress': 3,
      'resolved': 4
    };

    return cases.sort((a, b) => {
      // First sort by status priority
      const statusDiff = (statusPriority[a.status] || 5) - (statusPriority[b.status] || 5);
      if (statusDiff !== 0) return statusDiff;

      // Then sort by time (latest first within same status)
      const timeA = new Date(a.reportData?.createdAt || a.createdAt || 0).getTime();
      const timeB = new Date(b.reportData?.createdAt || b.createdAt || 0).getTime();
      return timeB - timeA;
    });
  };

  // Filter cases by status
  const filterCasesByStatus = (cases: any[]) => {
    if (statusFilter === 'all') return cases;
    return cases.filter(case_ => case_.status === statusFilter);
  };

  // Get filtered and sorted urgent cases
  const getFilteredSortedCases = () => {
    const filtered = filterCasesByStatus(urgentCases);
    return sortCasesByPriority(filtered);
  };

  const filteredSortedCases = getFilteredSortedCases();

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: ubuntuTabStyles }} />
      <div className="min-h-screen relative overflow-hidden" style={{
        background: `
          linear-gradient(135deg,
            rgba(34, 139, 34, 0.9) 0%,    /* Green */
            rgba(255, 215, 0, 0.8) 20%,   /* Gold */
            rgba(255, 69, 0, 0.8) 40%,    /* Orange */
            rgba(220, 20, 60, 0.8) 60%,   /* Red */
            rgba(0, 0, 128, 0.8) 80%,     /* Blue */
            rgba(75, 0, 130, 0.9) 100%    /* Indigo */
          )
        `
      }}>
      {/* Ubuntu Pattern Overlay */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundImage: `
          radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
          radial-gradient(circle at 75% 75%, rgba(255,215,0,0.2) 3px, transparent 3px),
          radial-gradient(circle at 50% 50%, rgba(255,255,255,0.05) 1px, transparent 1px)
        `,
        backgroundSize: '120px 120px, 180px 180px, 90px 90px'
      }} />

      <div className="container mx-auto px-4 py-4 relative z-10">
        <div className="max-w-7xl mx-auto space-y-4">
          {/* Ubuntu Header */}
          <div className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className="flex items-center gap-3">
              <div style={{
                background: 'linear-gradient(135deg, #228B22, #FFD700)',
                padding: '0.6rem',
                borderRadius: '1rem',
                boxShadow: '0 4px 16px rgba(0,0,0,0.2)',
                border: '1px solid rgba(255,215,0,0.4)'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.3rem' }}>
                  <Stethoscope className="w-5 h-5 text-white" />
                  <span style={{ fontSize: '1rem' }}>🤝</span>
                </div>
              </div>
              <div>
                <h1 style={{
                  fontSize: '1.6rem',
                  fontWeight: 'bold',
                  color: 'white',
                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                  marginBottom: '0.2rem',
                  lineHeight: '1.2'
                }}>
                  Ubuntu Healthcare Provider
                </h1>
                <p style={{
                  color: '#FFD700',
                  fontSize: '0.85rem',
                  fontWeight: '600',
                  textShadow: '1px 1px 2px rgba(0,0,0,0.7)',
                  marginBottom: '0.1rem'
                }}>
                  Dr. Nomsa Mthembu • Khayelitsha Health Centre
                </p>
                <p style={{
                  color: 'rgba(255,255,255,0.9)',
                  fontSize: '0.75rem',
                  fontStyle: 'italic',
                  textShadow: '1px 1px 2px rgba(0,0,0,0.7)'
                }}>
                  "Siyakhathalela - We care for our community"
                </p>
              </div>
            </div>
            <div style={{
              background: 'rgba(0,0,0,0.3)',
              padding: '0.6rem',
              borderRadius: '0.8rem',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,215,0,0.4)'
            }}>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search Ubuntu patients..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{
                      paddingLeft: '2rem',
                      width: '14rem',
                      height: '2.2rem',
                      fontSize: '0.9rem',
                      borderRadius: '0.6rem',
                      border: '1px solid rgba(255,215,0,0.4)',
                      background: 'rgba(255,255,255,0.9)',
                      color: '#333'
                    }}
                  />
                </div>
                <Button
                  onClick={() => setShowMonitoringDashboard(true)}
                  className="bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white font-medium px-3 py-2 rounded-lg shadow-md transition-all duration-200 hover:scale-102"
                  style={{ height: '2.2rem', fontSize: '0.8rem' }}
                >
                  <Bot className="w-4 h-4 mr-1" />
                  AI Monitoring
                  <Badge className="ml-1 bg-white/20 text-white text-xs px-1 py-0.5">
                    {healthcareMonitoringService.getActiveSessions().length}
                  </Badge>
                </Button>
                {onLogout && (
                  <Button
                    onClick={() => {
                      if (window.confirm('Are you sure you want to logout?')) {
                        onLogout();
                      }
                    }}
                    className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium px-3 py-2 rounded-lg shadow-md transition-all duration-200 hover:scale-102"
                    style={{ height: '2.2rem', fontSize: '0.8rem' }}
                    title="Logout"
                  >
                    <span style={{ fontSize: '0.8rem', marginRight: '0.3rem' }}>🚪</span>
                    Logout
                  </Button>
                )}
              </div>
            </div>
          </div>

        {/* Compact Professional Stats Cards */}
        <div className={`grid grid-cols-1 md:grid-cols-4 gap-2 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="group relative">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-400 to-blue-600 rounded-xl blur opacity-50 group-hover:opacity-70 transition duration-300"></div>
            <div className="relative p-2 bg-white/95 backdrop-blur-xl border border-blue-200/60 rounded-xl shadow-md hover:shadow-blue-500/15 transition-all duration-200 hover:scale-101">
              <div className="flex items-center justify-between mb-1">
                <div className="flex-1">
                  <p className="text-xs font-bold text-gray-500 uppercase tracking-wide mb-0.5">Active Cases</p>
                  <p className="text-xl font-black text-blue-700 mb-0.5">47</p>
                  <div className="flex items-center gap-1">
                    <TrendingUp className="w-2.5 h-2.5 text-green-600" />
                    <span className="text-xs text-green-600 font-medium">+12%</span>
                  </div>
                </div>
                <div className="p-1.5 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg shadow-md">
                  <Users className="w-3.5 h-3.5 text-white" />
                </div>
              </div>
              <div className="w-full h-1.5 bg-blue-100 rounded-full overflow-hidden">
                <div className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full w-[78%] transition-all duration-1000"></div>
              </div>
            </div>
          </div>

          <div className="group relative">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-red-400 to-red-600 rounded-xl blur opacity-50 group-hover:opacity-70 transition duration-300"></div>
            <div className="relative p-2 bg-white/95 backdrop-blur-xl border border-red-200/60 rounded-xl shadow-md hover:shadow-red-500/15 transition-all duration-200 hover:scale-101">
              <div className="flex items-center justify-between mb-1">
                <div className="flex-1">
                  <p className="text-xs font-bold text-gray-500 uppercase tracking-wide mb-0.5">Urgent Cases</p>
                  <p className="text-xl font-black text-red-700 mb-0.5">
                    {urgentCases.length}
                  </p>
                  <div className="flex items-center gap-1">
                    <AlertTriangle className="w-2.5 h-2.5 text-red-600 animate-pulse" />
                    <span className="text-xs text-red-600 font-medium">Needs attention</span>
                  </div>
                </div>
                <div className="p-1.5 bg-gradient-to-br from-red-500 to-red-600 rounded-lg shadow-md">
                  <Bell className="w-3.5 h-3.5 text-white animate-pulse" />
                </div>
              </div>
              <div className="w-full h-1.5 bg-red-100 rounded-full overflow-hidden">
                <div className="h-full bg-gradient-to-r from-red-500 to-red-600 rounded-full w-[32%] transition-all duration-1000"></div>
              </div>
            </div>
          </div>

          <div className="group relative">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-green-400 to-green-600 rounded-xl blur opacity-50 group-hover:opacity-70 transition duration-300"></div>
            <div className="relative p-2 bg-white/95 backdrop-blur-xl border border-green-200/60 rounded-xl shadow-md hover:shadow-green-500/15 transition-all duration-200 hover:scale-101">
              <div className="flex items-center justify-between mb-1">
                <div className="flex-1">
                  <p className="text-xs font-bold text-gray-500 uppercase tracking-wide mb-0.5">Today's Consultations</p>
                  <p className="text-xl font-black text-green-700 mb-0.5">23</p>
                  <div className="flex items-center gap-1">
                    <Activity className="w-2.5 h-2.5 text-green-600" />
                    <span className="text-xs text-green-600 font-medium">On track</span>
                  </div>
                </div>
                <div className="p-1.5 bg-gradient-to-br from-green-500 to-green-600 rounded-lg shadow-md">
                  <Calendar className="w-3.5 h-3.5 text-white" />
                </div>
              </div>
              <div className="w-full h-1.5 bg-green-100 rounded-full overflow-hidden">
                <div className="h-full bg-gradient-to-r from-green-500 to-green-600 rounded-full w-[92%] transition-all duration-1000"></div>
              </div>
            </div>
          </div>

          <div className="group relative">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-orange-400 to-orange-600 rounded-xl blur opacity-50 group-hover:opacity-70 transition duration-300"></div>
            <div className="relative p-2 bg-white/95 backdrop-blur-xl border border-orange-200/60 rounded-xl shadow-md hover:shadow-orange-500/15 transition-all duration-200 hover:scale-101">
              <div className="flex items-center justify-between mb-1">
                <div className="flex-1">
                  <p className="text-xs font-bold text-gray-500 uppercase tracking-wide mb-0.5">Non-Responsive</p>
                  <p className="text-xl font-black text-orange-700 mb-0.5">3</p>
                  <div className="flex items-center gap-1">
                    <Clock className="w-2.5 h-2.5 text-orange-600" />
                    <span className="text-xs text-orange-600 font-medium">Follow up needed</span>
                  </div>
                </div>
                <div className="p-1.5 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg shadow-md">
                  <Phone className="w-3.5 h-3.5 text-white" />
                </div>
              </div>
              <div className="w-full h-1.5 bg-orange-100 rounded-full overflow-hidden">
                <div className="h-full bg-gradient-to-r from-orange-500 to-orange-600 rounded-full w-[12%] transition-all duration-1000"></div>
              </div>
            </div>
          </div>
        </div>

        {/* New Case Alert */}
        {newCaseAlert && (
          <div className="fixed top-4 right-4 z-50 animate-bounce">
            <div className="bg-red-500 text-white px-6 py-4 rounded-lg shadow-2xl border-2 border-red-300">
              <div className="flex items-center gap-3">
                <Bell className="w-6 h-6 animate-pulse" />
                <div>
                  <div className="font-bold text-lg">New Urgent Case!</div>
                  <div className="text-sm">Check the Urgent Ubuntu Care tab</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Ubuntu Main Content */}
        <Tabs defaultValue="urgent" className="space-y-4 ubuntu-tabs" style={{ marginTop: '1rem' }}>
          <TabsList className="grid w-full grid-cols-5 gap-2" style={{
            background: 'rgba(0,0,0,0.3)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,215,0,0.4)',
            borderRadius: '0.8rem',
            padding: '0.3rem',
            minHeight: '3rem'
          }}>
            <TabsTrigger
              value="urgent"
              className="ubuntu-tab-trigger flex flex-col items-center justify-center text-center"
              style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '0.7rem',
                padding: '0.3rem 0.2rem',
                borderRadius: '0.6rem',
                transition: 'all 0.3s ease',
                textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                lineHeight: '1.1',
                minHeight: '2.4rem',
                height: '2.4rem',
                whiteSpace: 'normal',
                wordWrap: 'break-word',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <span className="text-sm mb-0.5">🚨</span>
              <span>Urgent Ubuntu Care</span>
            </TabsTrigger>
            <TabsTrigger
              value="recent"
              className="ubuntu-tab-trigger flex flex-col items-center justify-center text-center"
              style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '0.7rem',
                padding: '0.3rem 0.2rem',
                borderRadius: '0.6rem',
                transition: 'all 0.3s ease',
                textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                lineHeight: '1.1',
                minHeight: '2.4rem',
                height: '2.4rem',
                whiteSpace: 'normal',
                wordWrap: 'break-word',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <span className="text-sm mb-0.5">🤝</span>
              <span>Recent Ubuntu Healing</span>
            </TabsTrigger>
            <TabsTrigger
              value="non-responsive"
              className="ubuntu-tab-trigger flex flex-col items-center justify-center text-center"
              style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '0.7rem',
                padding: '0.3rem 0.2rem',
                borderRadius: '0.6rem',
                transition: 'all 0.3s ease',
                textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                lineHeight: '1.1',
                minHeight: '2.4rem',
                height: '2.4rem',
                whiteSpace: 'normal',
                wordWrap: 'break-word',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <span className="text-sm mb-0.5">📞</span>
              <span>Ubuntu Outreach</span>
            </TabsTrigger>
            <TabsTrigger
              value="whatsapp"
              className="ubuntu-tab-trigger flex flex-col items-center justify-center text-center"
              style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '0.7rem',
                padding: '0.3rem 0.2rem',
                borderRadius: '0.6rem',
                transition: 'all 0.3s ease',
                textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                lineHeight: '1.1',
                minHeight: '2.4rem',
                height: '2.4rem',
                whiteSpace: 'normal',
                wordWrap: 'break-word',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <span className="text-sm mb-0.5">💬</span>
              <span>Ubuntu WhatsApp</span>
            </TabsTrigger>
            <TabsTrigger
              value="analytics"
              className="ubuntu-tab-trigger flex flex-col items-center justify-center text-center"
              style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '0.7rem',
                padding: '0.3rem 0.2rem',
                borderRadius: '0.6rem',
                transition: 'all 0.3s ease',
                textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                lineHeight: '1.1',
                minHeight: '2.4rem',
                height: '2.4rem',
                whiteSpace: 'normal',
                wordWrap: 'break-word',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <span className="text-sm mb-0.5">📊</span>
              <span>Mzansi Health Data</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="urgent" className="space-y-6" style={{ marginTop: '1.5rem', marginBottom: '2rem' }}>
            <div className="group relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-red-400 via-pink-500 to-red-600 rounded-3xl blur opacity-60 group-hover:opacity-80 transition duration-1000"></div>
              <div className="relative overflow-hidden bg-white/95 backdrop-blur-xl border-3 border-red-200/60 rounded-3xl shadow-2xl hover:shadow-red-500/25 transition-all duration-500">
                <div className="p-8 border-b-2 border-red-200/50 bg-gradient-to-r from-red-50/80 to-pink-50/80 backdrop-blur-sm">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="p-3 bg-gradient-to-br from-red-500 to-pink-600 rounded-full shadow-xl animate-pulse-slow">
                      <Bell className="w-6 h-6 text-white filter drop-shadow-lg" />
                    </div>
                    <div>
                      <h3 className="text-lg font-black text-gray-900 mb-1" style={{textShadow: '1px 1px 2px rgba(0,0,0,0.2)'}}>
                        Urgent Ubuntu Care
                        <span className="ml-2 px-2 py-0.5 bg-red-500 text-white text-xs rounded-full">
                          {urgentCases.length}
                        </span>
                        {urgentCases.length > 0 && (
                          <>
                            <span className="ml-1 px-1.5 py-0.5 bg-red-100 text-red-700 text-xs rounded-full border border-red-300">
                              {urgentCases.filter(c => c.status === 'new').length} New
                            </span>
                            <span className="ml-1 px-1.5 py-0.5 bg-blue-100 text-blue-700 text-xs rounded-full border border-blue-300">
                              {urgentCases.filter(c => c.status === 'reviewed').length} Reviewed
                            </span>
                            <span className="ml-1 px-1.5 py-0.5 bg-yellow-100 text-yellow-700 text-xs rounded-full border border-yellow-300">
                              {urgentCases.filter(c => c.status === 'in_progress').length} In Progress
                            </span>
                          </>
                        )}
                        {filteredSortedCases.length > 3 && (
                          <span className="ml-2 text-xs text-red-600 font-medium animate-pulse">
                            ↓ Scroll for more
                          </span>
                        )}
                      </h3>
                      <p className="text-gray-700 font-medium text-sm" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>Critical & severe cases requiring immediate Ubuntu healing attention</p>
                    </div>
                  </div>

                  {/* Filter Buttons */}
                  {urgentCases.length > 0 && (
                    <div className="space-y-2 mt-2">
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-medium text-gray-600">Filter by status:</span>
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <span>📊 Sorted by: Priority (New → Reviewed → In Progress) + Latest First</span>
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <button
                          onClick={() => setStatusFilter('all')}
                          className={`px-2 py-1 rounded-full text-xs font-medium transition-all duration-200 ${
                            statusFilter === 'all'
                              ? 'bg-gray-600 text-white shadow-md'
                              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                          }`}
                        >
                          All ({urgentCases.length})
                        </button>
                        <button
                          onClick={() => setStatusFilter('new')}
                          className={`px-2 py-1 rounded-full text-xs font-medium transition-all duration-200 ${
                            statusFilter === 'new'
                              ? 'bg-red-500 text-white shadow-md'
                              : 'bg-red-100 text-red-600 hover:bg-red-200'
                          }`}
                        >
                          New ({urgentCases.filter(c => c.status === 'new').length})
                        </button>
                        <button
                          onClick={() => setStatusFilter('reviewed')}
                          className={`px-2 py-1 rounded-full text-xs font-medium transition-all duration-200 ${
                            statusFilter === 'reviewed'
                              ? 'bg-blue-500 text-white shadow-md'
                              : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                          }`}
                        >
                          Reviewed ({urgentCases.filter(c => c.status === 'reviewed').length})
                        </button>
                        <button
                          onClick={() => setStatusFilter('in_progress')}
                          className={`px-2 py-1 rounded-full text-xs font-medium transition-all duration-200 ${
                            statusFilter === 'in_progress'
                              ? 'bg-yellow-500 text-white shadow-md'
                              : 'bg-yellow-100 text-yellow-600 hover:bg-yellow-200'
                          }`}
                        >
                          In Progress ({urgentCases.filter(c => c.status === 'in_progress').length})
                        </button>
                      </div>
                    </div>
                  )}
                </div>
                <div className="max-h-[70vh] overflow-y-auto p-4 space-y-3 urgent-cases-scroll" style={{
                  scrollbarWidth: 'thin',
                  scrollbarColor: '#ef4444 #fecaca'
                }}>
                  {filteredSortedCases.length === 0 && urgentCases.length === 0 ? (
                    <div className="text-center py-6">
                      <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center justify-center gap-2 text-green-700 mb-2">
                          <span className="text-2xl">🌿</span>
                          <span className="text-base font-bold">Ubuntu Peace</span>
                        </div>
                        <p className="text-green-600 font-medium text-sm">
                          No urgent cases at the moment. All patients are receiving proper Ubuntu care.
                        </p>
                        <p className="text-green-500 text-xs mt-1">
                          Urgent cases from the patient portal will appear here automatically.
                        </p>
                      </div>
                    </div>
                  ) : filteredSortedCases.length === 0 ? (
                    <div className="text-center py-6">
                      <div className="p-3 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl border border-blue-200/60 shadow-md">
                        <div className="flex items-center justify-center gap-2 mb-2">
                          <Filter className="w-5 h-5 text-blue-500" />
                          <h3 className="text-base font-black text-gray-800">No Cases Found</h3>
                        </div>
                        <p className="text-gray-600 font-medium text-sm">
                          No urgent cases match the selected filter: <span className="text-blue-600 capitalize">{statusFilter}</span>
                        </p>
                        <button
                          onClick={() => setStatusFilter('all')}
                          className="mt-2 px-3 py-1.5 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 transition-colors text-xs"
                        >
                          Show All Cases
                        </button>
                      </div>
                    </div>
                  ) : (
                    filteredSortedCases.map((patient) => (
                      <div key={patient.id} className="group/item relative cursor-pointer" onClick={() => setSelectedPatient(patient)}>
                        <div className={`absolute -inset-0.5 rounded-xl blur opacity-40 group-hover/item:opacity-60 transition duration-300 ${
                          patient.status === 'new' ? 'bg-gradient-to-r from-red-300 to-pink-300' :
                          patient.status === 'reviewed' ? 'bg-gradient-to-r from-blue-300 to-cyan-300' :
                          patient.status === 'in_progress' ? 'bg-gradient-to-r from-yellow-300 to-orange-300' :
                          'bg-gradient-to-r from-green-300 to-emerald-300'
                        }`}></div>
                        <div className={`relative p-3 bg-white/90 backdrop-blur-sm rounded-xl border shadow-md transition-all duration-200 hover:scale-101 ${
                          patient.status === 'new' ? 'border-red-200/60 hover:shadow-red-400/15' :
                          patient.status === 'reviewed' ? 'border-blue-200/60 hover:shadow-blue-400/15' :
                          patient.status === 'in_progress' ? 'border-yellow-200/60 hover:shadow-yellow-400/15' :
                          'border-green-200/60 hover:shadow-green-400/15'
                        }`}>
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-black text-base text-gray-900" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>{patient.name}</h3>
                              <Badge className={`${getSeverityColor(patient.severity)} text-white font-medium shadow-md text-xs px-2 py-0.5`}>
                                {patient.severity.toUpperCase()}
                              </Badge>
                              <Badge className={`${getCaseStatusColor(patient.status)} text-white font-medium shadow-md text-xs px-2 py-0.5`}>
                                {getCaseStatusText(patient.status)}
                              </Badge>
                              <Badge variant="outline" className="border border-gray-400 text-gray-700 font-medium text-xs px-2 py-0.5">{patient.language}</Badge>
                              {patient.status === 'new' && (
                                <div className="flex items-center gap-1 animate-pulse">
                                  <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                                  <span className="text-red-600 text-xs font-medium">🔥 URGENT - Needs Review</span>
                                </div>
                              )}
                              {patient.status === 'reviewed' && (
                                <div className="flex items-center gap-1">
                                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                                  <span className="text-blue-600 text-xs font-medium">👁️ Reviewed - Ready for Treatment</span>
                                </div>
                              )}
                              {patient.status === 'in_progress' && (
                                <div className="flex items-center gap-1">
                                  <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full animate-pulse"></div>
                                  <span className="text-yellow-600 text-xs font-medium">⚡ Treatment in Progress</span>
                                </div>
                              )}
                            </div>
                            <p className="text-gray-700 font-medium text-sm mb-2" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>ID: {patient.id} • Age: {patient.age}</p>
                            <p className="text-sm text-gray-700 font-medium flex items-center gap-1 mb-1" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>
                              <MapPin className="w-3 h-3 text-red-600 filter drop-shadow-sm" />
                              {patient.location}
                            </p>
                            <p className="text-sm font-medium mb-2 text-gray-900" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>Symptoms: {patient.symptoms}</p>
                            <div className="flex items-center gap-3 text-sm text-gray-600 font-medium">
                              <span className="flex items-center gap-1" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>
                                <Clock className="w-3 h-3 text-red-500 filter drop-shadow-sm" />
                                Reported: {patient.timeReported}
                              </span>
                              <span style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>Last contact: {patient.lastContact}</span>
                            </div>
                            {patient.status !== 'new' && patient.reviewedBy && (
                              <div className="mt-1 p-1.5 bg-blue-50 border border-blue-200 rounded-lg">
                                <div className="flex items-center gap-1 text-xs text-blue-700">
                                  <User className="w-3 h-3" />
                                  <span className="font-medium">Reviewed by: {patient.reviewedBy}</span>
                                  {patient.reviewedAt && (
                                    <span className="text-blue-600">
                                      • {new Date(patient.reviewedAt).toLocaleString()}
                                    </span>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                          <div className="flex flex-col gap-2">
                            <Button
                              size="sm"
                              className="bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 text-white font-medium shadow-md px-3 py-1.5 rounded-lg transition-all duration-200 hover:scale-102 text-xs"
                              onClick={() => setSelectedPatient(patient)}
                            >
                              Review Case
                            </Button>
                            {patient.status === 'new' && (
                              <Button
                                size="sm"
                                className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium shadow-md px-3 py-1.5 rounded-lg transition-all duration-200 hover:scale-102 text-xs"
                                onClick={(e) => handleMarkAsReviewed(patient.caseId, e)}
                              >
                                ✓ Mark Reviewed
                              </Button>
                            )}
                            {patient.status === 'reviewed' && (
                              <Button
                                size="sm"
                                className="bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white font-medium shadow-md px-3 py-1.5 rounded-lg transition-all duration-200 hover:scale-102 text-xs"
                                onClick={(e) => handleMarkInProgress(patient.caseId, e)}
                              >
                                🔄 Start Treatment
                              </Button>
                            )}
                            <Button size="sm" variant="outline" className="border border-gray-400 text-gray-700 hover:bg-gray-50 font-medium shadow-sm px-3 py-1.5 rounded-lg transition-all duration-200 hover:scale-102 text-xs">
                              📞 Call Patient
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                  )}
                </div>
                {/* Scroll fade indicator */}
                {filteredSortedCases.length > 3 && (
                  <div className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-white/95 to-transparent pointer-events-none rounded-b-xl"></div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="recent" className="space-y-6" style={{ marginTop: '1.5rem', marginBottom: '2rem' }}>
            <div className="group relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-green-400 via-emerald-500 to-teal-500 rounded-3xl blur opacity-60 group-hover:opacity-80 transition duration-1000"></div>
              <div className="relative overflow-hidden bg-white/95 backdrop-blur-xl border-2 border-green-200/60 rounded-xl shadow-lg hover:shadow-green-500/20 transition-all duration-300">
                <div className="p-4 border-b border-green-200/50 bg-gradient-to-r from-green-50/80 to-emerald-50/80 backdrop-blur-sm">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg shadow-md">
                      <Activity className="w-4 h-4 text-white filter drop-shadow-lg" />
                    </div>
                    <div>
                      <h3 className="text-lg font-black text-gray-900 mb-1" style={{textShadow: '1px 1px 2px rgba(0,0,0,0.2)'}}>Recent Ubuntu Healing</h3>
                      <p className="text-gray-700 font-medium text-sm" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>Latest patient interactions and their current Ubuntu healing status</p>
                    </div>
                  </div>
                </div>
                <div className="p-4 space-y-3">
                  {recentConsultations.map((patient) => (
                    <div key={patient.id} className="group/item relative cursor-pointer" onClick={() => setSelectedPatient(patient)}>
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-green-300 to-emerald-300 rounded-xl blur opacity-40 group-hover/item:opacity-60 transition duration-300"></div>
                      <div className="relative p-3 bg-white/90 backdrop-blur-sm rounded-xl border border-green-200/60 shadow-md hover:shadow-green-400/15 transition-all duration-200 hover:scale-101">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-black text-base text-gray-900" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>{patient.name}</h3>
                              <Badge className={`${getSeverityColor(patient.severity)} text-white font-medium shadow-md text-xs px-2 py-0.5`}>
                                {patient.severity}
                              </Badge>
                              <Badge className={`${getStatusColor(patient.status)} text-white font-medium shadow-md text-xs px-2 py-0.5`}>
                                {patient.status.replace('_', ' ')}
                              </Badge>
                            </div>
                            <p className="text-gray-700 font-medium text-sm mb-2" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>ID: {patient.id} • Age: {patient.age}</p>
                            <p className="text-sm text-gray-700 font-medium flex items-center gap-1 mb-1" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>
                              <MapPin className="w-3 h-3 text-green-600 filter drop-shadow-sm" />
                              {patient.location}
                            </p>
                            <p className="text-sm font-medium mb-2 text-gray-900" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>Symptoms: {patient.symptoms}</p>
                            <p className="text-sm text-gray-600 font-medium" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>Reported: {patient.timeReported}</p>
                          </div>
                          <div className="flex flex-col gap-2">
                            <Button size="sm" variant="outline" className="border border-green-400 text-green-700 hover:bg-green-50 font-medium shadow-sm px-3 py-1.5 rounded-lg transition-all duration-200 hover:scale-102 text-xs">
                              View Details
                            </Button>
                            <Button size="sm" variant="outline" className="border border-gray-400 text-gray-700 hover:bg-gray-50 font-medium shadow-sm px-3 py-1.5 rounded-lg transition-all duration-200 hover:scale-102 text-xs">
                              Schedule Follow-up
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="non-responsive" className="space-y-6" style={{ marginTop: '1.5rem', marginBottom: '2rem' }}>
            <div className="group relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-orange-400 via-amber-500 to-orange-600 rounded-3xl blur opacity-60 group-hover:opacity-80 transition duration-1000"></div>
              <div className="relative overflow-hidden bg-white/95 backdrop-blur-xl border-2 border-orange-200/60 rounded-xl shadow-lg hover:shadow-orange-500/20 transition-all duration-300">
                <div className="p-4 border-b border-orange-200/50 bg-gradient-to-r from-orange-50/80 to-amber-50/80 backdrop-blur-sm">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="p-2 bg-gradient-to-br from-orange-500 to-amber-600 rounded-lg shadow-md">
                      <Phone className="w-4 h-4 text-white filter drop-shadow-lg" />
                    </div>
                    <div>
                      <h3 className="text-lg font-black text-gray-900 mb-1" style={{textShadow: '1px 1px 2px rgba(0,0,0,0.2)'}}>Ubuntu Outreach</h3>
                      <p className="text-gray-700 font-medium text-sm" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>Patients who haven't responded to Ubuntu follow-up calls or messages</p>
                    </div>
                  </div>
                </div>
                <div className="p-4 space-y-3">
                  {nonResponsiveCases.map((patient) => (
                    <div key={patient.id} className="group/item relative cursor-pointer">
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-orange-300 to-amber-300 rounded-xl blur opacity-40 group-hover/item:opacity-60 transition duration-300"></div>
                      <div className="relative p-3 bg-gradient-to-br from-orange-50/90 to-amber-50/90 backdrop-blur-sm rounded-xl border border-orange-200/60 shadow-md hover:shadow-orange-400/15 transition-all duration-200 hover:scale-101">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-black text-base text-gray-900" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>{patient.name}</h3>
                              <Badge variant="outline" className="border border-orange-600 text-orange-700 font-medium text-xs px-2 py-0.5">
                                {patient.language}
                              </Badge>
                              {patient.homeVisitAssigned && (
                                <Badge className="bg-gradient-to-r from-blue-500 to-cyan-600 text-white font-medium shadow-md text-xs px-2 py-0.5">Home Visit Assigned</Badge>
                              )}
                            </div>
                            <p className="text-gray-700 font-medium text-sm mb-2" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>ID: {patient.id} • Age: {patient.age}</p>
                            <p className="text-sm text-gray-700 font-medium flex items-center gap-1 mb-1" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>
                              <MapPin className="w-3 h-3 text-orange-600 filter drop-shadow-sm" />
                              {patient.location}
                            </p>
                            <p className="text-sm font-medium mb-2 text-gray-900" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>Condition: {patient.condition}</p>
                            <p className="text-sm text-orange-700 font-medium" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.05)'}}>
                              Last contact: {patient.lastContact}
                            </p>
                          </div>
                          <div className="flex flex-col gap-2">
                            <Button size="sm" className="bg-gradient-to-r from-orange-500 to-amber-600 hover:from-orange-600 hover:to-amber-700 text-white font-medium shadow-md px-3 py-1.5 rounded-lg transition-all duration-200 hover:scale-102 text-xs">
                              Assign Home Visit
                            </Button>
                            <Button size="sm" variant="outline" className="border border-orange-400 text-orange-700 hover:bg-orange-50 font-medium shadow-sm px-3 py-1.5 rounded-lg transition-all duration-200 hover:scale-102 text-xs">
                              Try Contact Again
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="whatsapp" className="space-y-6" style={{ marginTop: '1.5rem', marginBottom: '2rem' }}>
            <div className={`transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
              <div className="group relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-green-400 via-emerald-500 to-teal-600 rounded-3xl blur opacity-60 group-hover:opacity-80 transition duration-1000"></div>
                <div className="relative overflow-hidden bg-white/95 backdrop-blur-xl border-2 border-green-200/60 rounded-xl shadow-lg hover:shadow-green-500/20 transition-all duration-300">
                  <div className="p-4 border-b border-green-200/50 bg-gradient-to-r from-green-50/80 to-emerald-50/80 backdrop-blur-sm">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg shadow-md">
                        <MessageCircle className="w-4 h-4 text-white filter drop-shadow-lg" />
                      </div>
                      <div>
                        <h3 className="text-lg font-black text-gray-900 mb-1" style={{textShadow: '1px 1px 2px rgba(0,0,0,0.2)'}}>Ubuntu WhatsApp</h3>
                        <p className="text-gray-700 font-medium text-sm" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>Real-time Ubuntu messaging with patients for consultations and follow-ups</p>
                      </div>
                    </div>
                  </div>
                  <div className="p-0">
                    <WhatsAppCommunicationBoard />
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6" style={{ marginTop: '1.5rem', marginBottom: '2rem' }}>
            {/* Mzansi Health Data Header */}
            <div className="group relative mb-4">
              <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-400 via-pink-500 to-indigo-500 rounded-xl blur opacity-50 group-hover:opacity-70 transition duration-500"></div>
              <div className="relative overflow-hidden bg-white/95 backdrop-blur-xl border-2 border-purple-200/60 rounded-xl shadow-lg hover:shadow-purple-500/20 transition-all duration-300">
                <div className="p-4 border-b border-purple-200/50 bg-gradient-to-r from-purple-50/80 to-pink-50/80 backdrop-blur-sm">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="p-2 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg shadow-md">
                      <TrendingUp className="w-4 h-4 text-white filter drop-shadow-lg" />
                    </div>
                    <div>
                      <h3 className="text-lg font-black text-gray-900 mb-1" style={{textShadow: '1px 1px 2px rgba(0,0,0,0.2)'}}>
                        Mzansi Health Statistics
                        <Badge className="ml-2 bg-red-500 text-white font-medium shadow-md text-xs px-2 py-0.5">
                          🔒 Provider Only
                        </Badge>
                      </h3>
                      <p className="text-gray-700 font-medium text-sm" style={{textShadow: '1px 1px 1px rgba(0,0,0,0.1)'}}>
                        Comprehensive health data analytics for Ubuntu healthcare providers across South Africa
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-purple-700 bg-purple-100 p-2 rounded-lg border border-purple-200">
                    <AlertTriangle className="w-3 h-3" />
                    <span className="font-medium">
                      🔐 Secure Access: This health statistics data is restricted to authenticated healthcare providers only for patient privacy and POPIA compliance.
                    </span>
                  </div>
                </div>
                <div className="p-4">
                  <div className="text-center">
                    <Button
                      onClick={() => onNavigateToStatistics?.()}
                      className="bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white font-medium px-4 py-2 rounded-lg shadow-md transition-all duration-200 hover:scale-102 text-sm"
                    >
                      📊 Access Full Health Statistics Dashboard
                    </Button>
                    <p className="mt-2 text-gray-600 font-medium text-xs">
                      Click above to access the comprehensive health statistics and analytics dashboard
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

        </Tabs>
        </div>
      </div>

      {selectedPatient && (
        <PatientDetailModal
          patient={selectedPatient}
          onClose={() => setSelectedPatient(null)}
        />
      )}

      {showMonitoringDashboard && (
        <MonitoringDashboard
          onClose={() => setShowMonitoringDashboard(false)}
        />
      )}
      </div>
    </>
  );
};

export default ProviderDashboard;
