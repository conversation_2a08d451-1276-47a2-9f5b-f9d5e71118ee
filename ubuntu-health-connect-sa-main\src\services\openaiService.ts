// OpenAI Health Service for AI Health Assistant Chat
// Uses backend proxy to avoid CORS issues, with intelligent fallback system

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

export interface HealthAssistantResponse {
  message: string;
  severity?: 'low' | 'moderate' | 'high' | 'critical';
  recommendations?: string[];
  followUpQuestions?: string[];
  urgentCare?: boolean;
}

class OpenAIHealthService {
  private systemPrompt = `You are a multilingual AI health assistant for HealthConnect SA, designed to help South African patients with medical questions and symptom assessment. 

IMPORTANT GUIDELINES:
- You can communicate in English, Zulu (isiZulu), Xhosa (isiXhosa), and Afrikaans
- Always be empathetic, professional, and culturally sensitive
- Provide helpful health information but NEVER diagnose or replace professional medical care
- Always recommend consulting healthcare professionals for serious symptoms
- Be aware of South African healthcare context and common health issues
- If symptoms seem urgent, strongly recommend immediate medical attention
- Ask follow-up questions to better understand the patient's condition
- Provide practical advice for symptom management when appropriate

RESPONSE FORMAT:
- Keep responses conversational and easy to understand
- Use simple language appropriate for all education levels
- Include cultural sensitivity for South African context
- Always end with appropriate next steps or follow-up questions

EMERGENCY INDICATORS:
If you detect any of these, immediately recommend urgent medical care:
- Chest pain, difficulty breathing, severe abdominal pain
- Signs of stroke, severe headache, loss of consciousness
- Severe bleeding, high fever with confusion
- Suicidal thoughts or severe mental health crisis

Remember: You are a helpful assistant, not a doctor. Always encourage professional medical consultation when needed.`;

  async sendMessage(
    messages: ChatMessage[],
    userLanguage: string = 'en'
  ): Promise<HealthAssistantResponse> {
    try {
      console.log('🔄 Attempting to send message to AI service...', { userLanguage, messageCount: messages.length });

      // Convert our message format to OpenAI format
      const openAIMessages = [
        {
          role: 'system',
          content: `${this.systemPrompt}\n\nUser's preferred language: ${this.getLanguageName(userLanguage)}. Please respond in this language when possible.`
        },
        ...messages.map(msg => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content
        }))
      ];

      console.log('📤 Trying backend proxy first...');

      // Try backend proxy first
      try {
        const response = await fetch('http://localhost:5000/api/openai/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'gpt-4o-mini',
            messages: openAIMessages,
            max_tokens: 500,
            temperature: 0.7
          })
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data) {
            const completion = result.data;
            const responseContent = completion.choices[0]?.message?.content;

            if (responseContent) {
              console.log('✅ Backend proxy successful');
              const analysis = this.analyzeResponse(responseContent, messages);
              return {
                message: responseContent,
                ...analysis
              };
            }
          }
        }
        console.log('⚠️ Backend proxy failed, trying fallback...');
      } catch (proxyError) {
        console.log('⚠️ Backend proxy error, trying fallback:', proxyError.message);
      }

      // Fallback: Use a mock response for development
      console.log('🔄 Using development fallback response...');

      const userMessage = messages.filter(m => m.role === 'user').pop()?.content || '';
      const mockResponse = this.generateMockResponse(userMessage, userLanguage);

      const analysis = this.analyzeResponse(mockResponse, messages);

      console.log('✅ Development fallback response generated');

      return {
        message: mockResponse,
        ...analysis
      };

    } catch (error) {
      console.error('❌ AI Service Error:', error);

      // Final fallback
      return {
        message: 'I apologize, but I\'m currently experiencing technical difficulties. Please try again in a moment, or contact our support team if the issue persists.',
        severity: 'low',
        recommendations: ['Try again in a few minutes', 'Contact support if issue persists'],
        urgentCare: false
      };
    }
  }

  private generateMockResponse(userMessage: string, language: string): string {
    const lowerMessage = userMessage.toLowerCase();

    // Emergency keywords
    if (lowerMessage.includes('chest pain') || lowerMessage.includes('can\'t breathe') ||
        lowerMessage.includes('severe pain') || lowerMessage.includes('emergency')) {
      return 'I understand you\'re experiencing serious symptoms. Please seek immediate medical attention by calling emergency services or going to the nearest hospital. This is important for your safety.';
    }

    // Common symptoms
    if (lowerMessage.includes('headache') || lowerMessage.includes('head pain')) {
      return 'I understand you\'re experiencing headaches. This can have various causes including stress, dehydration, or tension. Can you tell me more about when the headaches started and how severe they are on a scale of 1-10?';
    }

    if (lowerMessage.includes('fever') || lowerMessage.includes('temperature')) {
      return 'Fever can be a sign that your body is fighting an infection. Have you taken your temperature? It\'s important to stay hydrated and rest. If your fever is high (over 39°C/102°F) or persists, please consider seeing a healthcare provider.';
    }

    if (lowerMessage.includes('cough') || lowerMessage.includes('coughing')) {
      return 'I understand you have a cough. Is it a dry cough or are you bringing up phlegm? How long have you had this cough? Coughs can be caused by various factors including infections, allergies, or irritants.';
    }

    if (lowerMessage.includes('tired') || lowerMessage.includes('fatigue') || lowerMessage.includes('exhausted')) {
      return 'Fatigue can have many causes including lack of sleep, stress, poor nutrition, or underlying health conditions. How long have you been feeling this way? Are you getting adequate sleep and maintaining a balanced diet?';
    }

    // General health inquiry
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('help')) {
      return 'Hello! I\'m your AI health assistant. I\'m here to help you with health questions and provide guidance. Please tell me about any symptoms or health concerns you may have, and I\'ll do my best to assist you.';
    }

    // Default response
    return 'Thank you for sharing your health concern with me. To better assist you, could you please provide more details about your symptoms, including when they started and how they\'re affecting you? This will help me give you more personalized guidance.';
  }

  private analyzeResponse(response: string, messages: ChatMessage[]): Partial<HealthAssistantResponse> {
    const lowerResponse = response.toLowerCase();
    const lastUserMessage = messages.filter(m => m.role === 'user').pop()?.content.toLowerCase() || '';

    // Determine severity based on keywords and context
    let severity: 'low' | 'moderate' | 'high' | 'critical' = 'low';
    let urgentCare = false;

    // Critical symptoms
    if (this.containsUrgentKeywords(lastUserMessage) || this.containsUrgentKeywords(lowerResponse)) {
      severity = 'critical';
      urgentCare = true;
    }
    // High severity symptoms
    else if (this.containsHighSeverityKeywords(lastUserMessage)) {
      severity = 'high';
    }
    // Moderate symptoms
    else if (this.containsModerateSeverityKeywords(lastUserMessage)) {
      severity = 'moderate';
    }

    // Generate recommendations based on severity
    const recommendations = this.generateRecommendations(severity, urgentCare);
    
    // Generate follow-up questions
    const followUpQuestions = this.generateFollowUpQuestions(lastUserMessage);

    return {
      severity,
      recommendations,
      followUpQuestions,
      urgentCare
    };
  }

  private containsUrgentKeywords(text: string): boolean {
    const urgentKeywords = [
      // English urgent symptoms
      'chest pain', 'can\'t breathe', 'difficulty breathing', 'severe pain',
      'bleeding heavily', 'unconscious', 'stroke', 'heart attack',
      'severe headache', 'suicidal', 'overdose', 'poisoning',
      'choking', 'severe bleeding', 'broken bone', 'severe burn',
      'severe allergic reaction', 'anaphylaxis', 'emergency',

      // Zulu urgent symptoms
      'ubuhlungu besifuba', 'ukuphefumula kanzima', 'ukuphefumula kufushane',
      'ubuhlungu obukhulu', 'ukuphela kwegazi', 'ukuphelelwa amandla',
      'ukuqhaqhazela', 'isitroke', 'isifo senhliziyo',

      // Afrikaans urgent symptoms
      'borspyn', 'asemhaling probleme', 'kortasem', 'erge pyn',
      'bloeding', 'bewusteloos', 'toeval', 'beroerte', 'hartaanval'
    ];
    return urgentKeywords.some(keyword => text.toLowerCase().includes(keyword.toLowerCase()));
  }

  private containsHighSeverityKeywords(text: string): boolean {
    const highSeverityKeywords = [
      'high fever', 'severe', 'intense pain', 'vomiting blood',
      'persistent vomiting', 'severe diarrhea', 'dehydrated'
    ];
    return highSeverityKeywords.some(keyword => text.includes(keyword));
  }

  private containsModerateSeverityKeywords(text: string): boolean {
    const moderateKeywords = [
      'fever', 'pain', 'headache', 'nausea', 'vomiting', 'diarrhea',
      'cough', 'sore throat', 'fatigue', 'dizzy'
    ];
    return moderateKeywords.some(keyword => text.includes(keyword));
  }

  private generateRecommendations(severity: string, urgentCare: boolean): string[] {
    if (urgentCare) {
      return [
        'Seek immediate medical attention',
        'Call emergency services or go to nearest hospital',
        'Do not delay - this could be serious'
      ];
    }

    switch (severity) {
      case 'high':
        return [
          'Schedule appointment with healthcare provider within 24 hours',
          'Monitor symptoms closely',
          'Call 10010 if symptoms worsen'
        ];
      case 'moderate':
        return [
          'Consider scheduling appointment with healthcare provider',
          'Rest and stay hydrated',
          'Monitor symptoms for changes'
        ];
      default:
        return [
          'Continue monitoring symptoms',
          'Maintain healthy lifestyle',
          'Contact healthcare provider if symptoms persist'
        ];
    }
  }

  private generateFollowUpQuestions(userMessage: string): string[] {
    const questions = [];
    
    if (userMessage.includes('pain')) {
      questions.push('On a scale of 1-10, how would you rate the pain?');
      questions.push('When did the pain start?');
    }
    
    if (userMessage.includes('fever')) {
      questions.push('Have you taken your temperature?');
      questions.push('Are you experiencing chills or sweating?');
    }
    
    if (userMessage.includes('cough')) {
      questions.push('Is it a dry cough or are you bringing up phlegm?');
      questions.push('Any shortness of breath?');
    }

    return questions.slice(0, 2); // Limit to 2 questions
  }

  private getLanguageName(code: string): string {
    const languages: Record<string, string> = {
      'en': 'English',
      'zu': 'isiZulu (Zulu)',
      'xh': 'isiXhosa (Xhosa)',
      'af': 'Afrikaans'
    };
    return languages[code] || 'English';
  }




}

export const openAIHealthService = new OpenAIHealthService();

// Test function to verify OpenAI backend proxy is working
(window as any).testOpenAIConnection = async () => {
  console.log('🧪 Testing OpenAI Backend Proxy Connection...');

  try {
    console.log('✅ Testing backend proxy instead of direct API');

    // Test simple API call via backend proxy
    const testMessages = [{
      id: 'test-1',
      role: 'user' as const,
      content: 'Hello, can you help me?',
      timestamp: new Date()
    }];

    console.log('📤 Sending test message to backend proxy...');
    const response = await openAIHealthService.sendMessage(testMessages, 'en');

    console.log('📥 Backend Proxy Response:', response);

    if (response.message && !response.message.includes('technical difficulties')) {
      console.log('🎉 SUCCESS: OpenAI Backend Proxy is working correctly!');
      return true;
    } else {
      console.log('❌ ISSUE: Backend proxy returned fallback response');
      return false;
    }

  } catch (error) {
    console.error('❌ Backend Proxy Test Failed:', error);
    return false;
  }
};
