<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Urgent Cases</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        button {
            background: #ff4444;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #cc3333;
        }
        .output {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .urgent-case {
            background: rgba(255, 68, 68, 0.2);
            border: 2px solid #ff4444;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Ubuntu Health - Urgent Cases Test</h1>
        <p>This page helps test the urgent case functionality between patient portal and healthcare provider dashboard.</p>
        
        <div>
            <button onclick="debugCases()">Debug Current Cases</button>
            <button onclick="createTestCase()">Create Test Urgent Case</button>
            <button onclick="clearAllCases()">Clear All Cases</button>
            <button onclick="checkLocalStorage()">Check LocalStorage</button>
        </div>
        
        <div id="output" class="output">Click buttons above to test urgent case functionality...</div>
        
        <div id="urgent-cases"></div>
        
        <h3>Instructions:</h3>
        <ol>
            <li>Open the Healthcare Provider Dashboard in another tab</li>
            <li>Click "Create Test Urgent Case" here</li>
            <li>Check if the urgent case appears in the provider dashboard</li>
            <li>Use "Debug Current Cases" to see what's stored</li>
        </ol>
    </div>

    <script>
        const STORAGE_KEY = 'ubuntu-health-patient-cases';

        function log(message) {
            const output = document.getElementById('output');
            output.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }

        // Create test urgent case directly via database API
        async function createTestUrgentCase() {
            const testInteraction = {
                id: `test_interaction_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                patient_id: 'TEST_PATIENT_001',
                interaction_type: 'assessment',
                summary: 'Test urgent case with severe chest pain and breathing difficulties',
                full_conversation: JSON.stringify([
                    {
                        id: 'test1',
                        role: 'user',
                        content: 'I have severe chest pain and can\'t breathe properly',
                        timestamp: new Date().toISOString()
                    },
                    {
                        id: 'test2',
                        role: 'assistant',
                        content: 'This sounds very serious. You need immediate medical attention.',
                        timestamp: new Date().toISOString()
                    }
                ]),
                ai_assessment: JSON.stringify({
                    assessmentSummary: {
                        severity: 'critical',
                        priority: 'Critical Priority',
                        description: 'Test urgent case with severe chest pain',
                        urgentCare: true
                    },
                    symptomsReported: {
                        primary: ['severe chest pain', 'difficulty breathing'],
                        secondary: ['sweating', 'nausea'],
                        duration: '30 minutes',
                        onset: 'sudden'
                    },
                    aiRecommendations: {
                        immediate: ['Seek immediate medical attention', 'Call emergency services'],
                        shortTerm: ['Hospital evaluation', 'Cardiac assessment'],
                        longTerm: ['Follow up with cardiologist']
                    },
                    followUpPlan: {
                        appointmentNeeded: true,
                        timeframe: 'Immediately',
                        location: 'Emergency Department',
                        medicationDelivery: false,
                        nextCheckIn: 'Immediate',
                        additionalNotes: ['Test case for urgent care functionality']
                    },
                    conversationSummary: 'Test patient reported severe chest pain and breathing difficulties',
                    riskFactors: ['chest pain', 'breathing difficulty'],
                    redFlags: ['severe chest pain', 'difficulty breathing']
                }),
                severity: 'Critical',
                recommendations: JSON.stringify(['Seek immediate medical attention', 'Call emergency services']),
                urgent_care: true
            };

            try {
                // Save to database
                const response = await fetch('http://localhost:5001/api/ai-interactions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testInteraction),
                });

                if (response.ok) {
                    const result = await response.json();
                    log('✅ Test urgent case created in database: ' + testInteraction.patient_id);
                    console.log('Database response:', result);
                } else {
                    log('❌ Failed to create test case in database');
                    console.error('Database error:', await response.text());
                }
            } catch (error) {
                log('❌ Error creating test case: ' + error.message);
                console.error('Error:', error);
            }

            // Refresh display
            displayUrgentCases();

            return testInteraction;
        }

        async function debugCases() {
            log('=== Debug State (Database) ===');

            try {
                // Debug database
                const response = await fetch('http://localhost:5001/api/ai-interactions', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    const result = await response.json();
                    const interactions = result.data?.interactions || [];

                    log('Total interactions in database: ' + interactions.length);

                    interactions.forEach((interaction, index) => {
                        const isUrgent = interaction.urgent_care ||
                                       interaction.severity === 'Critical' ||
                                       interaction.severity === 'High';
                        log(`Interaction ${index + 1}: Patient ${interaction.patient_id} - Urgent: ${isUrgent} - Type: ${interaction.interaction_type}`);
                        console.log(`Interaction ${index + 1}:`, interaction);
                    });
                } else {
                    log('❌ Failed to load debug data from database');
                }
            } catch (error) {
                log('❌ Error loading debug data: ' + error.message);
                console.error('Error:', error);
            }

            // Also check localStorage
            const localData = localStorage.getItem(STORAGE_KEY);
            if (localData) {
                const cases = JSON.parse(localData);
                log('LocalStorage fallback cases: ' + cases.length);
            } else {
                log('No cases in localStorage');
            }

            log('===============================');
        }

        function createTestCase() {
            createTestUrgentCase();
        }

        async function clearAllCases() {
            try {
                // Clear from database
                const response = await fetch('http://localhost:5001/api/ai-interactions', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    log('✅ All cases cleared from database');
                } else {
                    log('❌ Failed to clear cases from database');
                }
            } catch (error) {
                log('❌ Error clearing cases: ' + error.message);
                console.error('Error:', error);
            }

            // Also clear localStorage as fallback
            localStorage.removeItem(STORAGE_KEY);
            log('📱 LocalStorage cleared as well');

            // Refresh display
            displayUrgentCases();
        }

        function checkLocalStorage() {
            const data = localStorage.getItem(STORAGE_KEY);
            if (data) {
                const cases = JSON.parse(data);
                log('LocalStorage contains ' + cases.length + ' cases');
                cases.forEach((case_, index) => {
                    const isUrgent = case_.reportData.assessmentSummary.urgentCare ||
                                   case_.reportData.assessmentSummary.severity === 'critical' ||
                                   case_.reportData.assessmentSummary.severity === 'high';
                    log(`Case ${index + 1}: ${case_.patientName} - Urgent: ${isUrgent}`);
                });
            } else {
                log('No cases found in LocalStorage');
            }
        }

        async function displayUrgentCases() {
            const container = document.getElementById('urgent-cases');
            container.innerHTML = '<h3>🔄 Loading urgent cases from database...</h3>';

            try {
                // Load from database
                const response = await fetch('http://localhost:5001/api/ai-interactions', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    const result = await response.json();
                    const interactions = result.data?.interactions || [];

                    // Filter for urgent cases
                    const urgentCases = interactions.filter(interaction => {
                        try {
                            const assessment = JSON.parse(interaction.ai_assessment || '{}');
                            return interaction.urgent_care ||
                                   interaction.severity === 'Critical' ||
                                   interaction.severity === 'High' ||
                                   assessment.assessmentSummary?.urgentCare ||
                                   assessment.assessmentSummary?.severity === 'critical' ||
                                   assessment.assessmentSummary?.severity === 'high';
                        } catch (e) {
                            return interaction.urgent_care || interaction.severity === 'Critical';
                        }
                    });

                    container.innerHTML = '';

                    if (urgentCases.length > 0) {
                        container.innerHTML = '<h3>🚨 Current Urgent Cases from Database:</h3>';
                        urgentCases.forEach(interaction => {
                            try {
                                const assessment = JSON.parse(interaction.ai_assessment || '{}');
                                const div = document.createElement('div');
                                div.className = 'urgent-case';
                                div.innerHTML = `
                                    <strong>Patient ${interaction.patient_id}</strong><br>
                                    Severity: ${interaction.severity}<br>
                                    Urgent Care: ${interaction.urgent_care}<br>
                                    Type: ${interaction.interaction_type}<br>
                                    Summary: ${interaction.summary}<br>
                                    Created: ${new Date(interaction.timestamp).toLocaleString()}
                                `;
                                container.appendChild(div);
                            } catch (e) {
                                console.error('Error displaying case:', e);
                            }
                        });
                    } else {
                        container.innerHTML = '<h3>✅ No urgent cases found in database</h3>';
                    }

                    log(`📊 Loaded ${interactions.length} total interactions, ${urgentCases.length} urgent cases`);
                } else {
                    container.innerHTML = '<h3>❌ Failed to load from database</h3>';
                    log('❌ Failed to load cases from database');
                }
            } catch (error) {
                container.innerHTML = '<h3>❌ Error connecting to database</h3>';
                log('❌ Error loading cases: ' + error.message);
                console.error('Error:', error);
            }
        }

        // Listen for storage changes from other tabs
        window.addEventListener('storage', (e) => {
            if (e.key === STORAGE_KEY) {
                log('Storage changed in another tab, refreshing display...');
                displayUrgentCases();
            }
        });

        // Auto-refresh display every 2 seconds
        setInterval(displayUrgentCases, 2000);

        // Initial display
        displayUrgentCases();

        log('Test page loaded. Open browser console for detailed debug info.');
    </script>
</body>
</html>
