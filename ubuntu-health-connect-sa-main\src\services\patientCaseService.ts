// Patient Case Service - Database-backed patient case management
import { MedicalReportData } from './medicalReportService';
import { getAiInteractionsUrl, getBackendUrl } from '@/config/simple-config';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export interface PatientCase {
  id: string;
  patientId: string;
  patientName: string;
  age: number;
  location: string;
  language: string;
  reportData: MedicalReportData;
  conversation: ChatMessage[];
  createdAt: Date;
  status: 'new' | 'reviewed' | 'in_progress' | 'resolved';
  reviewedBy?: string;
  reviewedAt?: Date;
}

// Using simple configuration for reliable backend communication

class PatientCaseService {
  private cases: PatientCase[] = [];
  private listeners: Array<(cases: PatientCase[]) => void> = [];

  constructor() {
    this.loadCasesFromDatabase();
    console.log('PatientCaseService initialized with database backend');
  }

  // Load cases from localStorage as fallback
  private loadCasesFromLocalStorage() {
    try {
      const stored = localStorage.getItem('ubuntu-health-patient-cases');
      if (stored) {
        const parsedCases = JSON.parse(stored);
        this.cases = parsedCases.map((case_: any) => ({
          ...case_,
          createdAt: new Date(case_.createdAt),
          reviewedAt: case_.reviewedAt ? new Date(case_.reviewedAt) : undefined,
          conversation: case_.conversation.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }))
        }));
        console.log('📱 Loaded', this.cases.length, 'cases from localStorage');
        console.log('🚨 Urgent cases from localStorage:', this.getUrgentCases().length);
        this.notifyListeners();
      }
    } catch (error) {
      console.error('Error loading cases from localStorage:', error);
      this.cases = [];
    }
  }

  // Save cases to localStorage as fallback
  private saveCasesToLocalStorage() {
    try {
      localStorage.setItem('ubuntu-health-patient-cases', JSON.stringify(this.cases));
      console.log('📱 Saved', this.cases.length, 'cases to localStorage');
    } catch (error) {
      console.error('Error saving cases to localStorage:', error);
    }
  }

  // Subscribe to case updates
  subscribe(listener: (cases: PatientCase[]) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  // Load cases from database
  private async loadCasesFromDatabase() {
    try {
      const response = await fetch(getAiInteractionsUrl(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('📥 Database response:', data);

        if (data.success && data.data) {
          const interactions = data.data.interactions || data.data;
          console.log('📊 Raw interactions from database:', interactions.length);

          this.cases = this.convertInteractionsToCases(interactions);
          console.log('✅ Loaded', this.cases.length, 'valid cases from database');
          console.log('🚨 Urgent cases:', this.getUrgentCases().length);
          this.notifyListeners();
        } else {
          console.warn('❌ Invalid database response structure:', data);
        }
      } else {
        console.warn('❌ Failed to load from database. Status:', response.status);
      }
    } catch (error) {
      console.error('❌ Error loading cases from database:', error);
    }
  }



  // Save case to database
  private async saveCaseToDatabase(patientCase: PatientCase): Promise<boolean> {
    try {
      const interactionData = this.convertCaseToInteraction(patientCase);

      const response = await fetch(getAiInteractionsUrl(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(interactionData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Case saved to database:', result);
        return true;
      } else {
        console.warn('❌ Failed to save case to database. Status:', response.status);
        return false;
      }
    } catch (error) {
      console.error('❌ Error saving case to database:', error);
      return false;
    }
  }



  // Convert database AI interactions to PatientCase format
  // Only include real AI Health Assistant Chat interactions, no mock data
  private convertInteractionsToCases(interactions: any[]): PatientCase[] {
    console.log('🔍 Converting interactions to cases. Total interactions:', interactions.length);

    return interactions
      .filter(interaction => {
        // Include all real AI Health Assistant interactions (chat, assessment, medical reports)
        // Exclude test data and mock interactions
        const isValidType = interaction.interaction_type === 'chat' ||
                           interaction.interaction_type === 'assessment' ||
                           interaction.interaction_type === 'medical_report' ||
                           interaction.interaction_type === 'urgent_medical_report' ||
                           interaction.interaction_type === 'urgent_chat';
        const hasContent = interaction.summary && interaction.full_conversation;
        const notTestData = !interaction.patient_id.includes('TEST') &&
                           !interaction.patient_id.includes('test') &&
                           !interaction.patient_id.includes('MOCK') &&
                           !interaction.patient_id.includes('DEMO') &&
                           !interaction.patient_id.includes('SAMPLE') &&
                           interaction.patient_id !== 'UNKNOWN_PATIENT' &&
                           interaction.patient_id !== '' &&
                           !interaction.summary.includes('Test') &&
                           !interaction.summary.includes('test');
        const hasAssessment = interaction.ai_assessment && interaction.ai_assessment !== '{}';

        const isValid = isValidType && hasContent && notTestData && hasAssessment;

        if (!isValid) {
          console.log('❌ Filtering out interaction:', {
            id: interaction.id,
            type: interaction.interaction_type,
            patient_id: interaction.patient_id,
            urgent_care: interaction.urgent_care,
            isValidType,
            hasContent,
            notTestData,
            hasAssessment
            patient_id: interaction.patient_id,
            hasContent,
            notTestData,
            hasAssessment
          });
        }

        if (isValid) {
          console.log('✅ Including interaction:', {
            id: interaction.id,
            type: interaction.interaction_type,
            patient_id: interaction.patient_id,
            urgent_care: interaction.urgent_care,
            severity: interaction.severity
          });
        }

        return isValid;
      })
      .map(interaction => {
        try {
          console.log('✅ Processing valid interaction:', interaction.id);
          const reportData = JSON.parse(interaction.ai_assessment || '{}');
          const conversation = JSON.parse(interaction.full_conversation || '[]');

          // Only create case if it has proper medical report data structure
          if (!reportData.assessmentSummary || !reportData.symptomsReported) {
            console.log('❌ Skipping interaction without proper medical report structure:', interaction.id);
            return null;
          }

          const patientCase = {
            id: interaction.id,
            patientId: interaction.patient_id,
            patientName: `Patient ${interaction.patient_id}`, // Will be updated with real name from patient lookup
            age: 0, // Will be updated with real age from patient lookup
            location: 'Unknown', // Will be updated with real location from patient lookup
            language: 'English', // Default language
            reportData: reportData,
            conversation: conversation.map((msg: any) => ({
              ...msg,
              timestamp: new Date(msg.timestamp)
            })),
            createdAt: new Date(interaction.timestamp),
            status: 'new' as const,
          };

          console.log('✅ Created patient case:', {
            id: patientCase.id,
            patientId: patientCase.patientId,
            urgent: reportData.assessmentSummary.urgentCare,
            severity: reportData.assessmentSummary.severity
          });

          return patientCase;
        } catch (error) {
          console.error('❌ Error converting interaction to case:', error, interaction);
          return null;
        }
      })
      .filter(case_ => case_ !== null) as PatientCase[];
  }

  // Convert PatientCase to database AI interaction format
  private convertCaseToInteraction(patientCase: PatientCase) {
    return {
      id: patientCase.id,
      patient_id: patientCase.patientId,
      interaction_type: 'chat', // Changed from 'assessment' to 'chat' to match filter
      summary: patientCase.reportData.conversationSummary,
      full_conversation: JSON.stringify(patientCase.conversation),
      ai_assessment: JSON.stringify(patientCase.reportData),
      severity: patientCase.reportData.assessmentSummary.severity === 'critical' ? 'Critical' :
               patientCase.reportData.assessmentSummary.severity === 'high' ? 'High' :
               patientCase.reportData.assessmentSummary.severity === 'moderate' ? 'Medium' : 'Low',
      recommendations: JSON.stringify(patientCase.reportData.aiRecommendations),
      urgent_care: patientCase.reportData.assessmentSummary.urgentCare,
    };
  }

  // Notify all listeners of case updates
  private notifyListeners() {
    this.listeners.forEach(listener => listener([...this.cases]));
  }

  // Create a new patient case from medical report
  async createCase(
    patientId: string,
    patientName: string,
    age: number,
    location: string,
    language: string,
    reportData: MedicalReportData,
    conversation: ChatMessage[]
  ): Promise<PatientCase> {
    const newCase: PatientCase = {
      id: this.generateCaseId(),
      patientId,
      patientName,
      age,
      location,
      language,
      reportData,
      conversation: [...conversation],
      createdAt: new Date(),
      status: 'new'
    };

    this.cases.unshift(newCase); // Add to beginning for newest first

    console.log('New patient case created:', newCase);
    console.log('Total cases now:', this.cases.length);
    console.log('Is urgent case?', reportData.assessmentSummary.urgentCare || reportData.assessmentSummary.severity === 'critical' || reportData.assessmentSummary.severity === 'high');
    console.log('Notifying listeners...', this.listeners.length, 'listeners');

    // Save to database
    await this.saveCaseToDatabase(newCase);

    this.notifyListeners();
    return newCase;
  }

  // Get all cases
  getAllCases(): PatientCase[] {
    return [...this.cases];
  }

  // Get urgent cases (for provider dashboard)
  getUrgentCases(): PatientCase[] {
    return this.cases.filter(patientCase =>
      patientCase.reportData.assessmentSummary.urgentCare ||
      patientCase.reportData.assessmentSummary.severity === 'critical' ||
      patientCase.reportData.assessmentSummary.severity === 'high'
    );
  }

  // Get case by ID
  getCaseById(caseId: string): PatientCase | undefined {
    return this.cases.find(patientCase => patientCase.id === caseId);
  }

  // Update case status
  updateCaseStatus(caseId: string, status: PatientCase['status'], reviewedBy?: string): boolean {
    const caseIndex = this.cases.findIndex(patientCase => patientCase.id === caseId);
    if (caseIndex === -1) return false;

    this.cases[caseIndex] = {
      ...this.cases[caseIndex],
      status,
      reviewedBy,
      reviewedAt: status === 'reviewed' ? new Date() : this.cases[caseIndex].reviewedAt
    };

    this.notifyListeners();
    return true;
  }

  // Convert case to provider dashboard format
  convertToProviderFormat(patientCase: PatientCase) {
    const severity = patientCase.reportData.assessmentSummary.severity;
    const timeAgo = this.getTimeAgo(patientCase.createdAt);

    return {
      id: patientCase.patientId,
      caseId: patientCase.id,
      name: patientCase.patientName,
      age: patientCase.age,
      location: patientCase.location,
      symptoms: patientCase.reportData.symptomsReported.primary.join(', '),
      severity: severity,
      timeReported: timeAgo,
      language: patientCase.language,
      lastContact: timeAgo,
      reportData: patientCase.reportData,
      conversation: patientCase.conversation,
      status: patientCase.status,
      reviewedBy: patientCase.reviewedBy,
      reviewedAt: patientCase.reviewedAt
    };
  }

  // Generate unique case ID
  private generateCaseId(): string {
    return `case_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  // Get time ago string
  private getTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  }

  // Clear all cases (for testing)
  async clearAllCases() {
    this.cases = [];

    try {
      await fetch(getAiInteractionsUrl(), {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });
      console.log('✅ Cleared cases from database');
    } catch (error) {
      console.error('❌ Error clearing database:', error);
    }

    this.notifyListeners();
  }

  // Debug method to check current state
  debugState() {
    console.log('=== PatientCaseService Debug State ===');
    console.log('Backend URL:', getBackendUrl());
    console.log('AI Interactions URL:', getAiInteractionsUrl());
    console.log('Total cases:', this.cases.length);
    console.log('Active listeners:', this.listeners.length);
    console.log('Urgent cases:', this.getUrgentCases().length);
    console.log('Cases:', this.cases);

    // Check each case for urgency criteria
    this.cases.forEach((case_, index) => {
      const isUrgent = case_.reportData.assessmentSummary.urgentCare ||
                      case_.reportData.assessmentSummary.severity === 'critical' ||
                      case_.reportData.assessmentSummary.severity === 'high';
      console.log(`Case ${index + 1}: ${case_.patientName} - Urgent: ${isUrgent}`, {
        urgentCare: case_.reportData.assessmentSummary.urgentCare,
        severity: case_.reportData.assessmentSummary.severity,
        priority: case_.reportData.assessmentSummary.priority
      });
    });

    console.log('=====================================');
  }

  // Removed test case creation - only real patient data should be used

  // Get cases by patient ID
  getCasesByPatientId(patientId: string): PatientCase[] {
    return this.cases.filter(patientCase => patientCase.patientId === patientId);
  }

  // Get recent cases (last 24 hours)
  getRecentCases(): PatientCase[] {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    return this.cases.filter(patientCase => patientCase.createdAt > oneDayAgo);
  }

  // Get cases by status
  getCasesByStatus(status: PatientCase['status']): PatientCase[] {
    return this.cases.filter(patientCase => patientCase.status === status);
  }

  // Add a message to existing case conversation
  addMessageToCase(caseId: string, message: ChatMessage): boolean {
    const caseIndex = this.cases.findIndex(patientCase => patientCase.id === caseId);
    if (caseIndex === -1) return false;

    this.cases[caseIndex].conversation.push(message);
    this.notifyListeners();
    return true;
  }

  // Get conversation for a case
  getCaseConversation(caseId: string): ChatMessage[] {
    const case_ = this.getCaseById(caseId);
    return case_?.conversation || [];
  }

  // Export case data (for healthcare providers)
  exportCaseData(caseId: string): string {
    const case_ = this.getCaseById(caseId);
    if (!case_) return '';

    const conversationText = case_.conversation
      .map(msg => `${msg.role.toUpperCase()}: ${msg.content}`)
      .join('\n\n');

    return `
PATIENT CASE REPORT
==================
Case ID: ${case_.id}
Patient: ${case_.patientName} (ID: ${case_.patientId})
Age: ${case_.age}
Location: ${case_.location}
Language: ${case_.language}
Created: ${case_.createdAt.toLocaleString()}
Status: ${case_.status}

ASSESSMENT SUMMARY
==================
Severity: ${case_.reportData.assessmentSummary.severity}
Priority: ${case_.reportData.assessmentSummary.priority}
Urgent Care Required: ${case_.reportData.assessmentSummary.urgentCare ? 'YES' : 'NO'}
Description: ${case_.reportData.assessmentSummary.description}

SYMPTOMS REPORTED
=================
Primary: ${case_.reportData.symptomsReported.primary.join(', ')}
Secondary: ${case_.reportData.symptomsReported.secondary.join(', ')}
Duration: ${case_.reportData.symptomsReported.duration}
Onset: ${case_.reportData.symptomsReported.onset}

AI RECOMMENDATIONS
==================
Immediate: ${case_.reportData.aiRecommendations.immediate.join(', ')}
Short-term: ${case_.reportData.aiRecommendations.shortTerm.join(', ')}
Long-term: ${case_.reportData.aiRecommendations.longTerm.join(', ')}

FOLLOW-UP PLAN
==============
Appointment Needed: ${case_.reportData.followUpPlan.appointmentNeeded ? 'Yes' : 'No'}
Timeframe: ${case_.reportData.followUpPlan.timeframe}
Location: ${case_.reportData.followUpPlan.location}
Next Check-in: ${case_.reportData.followUpPlan.nextCheckIn}

CONVERSATION TRANSCRIPT
=======================
${conversationText}

RISK ASSESSMENT
===============
Risk Factors: ${case_.reportData.riskFactors.join(', ')}
Red Flags: ${case_.reportData.redFlags.join(', ')}

Generated: ${new Date().toLocaleString()}
    `.trim();
  }
}

// Create singleton instance
export const patientCaseService = new PatientCaseService();

// Add debug functions to window for testing
if (typeof window !== 'undefined') {
  (window as any).debugPatientCases = () => patientCaseService.debugState();
  (window as any).clearAllCases = () => {
    patientCaseService.clearAllCases();
    console.log('All cases cleared from database');
  };
}
