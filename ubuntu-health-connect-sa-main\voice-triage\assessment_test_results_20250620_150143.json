{"timestamp": "2025-06-20T15:01:43.869743", "total_tests": 10, "passed": 6, "success_rate": 60.0, "results": [{"scenario": "Common Cold - Low Risk", "passed": true, "risk_match": true, "care_match": true, "actual_risk": "low", "actual_care": "self_care", "urgency_score": 3, "response_time": 2.029651, "details": {"ai_confidence": 0.85, "follow_up_required": false, "immediate_actions": ["Rest and stay hydrated", "Monitor symptoms", "Take appropriate pain relief if needed"], "recommended_care_level": "self_care", "risk_level": "low", "suspected_conditions": [{"condition": "General malaise", "probability": 0.7, "reasoning": "Based on reported symptoms and common presentations"}], "symptoms_analyzed": ["runny nose", "sneezing", "mild fatigue"], "urgency_score": 3}}, {"scenario": "Flu-like Symptoms - Medium Risk", "passed": true, "risk_match": true, "care_match": true, "actual_risk": "medium", "actual_care": "clinic", "urgency_score": 5, "response_time": 2.059923, "details": {"ai_confidence": 0.85, "follow_up_required": true, "immediate_actions": ["Rest and stay hydrated", "Monitor symptoms", "Take appropriate pain relief if needed"], "recommended_care_level": "clinic", "risk_level": "medium", "suspected_conditions": [{"condition": "Viral upper respiratory infection", "probability": 0.7, "reasoning": "Based on reported symptoms and common presentations"}], "symptoms_analyzed": ["fever", "body aches", "headache", "fatigue"], "urgency_score": 5}}, {"scenario": "Severe Infection - High Risk", "passed": true, "risk_match": true, "care_match": true, "actual_risk": "high", "actual_care": "urgent_care", "urgency_score": 7, "response_time": 2.02685, "details": {"ai_confidence": 0.85, "follow_up_required": true, "immediate_actions": ["Rest and stay hydrated", "Monitor symptoms", "Take appropriate pain relief if needed"], "recommended_care_level": "urgent_care", "risk_level": "high", "suspected_conditions": [{"condition": "General malaise", "probability": 0.7, "reasoning": "Based on reported symptoms and common presentations"}], "symptoms_analyzed": ["high fever", "severe headache", "neck stiffness"], "urgency_score": 7}}, {"scenario": "Cardiac Emergency - Critical Risk", "passed": true, "risk_match": true, "care_match": true, "actual_risk": "critical", "actual_care": "emergency", "urgency_score": 9, "response_time": 2.028386, "details": {"ai_confidence": 0.85, "follow_up_required": true, "immediate_actions": ["Rest and stay hydrated", "Monitor symptoms", "Take appropriate pain relief if needed"], "recommended_care_level": "emergency", "risk_level": "critical", "suspected_conditions": [{"condition": "General malaise", "probability": 0.7, "reasoning": "Based on reported symptoms and common presentations"}], "symptoms_analyzed": ["chest pain", "shortness of breath", "sweating"], "urgency_score": 9}}, {"scenario": "Respiratory Emergency - Critical Risk", "passed": true, "risk_match": true, "care_match": true, "actual_risk": "critical", "actual_care": "emergency", "urgency_score": 9, "response_time": 2.063795, "details": {"ai_confidence": 0.85, "follow_up_required": true, "immediate_actions": ["Rest and stay hydrated", "Monitor symptoms", "Take appropriate pain relief if needed"], "recommended_care_level": "emergency", "risk_level": "critical", "suspected_conditions": [{"condition": "General malaise", "probability": 0.7, "reasoning": "Based on reported symptoms and common presentations"}], "symptoms_analyzed": ["difficulty breathing", "blue lips", "wheezing"], "urgency_score": 9}}, {"scenario": "Neurological Emergency - Critical Risk", "passed": false, "risk_match": false, "care_match": false, "actual_risk": "low", "actual_care": "self_care", "urgency_score": 3, "response_time": 2.055371, "details": {"ai_confidence": 0.85, "follow_up_required": false, "immediate_actions": ["Rest and stay hydrated", "Monitor symptoms", "Take appropriate pain relief if needed"], "recommended_care_level": "self_care", "risk_level": "low", "suspected_conditions": [{"condition": "General malaise", "probability": 0.7, "reasoning": "Based on reported symptoms and common presentations"}], "symptoms_analyzed": ["loss of consciousness", "confusion", "slurred speech"], "urgency_score": 3}}, {"scenario": "Gastrointestinal Issue - Medium Risk", "passed": false, "risk_match": false, "care_match": false, "actual_risk": "low", "actual_care": "self_care", "urgency_score": 3, "response_time": 2.181919, "details": {"ai_confidence": 0.85, "follow_up_required": false, "immediate_actions": ["Rest and stay hydrated", "Monitor symptoms", "Take appropriate pain relief if needed"], "recommended_care_level": "self_care", "risk_level": "low", "suspected_conditions": [{"condition": "General malaise", "probability": 0.7, "reasoning": "Based on reported symptoms and common presentations"}], "symptoms_analyzed": ["nausea", "vomiting", "abdominal pain"], "urgency_score": 3}}, {"scenario": "Severe Trauma - Critical Risk", "passed": true, "risk_match": true, "care_match": true, "actual_risk": "critical", "actual_care": "emergency", "urgency_score": 9, "response_time": 2.04022, "details": {"ai_confidence": 0.85, "follow_up_required": true, "immediate_actions": ["Rest and stay hydrated", "Monitor symptoms", "Take appropriate pain relief if needed"], "recommended_care_level": "emergency", "risk_level": "critical", "suspected_conditions": [{"condition": "General malaise", "probability": 0.7, "reasoning": "Based on reported symptoms and common presentations"}], "symptoms_analyzed": ["severe bleeding", "unconscious", "severe pain"], "urgency_score": 9}}, {"scenario": "Mental Health Crisis - High Risk", "passed": false, "risk_match": false, "care_match": false, "actual_risk": "low", "actual_care": "self_care", "urgency_score": 3, "response_time": 2.030109, "details": {"ai_confidence": 0.85, "follow_up_required": false, "immediate_actions": ["Rest and stay hydrated", "Monitor symptoms", "Take appropriate pain relief if needed"], "recommended_care_level": "self_care", "risk_level": "low", "suspected_conditions": [{"condition": "General malaise", "probability": 0.7, "reasoning": "Based on reported symptoms and common presentations"}], "symptoms_analyzed": ["suicidal thoughts", "severe depression", "anxiety"], "urgency_score": 3}}, {"scenario": "Allergic Reaction - High Risk", "passed": false, "risk_match": false, "care_match": false, "actual_risk": "low", "actual_care": "self_care", "urgency_score": 3, "response_time": 2.028959, "details": {"ai_confidence": 0.85, "follow_up_required": false, "immediate_actions": ["Rest and stay hydrated", "Monitor symptoms", "Take appropriate pain relief if needed"], "recommended_care_level": "self_care", "risk_level": "low", "suspected_conditions": [{"condition": "General malaise", "probability": 0.7, "reasoning": "Based on reported symptoms and common presentations"}], "symptoms_analyzed": ["severe allergic reaction", "swelling", "rash"], "urgency_score": 3}}]}