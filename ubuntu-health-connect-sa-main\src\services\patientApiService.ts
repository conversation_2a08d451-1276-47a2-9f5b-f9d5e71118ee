// Patient API Service - Frontend-Backend Integration
// Ensures proper connection between frontend and backend for patient medical records

import { patientRegistrationService, PatientLookupResult } from './patientRegistrationService';
import { healthcareDatabase, DatabasePatient, ChatInteraction } from './healthcareDatabase';
import { aiInteractionsSummaryService } from './aiInteractionsSummaryService';

// Backend API Configuration
const BACKEND_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';

export interface PatientMedicalRecord {
  patient: DatabasePatient;
  aiInteractions: ChatInteraction[];
  aiSummary: string;
  medicalReports: any[];
  lastUpdated: string;
}

export interface PatientSearchRequest {
  query: string;
  searchType: 'name' | 'idNumber' | 'phone' | 'patientId' | 'all';
  providerId: string;
}

export interface PatientDetailsRequest {
  patientId?: string;
  idNumber?: string;
  providerId: string;
}

class PatientApiService {
  private static instance: PatientApiService;
  private backendConnected = false;

  constructor() {
    this.initializeService();
  }

  static getInstance(): PatientApiService {
    if (!PatientApiService.instance) {
      PatientApiService.instance = new PatientApiService();
    }
    return PatientApiService.instance;
  }

  /**
   * Initialize the service and ensure data integrity
   */
  private async initializeService() {
    console.log('🔗 Initializing Patient API Service - Frontend-Backend Integration');

    // Test backend connection
    await this.testBackendConnection();

    // Validate that patient registration service is properly initialized
    const allPatients = patientRegistrationService.getAllRegisteredPatients();
    console.log(`✅ Connected to ${allPatients.length} real patients in the system`);

    // Validate data integrity
    const isValid = patientRegistrationService.validateRealPatientData();
    if (isValid) {
      console.log('✅ All patient data validated - frontend-backend connection established');
    } else {
      console.error('❌ Patient data validation failed - check data integrity');
    }
  }

  /**
   * Test backend connection
   */
  private async testBackendConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${BACKEND_URL}/api/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });

      if (response.ok) {
        console.log(`✅ Backend connected at: ${BACKEND_URL}`);
        this.backendConnected = true;
        return true;
      }
    } catch (error) {
      console.log(`❌ Backend not available at: ${BACKEND_URL}`);
    }

    console.warn('⚠️ Backend not connected - using frontend-only mode');
    this.backendConnected = false;
    return false;
  }

  /**
   * Force reconnect to backend
   */
  async reconnectToBackend(): Promise<boolean> {
    console.log('🔄 Force reconnecting to backend...');
    this.backendConnected = false;
    return await this.testBackendConnection();
  }

  /**
   * Send patient data to backend database
   */
  async sendPatientToBackend(patient: DatabasePatient): Promise<boolean> {
    if (!this.backendConnected) {
      console.log('⚠️ Backend not connected - skipping backend sync');
      return false;
    }

    try {
      const response = await fetch(`${BACKEND_URL}/api/patients`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          patient_id: patient.id,
          first_name: patient.personalInfo.firstName,
          last_name: patient.personalInfo.lastName,
          id_number: patient.personalInfo.idNumber,
          phone_number: patient.personalInfo.phone,
          email: patient.personalInfo.email,
          date_of_birth: patient.personalInfo.dateOfBirth,
          age: patient.personalInfo.age,
          gender: patient.personalInfo.gender,
          address: patient.personalInfo.address,
          emergency_contact_name: patient.personalInfo.emergencyContact.name,
          emergency_contact_phone: patient.personalInfo.emergencyContact.phone,
          emergency_contact_relationship: patient.personalInfo.emergencyContact.relationship,
          medical_history: patient.medicalHistory.map(condition => ({
            condition_name: condition.condition,
            severity: condition.severity,
            status: condition.status,
            diagnosed_date: condition.diagnosedDate,
            notes: condition.notes
          })),
          medications: patient.medications,
          allergies: patient.allergies,
          created_at: patient.createdAt,
          updated_at: patient.updatedAt
        }),
        signal: AbortSignal.timeout(10000)
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Patient ${result.action} in backend database: ${patient.id}`, result);
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error(`❌ Failed to send patient to backend: ${response.status}`, errorData);
        return false;
      }
    } catch (error) {
      console.error('❌ Error sending patient to backend:', error);
      return false;
    }
  }

  /**
   * Get all patients from backend database
   */
  async getAllPatientsFromBackend(searchTerm: string = ''): Promise<DatabasePatient[]> {
    if (!this.backendConnected) {
      console.log('⚠️ Backend not connected - cannot get patients from backend');
      return [];
    }

    try {
      const url = new URL(`${BACKEND_URL}/api/patients`);
      if (searchTerm) {
        url.searchParams.append('search', searchTerm);
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Retrieved ${result.count} patients from backend database`);

        // Convert backend format to frontend format
        return result.patients.map((backendPatient: any) => this.convertBackendToFrontendPatient(backendPatient));
      } else {
        console.error(`❌ Failed to get patients from backend: ${response.status}`);
        return [];
      }
    } catch (error) {
      console.error('❌ Error getting patients from backend:', error);
      return [];
    }
  }

  /**
   * Send AI interaction to backend
   */
  async sendAIInteractionToBackend(patientId: string, interaction: any): Promise<boolean> {
    if (!this.backendConnected) {
      return false;
    }

    try {
      const response = await fetch(`${BACKEND_URL}/api/patients/${patientId}/ai-interactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(interaction),
        signal: AbortSignal.timeout(5000)
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ AI interaction sent to backend: ${result.interaction_id}`);
        return true;
      } else {
        console.error(`❌ Failed to send AI interaction to backend: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.error('❌ Error sending AI interaction to backend:', error);
      return false;
    }
  }

  /**
   * Search for patients - Frontend API endpoint
   */
  async searchPatients(request: PatientSearchRequest): Promise<PatientLookupResult[]> {
    try {
      console.log(`🔍 API: Searching patients for query: "${request.query}"`);
      
      let searchCriteria: any = {};
      
      switch (request.searchType) {
        case 'name':
          searchCriteria = {
            firstName: request.query,
            lastName: request.query
          };
          break;
        case 'idNumber':
          searchCriteria = { idNumber: request.query };
          break;
        case 'phone':
          searchCriteria = { phone: request.query };
          break;
        case 'patientId':
          searchCriteria = { patientId: request.query };
          break;
        case 'all':
        default:
          searchCriteria = {
            firstName: request.query,
            lastName: request.query,
            idNumber: request.query,
            phone: request.query,
            patientId: request.query
          };
          break;
      }
      
      const results = patientRegistrationService.searchPatients(searchCriteria);
      console.log(`✅ API: Found ${results.length} patients matching search criteria`);
      
      return results;
    } catch (error) {
      console.error('❌ API: Error searching patients:', error);
      throw new Error('Failed to search patients');
    }
  }

  /**
   * Get all registered patients - Frontend API endpoint
   */
  async getAllPatients(providerId: string): Promise<PatientLookupResult[]> {
    try {
      console.log(`📋 API: Getting all patients for provider: ${providerId}`);
      
      const patients = patientRegistrationService.getAllRegisteredPatients();
      console.log(`✅ API: Retrieved ${patients.length} real patients`);
      
      return patients;
    } catch (error) {
      console.error('❌ API: Error getting all patients:', error);
      throw new Error('Failed to retrieve patients');
    }
  }

  /**
   * Get complete patient medical record - Frontend API endpoint
   */
  async getPatientMedicalRecord(request: PatientDetailsRequest): Promise<PatientMedicalRecord | null> {
    try {
      console.log(`📄 API: Getting medical record for patient request:`, request);
      
      let patient: DatabasePatient | null = null;
      
      // Get patient by ID or ID number
      if (request.patientId) {
        const patientResult = await healthcareDatabase.getPatientById(request.patientId, request.providerId);
        patient = patientResult;
      } else if (request.idNumber) {
        const patientResult = patientRegistrationService.getPatientByIdNumber(request.idNumber);
        patient = patientResult?.patient || null;
      }
      
      if (!patient) {
        console.warn(`⚠️ API: Patient not found for request:`, request);
        return null;
      }
      
      console.log(`👤 API: Found patient: ${patient.personalInfo.firstName} ${patient.personalInfo.lastName}`);
      
      // Get AI interactions
      const aiInteractions = await healthcareDatabase.getPatientAIInteractions(patient.id, request.providerId);
      console.log(`🤖 API: Retrieved ${aiInteractions.length} AI interactions`);
      
      // Generate AI summary
      const aiSummary = await healthcareDatabase.getAIInteractionsSummary(patient.id, request.providerId);
      console.log(`📊 API: Generated AI interactions summary`);
      
      const medicalRecord: PatientMedicalRecord = {
        patient,
        aiInteractions,
        aiSummary,
        medicalReports: [], // Will be populated with actual medical reports
        lastUpdated: new Date().toISOString()
      };
      
      console.log(`✅ API: Complete medical record prepared for ${patient.personalInfo.firstName} ${patient.personalInfo.lastName}`);
      
      return medicalRecord;
    } catch (error) {
      console.error('❌ API: Error getting patient medical record:', error);
      throw new Error('Failed to retrieve patient medical record');
    }
  }

  /**
   * Get specific patient by SA ID number - Optimized for Nomsa Mthembu lookup
   */
  async getPatientByIdNumber(idNumber: string, providerId: string): Promise<PatientMedicalRecord | null> {
    try {
      console.log(`🆔 API: Looking up patient by SA ID: ${idNumber}`);
      
      // Special handling for Nomsa Mthembu to ensure proper connection
      if (idNumber === '8503155678901') {
        console.log(`👩 API: Retrieving Nomsa Mthembu's medical record`);
      }
      
      const request: PatientDetailsRequest = {
        idNumber,
        providerId
      };
      
      return await this.getPatientMedicalRecord(request);
    } catch (error) {
      console.error(`❌ API: Error getting patient by ID number ${idNumber}:`, error);
      throw new Error('Failed to retrieve patient by ID number');
    }
  }

  /**
   * Validate patient data integrity - Ensure frontend-backend sync
   */
  async validatePatientDataIntegrity(): Promise<boolean> {
    try {
      console.log('🔍 API: Validating patient data integrity...');
      
      // Check if Nomsa Mthembu's data is properly connected
      const nomsaRecord = await this.getPatientByIdNumber('8503155678901', 'PROV001');
      
      if (!nomsaRecord) {
        console.error('❌ API: Nomsa Mthembu record not found - data integrity issue');
        return false;
      }
      
      // Validate Nomsa's medical data
      const nomsa = nomsaRecord.patient;
      const expectedConditions = ['Hypertension (High Blood Pressure)', 'Type 2 Diabetes Mellitus'];
      const actualConditions = nomsa.medicalHistory.map(c => c.condition);
      
      const hasExpectedConditions = expectedConditions.every(condition => 
        actualConditions.some(actual => actual.includes(condition.split(' ')[0]))
      );
      
      if (!hasExpectedConditions) {
        console.error('❌ API: Nomsa Mthembu medical conditions not properly connected');
        return false;
      }
      
      // Validate AI interactions
      if (nomsaRecord.aiInteractions.length === 0) {
        console.error('❌ API: Nomsa Mthembu AI interactions not properly connected');
        return false;
      }
      
      console.log('✅ API: Patient data integrity validated - frontend-backend properly connected');
      return true;
    } catch (error) {
      console.error('❌ API: Error validating patient data integrity:', error);
      return false;
    }
  }

  /**
   * Get patient statistics for dashboard
   */
  async getPatientStatistics(providerId: string): Promise<any> {
    try {
      console.log(`📊 API: Getting patient statistics for provider: ${providerId}`);
      
      const stats = patientRegistrationService.getPatientStatistics();
      const dbStats = healthcareDatabase.getStatistics();
      
      const combinedStats = {
        ...stats,
        ...dbStats,
        lastUpdated: new Date().toISOString()
      };
      
      console.log(`✅ API: Patient statistics retrieved:`, combinedStats);
      
      return combinedStats;
    } catch (error) {
      console.error('❌ API: Error getting patient statistics:', error);
      throw new Error('Failed to retrieve patient statistics');
    }
  }

  /**
   * Test specific patient connection - For debugging
   */
  async testPatientConnection(patientName: string): Promise<boolean> {
    try {
      console.log(`🧪 API: Testing connection for patient: ${patientName}`);

      let testResult = false;

      if (patientName.toLowerCase().includes('nomsa')) {
        const nomsaRecord = await this.getPatientByIdNumber('8503155678901', 'PROV001');
        testResult = nomsaRecord !== null;

        if (testResult) {
          console.log(`✅ API: Nomsa Mthembu connection test PASSED`);
          console.log(`   - Patient ID: ${nomsaRecord!.patient.id}`);
          console.log(`   - Medical conditions: ${nomsaRecord!.patient.medicalHistory.length}`);
          console.log(`   - AI interactions: ${nomsaRecord!.aiInteractions.length}`);
          console.log(`   - Medications: ${nomsaRecord!.patient.medications.length}`);
          console.log(`   - Lab results: ${nomsaRecord!.patient.labResults.length}`);
          console.log(`   - Allergies: ${nomsaRecord!.patient.allergies.length}`);

          // Debug medical conditions
          console.log(`📋 Medical Conditions:`);
          nomsaRecord!.patient.medicalHistory.forEach((condition, index) => {
            console.log(`   ${index + 1}. ${condition.condition} (${condition.severity}) - ${condition.status}`);
          });

          // Debug medications
          console.log(`💊 Medications:`);
          nomsaRecord!.patient.medications.forEach((med, index) => {
            console.log(`   ${index + 1}. ${med.name} ${med.dosage} - ${med.frequency}`);
          });

          // Debug allergies
          console.log(`⚠️ Allergies:`);
          nomsaRecord!.patient.allergies.forEach((allergy, index) => {
            console.log(`   ${index + 1}. ${allergy.allergen} (${allergy.severity}) - ${allergy.reaction}`);
          });

        } else {
          console.error(`❌ API: Nomsa Mthembu connection test FAILED`);
        }
      }

      return testResult;
    } catch (error) {
      console.error(`❌ API: Error testing patient connection for ${patientName}:`, error);
      return false;
    }
  }
}

// Export singleton instance
export const patientApiService = PatientApiService.getInstance();
export default patientApiService;

// Make debugging functions available globally
if (typeof window !== 'undefined') {
  (window as any).debugPatientAPI = () => {
    console.log('=== Patient API Service Debug ===');
    console.log('Backend connected:', (patientApiService as any).backendConnected);
    console.log('Backend URL:', BACKEND_URL);
    console.log('================================');
  };
}
