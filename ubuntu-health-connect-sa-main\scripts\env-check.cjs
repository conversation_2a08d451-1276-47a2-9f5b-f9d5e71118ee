#!/usr/bin/env node
/**
 * Environment Configuration Check
 * Validates and displays current environment configuration
 */

// Load dotenv if available, but don't fail if it's not installed
try {
  require('dotenv').config();
} catch (e) {
  // dotenv not available, continue without it
}

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkEnvVar(name, defaultValue, required = false) {
  const value = process.env[name];
  const hasValue = value !== undefined && value !== '';
  const displayValue = hasValue ? value : defaultValue || 'Not set';
  
  if (required && !hasValue) {
    log(`❌ ${name}: ${displayValue} (REQUIRED)`, 'red');
    return false;
  } else if (hasValue) {
    log(`✅ ${name}: ${displayValue}`, 'green');
    return true;
  } else {
    log(`⚠️ ${name}: ${displayValue} (using default)`, 'yellow');
    return true;
  }
}

function envCheck() {
  log('🔧 Ubuntu Health Connect SA - Environment Configuration', 'bold');
  log('=======================================================', 'blue');
  log('', 'reset');
  
  // Frontend Configuration
  log('📱 Frontend Configuration:', 'cyan');
  log('-------------------------', 'cyan');
  checkEnvVar('VITE_FRONTEND_PORT', '8085');
  checkEnvVar('VITE_BACKEND_PORT', '5001');
  checkEnvVar('VITE_BACKEND_HOST', 'localhost');
  checkEnvVar('VITE_API_BASE_URL', 'http://localhost:5001');
  log('', 'reset');
  
  // Backend Configuration
  log('🔧 Backend Configuration:', 'cyan');
  log('------------------------', 'cyan');
  checkEnvVar('FLASK_PORT', '5001');
  checkEnvVar('FLASK_HOST', '0.0.0.0');
  checkEnvVar('FLASK_ENV', 'development');
  checkEnvVar('FLASK_DEBUG', 'true');
  log('', 'reset');
  
  // Application Configuration
  log('🏥 Application Configuration:', 'cyan');
  log('-----------------------------', 'cyan');
  checkEnvVar('VITE_APP_NAME', 'Ubuntu Health Connect SA');
  checkEnvVar('VITE_APP_VERSION', '1.0.0');
  checkEnvVar('VITE_APP_ENVIRONMENT', 'development');
  log('', 'reset');
  
  // Development Settings
  log('⚙️ Development Settings:', 'cyan');
  log('-----------------------', 'cyan');
  checkEnvVar('AUTO_START_BACKEND', 'true');
  checkEnvVar('AUTO_OPEN_BROWSER', 'true');
  checkEnvVar('ENABLE_HOT_RELOAD', 'true');
  checkEnvVar('ENABLE_CONSOLE_LOGS', 'true');
  log('', 'reset');
  
  // Optional Configuration
  log('🔑 Optional Configuration:', 'cyan');
  log('-------------------------', 'cyan');
  const hasOpenAI = checkEnvVar('VITE_OPENAI_API_KEY', 'Not configured');
  checkEnvVar('OPENAI_MODEL', 'gpt-4o-mini');
  log('', 'reset');
  
  // Computed URLs
  const frontendPort = process.env.VITE_FRONTEND_PORT || '8085';
  const backendPort = process.env.VITE_BACKEND_PORT || '5001';
  const backendHost = process.env.VITE_BACKEND_HOST || 'localhost';
  
  log('🌐 Computed URLs:', 'cyan');
  log('----------------', 'cyan');
  log(`Frontend URL: http://localhost:${frontendPort}`, 'blue');
  log(`Backend URL: http://${backendHost}:${backendPort}`, 'blue');
  log(`Health Check: http://${backendHost}:${backendPort}/health`, 'blue');
  log(`AI Interactions: http://${backendHost}:${backendPort}/api/ai-interactions`, 'blue');
  log('', 'reset');
  
  // Validation Summary
  log('📋 Configuration Status:', 'bold');
  log('=======================', 'blue');
  
  if (frontendPort === backendPort) {
    log('❌ ERROR: Frontend and backend ports are the same!', 'red');
    log('   Please set different ports in your .env file', 'red');
    return false;
  }
  
  if (!hasOpenAI) {
    log('⚠️ WARNING: OpenAI API key not configured', 'yellow');
    log('   AI features may not work properly', 'yellow');
    log('   Add VITE_OPENAI_API_KEY to your .env file', 'yellow');
  }
  
  log('✅ Configuration is valid!', 'green');
  log('', 'reset');
  
  // Quick Start Commands
  log('🚀 Quick Start Commands:', 'bold');
  log('=======================', 'blue');
  log('Start everything:     npm run dev', 'green');
  log('Start backend only:   npm run start:backend:only', 'green');
  log('Start frontend only:  npm run start:frontend:only', 'green');
  log('Check health:         npm run health:check', 'green');
  log('Check environment:    npm run env:check', 'green');
  log('', 'reset');
  
  return true;
}

// Run environment check
const isValid = envCheck();
process.exit(isValid ? 0 : 1);
