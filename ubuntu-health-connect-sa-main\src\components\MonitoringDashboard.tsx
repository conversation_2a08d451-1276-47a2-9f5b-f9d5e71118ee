import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { intelligentMonitoringAgent, MonitoringSession, PatientResponse } from '@/services/intelligentMonitoringAgent';
import { 
  Activity, 
  Clock, 
  MessageSquare, 
  AlertTriangle, 
  CheckCircle, 
  Pause, 
  Play,
  FileText,
  TrendingUp,
  TrendingDown,
  Minus,
  Bot,
  User
} from 'lucide-react';

interface MonitoringDashboardProps {
  onClose: () => void;
}

export const MonitoringDashboard: React.FC<MonitoringDashboardProps> = ({ onClose }) => {
  const [activeSessions, setActiveSessions] = useState<MonitoringSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<MonitoringSession | null>(null);
  const [activeTab, setActiveTab] = useState<'active' | 'completed'>('active');

  useEffect(() => {
    // Refresh data every 30 seconds
    const refreshData = () => {
      setActiveSessions(intelligentMonitoringAgent.getActiveSessions());
    };

    refreshData();
    const interval = setInterval(refreshData, 30000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'monitoring': return 'bg-green-500';
      case 'paused': return 'bg-yellow-500';
      case 'completed': return 'bg-blue-500';
      case 'doctor_intervened': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'urgent': return 'bg-red-500';
      case 'concerning': return 'bg-yellow-500';
      case 'stable': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'deteriorating': return <TrendingDown className="w-4 h-4 text-red-600" />;
      case 'stable': return <Minus className="w-4 h-4 text-blue-600" />;
      default: return <Minus className="w-4 h-4 text-gray-600" />;
    }
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const hours = Math.floor(minutes / 60);
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString();
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Bot className="w-6 h-6 text-blue-500" />
            Ubuntu Healthcare Monitoring Dashboard
            <Badge className="bg-blue-500 text-white">
              {activeSessions.length} Active Sessions
            </Badge>
          </DialogTitle>
        </DialogHeader>

        {/* Tab Navigation */}
        <div className="flex gap-2 mb-6">
          <Button
            variant={activeTab === 'active' ? 'default' : 'outline'}
            onClick={() => setActiveTab('active')}
            className="flex items-center gap-2"
          >
            <Activity className="w-4 h-4" />
            Active Monitoring ({activeSessions.length})
          </Button>
          <Button
            variant={activeTab === 'completed' ? 'default' : 'outline'}
            onClick={() => setActiveTab('completed')}
            className="flex items-center gap-2"
          >
            <FileText className="w-4 h-4" />
            Completed Sessions
          </Button>
        </div>

        {/* Active Sessions Tab */}
        {activeTab === 'active' && (
          <div className="space-y-4">
            {activeSessions.length === 0 ? (
              <Card className="p-8 text-center">
                <div className="flex flex-col items-center gap-4">
                  <Bot className="w-12 h-12 text-gray-400" />
                  <h3 className="text-lg font-bold text-gray-600">No Active Monitoring Sessions</h3>
                  <p className="text-gray-500">AI monitoring will automatically start when urgent cases are created.</p>
                </div>
              </Card>
            ) : (
              <div className="grid gap-4">
                {activeSessions.map((session) => (
                  <Card key={session.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            <Activity className="w-5 h-5 text-blue-500" />
                            <span className="font-bold">{session.patientName}</span>
                            <span className="text-sm text-gray-600">({session.patientId})</span>
                          </div>
                          <Badge className={`${getStatusColor(session.status)} text-white`}>
                            {session.status.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Clock className="w-4 h-4" />
                          Started: {formatTime(session.startedAt)}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="flex items-center gap-2">
                          <MessageSquare className="w-4 h-4 text-green-500" />
                          <span className="text-sm">
                            <strong>{session.checkInCount}</strong> check-ins sent
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-blue-500" />
                          <span className="text-sm">
                            <strong>{session.patientResponses.length}</strong> responses received
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-purple-500" />
                          <span className="text-sm">
                            Duration: <strong>{formatDuration(new Date().getTime() - session.startedAt.getTime())}</strong>
                          </span>
                        </div>
                      </div>

                      {/* Recent Responses */}
                      {session.patientResponses.length > 0 && (
                        <div className="mb-4">
                          <h4 className="font-bold text-sm mb-2">Recent Responses:</h4>
                          <div className="space-y-2 max-h-32 overflow-y-auto">
                            {session.patientResponses.slice(-3).map((response, index) => (
                              <div key={index} className="flex items-start gap-2 p-2 bg-gray-50 rounded">
                                <Badge className={`${getSeverityColor(response.sentiment)} text-white text-xs`}>
                                  {response.sentiment}
                                </Badge>
                                <div className="flex-1">
                                  <p className="text-sm">{response.content}</p>
                                  <p className="text-xs text-gray-500">{formatTime(response.timestamp)}</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedSession(session)}
                        >
                          View Details
                        </Button>
                        {session.status === 'active' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              intelligentMonitoringAgent.pauseMonitoring(session.id);
                              setActiveSessions(intelligentMonitoringAgent.getActiveSessions());
                            }}
                          >
                            <Pause className="w-4 h-4 mr-1" />
                            Pause
                          </Button>
                        )}
                        {session.status === 'paused' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              intelligentMonitoringAgent.resumeMonitoring(session.id);
                              setActiveSessions(intelligentMonitoringAgent.getActiveSessions());
                            }}
                          >
                            <Play className="w-4 h-4 mr-1" />
                            Resume
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 border-red-300 hover:bg-red-50"
                          onClick={() => {
                            intelligentMonitoringAgent.stopMonitoringForCase(session.caseId, 'doctor_intervened');
                            setActiveSessions(intelligentMonitoringAgent.getActiveSessions());
                          }}
                        >
                          Doctor Intervention
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Completed Sessions Tab */}
        {activeTab === 'completed' && (
          <div className="space-y-4">
            {intelligentMonitoringAgent.getAllSessions().filter(s => s.status === 'completed' || s.status === 'doctor_intervened').length === 0 ? (
              <Card className="p-8 text-center">
                <div className="flex flex-col items-center gap-4">
                  <FileText className="w-12 h-12 text-gray-400" />
                  <h3 className="text-lg font-bold text-gray-600">No Completed Sessions</h3>
                  <p className="text-gray-500">Completed monitoring sessions will appear here.</p>
                </div>
              </Card>
            ) : (
              <div className="grid gap-4">
                {intelligentMonitoringAgent.getAllSessions()
                  .filter(session => session.status === 'completed' || session.status === 'doctor_intervened')
                  .map((session) => (
                  <Card key={session.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <FileText className="w-5 h-5 text-blue-500" />
                          <span className="font-bold">{session.patientName}</span>
                          <Badge className={`${getStatusColor(session.status)} text-white`}>
                            {session.status.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-500">
                          {session.startedAt.toLocaleString()}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-purple-500" />
                          <span className="text-sm">
                            Duration: <strong>{formatDuration(new Date().getTime() - session.startedAt.getTime())}</strong>
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MessageSquare className="w-4 h-4 text-green-500" />
                          <span className="text-sm">
                            <strong>{session.checkInCount}</strong> check-ins
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-blue-500" />
                          <span className="text-sm">
                            <strong>{session.patientResponses.length}</strong> responses
                          </span>
                        </div>
                      </div>

                      <div className="mb-4">
                        <h4 className="font-bold text-sm mb-2">Medical Context:</h4>
                        <div className="text-sm bg-blue-50 p-3 rounded border-l-4 border-blue-500">
                          <p><strong>Symptoms:</strong> {session.medicalContext.symptoms.join(', ')}</p>
                          <p><strong>Severity:</strong> {session.medicalContext.severity}</p>
                          <p><strong>Urgent Care:</strong> {session.medicalContext.urgentCare ? 'Yes' : 'No'}</p>
                        </div>
                      </div>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setSelectedSession(session)}
                      >
                        View Session Details
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Session Detail Modal */}
        {selectedSession && (
          <Dialog open={true} onOpenChange={() => setSelectedSession(null)}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  Monitoring Session Details - Patient {selectedSession.patientId}
                </DialogTitle>
              </DialogHeader>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-bold mb-2">Session Info</h4>
                    <div className="space-y-1 text-sm">
                      <p><strong>Session ID:</strong> {selectedSession.id}</p>
                      <p><strong>Case ID:</strong> {selectedSession.caseId}</p>
                      <p><strong>Status:</strong> {selectedSession.status}</p>
                      <p><strong>Started:</strong> {selectedSession.startedAt.toLocaleString()}</p>
                      <p><strong>Duration:</strong> {formatDuration(new Date().getTime() - selectedSession.startedAt.getTime())}</p>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-bold mb-2">Statistics</h4>
                    <div className="space-y-1 text-sm">
                      <p><strong>Check-ins Sent:</strong> {selectedSession.checkInCount}</p>
                      <p><strong>Responses Received:</strong> {selectedSession.patientResponses.length}</p>
                      <p><strong>Response Rate:</strong> {selectedSession.checkInCount > 0 ? Math.round((selectedSession.patientResponses.length / selectedSession.checkInCount) * 100) : 0}%</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-bold mb-2">Conversation History</h4>
                  <div className="space-y-3 max-h-96 overflow-y-auto border rounded p-4">
                    {selectedSession.patientResponses.map((response, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-start gap-2">
                          <User className="w-4 h-4 text-green-500 mt-1" />
                          <div className="bg-green-50 p-2 rounded flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <Badge className={`${getSeverityColor(response.sentiment)} text-white text-xs`}>
                                {response.sentiment}
                              </Badge>
                              <span className="text-xs text-gray-500">
                                {response.timestamp.toLocaleString()}
                              </span>
                            </div>
                            <p className="text-sm">{response.content}</p>
                            {response.analysis && (
                              <p className="text-xs text-gray-600 mt-1 italic">{response.analysis}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </DialogContent>
    </Dialog>
  );
};
