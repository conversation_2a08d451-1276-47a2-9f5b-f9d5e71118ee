/**
 * Simple Configuration - Fallback for environment issues
 */

// Simple configuration with hardcoded fallbacks
export const SIMPLE_CONFIG = {
  // Frontend Configuration
  FRONTEND_PORT: 8089,
  FRONTEND_URL: 'http://localhost:8089',
  
  // Backend Configuration
  BACKEND_PORT: 5000,
  BACKEND_HOST: 'localhost',
  BACKEND_URL: 'http://localhost:5000',

  // API Endpoints
  API_BASE_URL: 'http://localhost:5000',
  HEALTH_ENDPOINT: '/health',
  PATIENTS_ENDPOINT: '/api/patients',
  AI_INTERACTIONS_ENDPOINT: '/api/ai-interactions',
  
  // Full API URLs
  HEALTH_URL: 'http://localhost:5000/health',
  PATIENTS_URL: 'http://localhost:5000/api/patients',
  AI_INTERACTIONS_URL: 'http://localhost:5000/api/ai-interactions',
};

// Simple functions to get URLs
export const getBackendUrl = () => SIMPLE_CONFIG.BACKEND_URL;
export const getApiUrl = (endpoint?: string) => {
  return endpoint ? `${SIMPLE_CONFIG.API_BASE_URL}${endpoint}` : SIMPLE_CONFIG.API_BASE_URL;
};
export const getHealthUrl = () => SIMPLE_CONFIG.HEALTH_URL;
export const getPatientsUrl = () => SIMPLE_CONFIG.PATIENTS_URL;
export const getAiInteractionsUrl = () => SIMPLE_CONFIG.AI_INTERACTIONS_URL;

// Health check function
export const checkBackendHealth = async (): Promise<boolean> => {
  try {
    const response = await fetch(getHealthUrl(), {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });
    return response.ok;
  } catch (error) {
    console.warn('Backend health check failed:', error);
    return false;
  }
};

console.log('🔧 Simple Config Loaded:', {
  frontend: SIMPLE_CONFIG.FRONTEND_URL,
  backend: SIMPLE_CONFIG.BACKEND_URL,
  api: SIMPLE_CONFIG.AI_INTERACTIONS_URL
});
