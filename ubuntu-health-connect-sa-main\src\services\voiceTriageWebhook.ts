// Voice Triage Webhook Handler - Receives data from voice triage system
// This service handles incoming webhook data from the voice triage backend

import { voiceTriageIntegrationService } from './voiceTriageIntegration';

export interface VoiceTriageWebhookData {
  event: 'call_started' | 'transcript_added' | 'call_completed' | 'assessment_generated';
  callSid: string;
  phoneNumber: string;
  patientId?: string;
  language?: string;
  timestamp: string;
  data?: {
    transcript?: {
      speaker: 'patient' | 'ai';
      text: string;
      confidence: number;
      timestamp: number;
    };
    assessment?: {
      symptoms: string[];
      severity: 'Low' | 'Medium' | 'High' | 'Critical';
      recommendations: string[];
      triageDecision: 'Self-Care' | 'Schedule Appointment' | 'Urgent Care' | 'Emergency';
      riskLevel: string;
      urgencyScore: number;
    };
    callDuration?: number;
    callStatus?: 'initiated' | 'in_progress' | 'completed' | 'failed';
  };
}

class VoiceTriageWebhookService {
  private webhookEndpoint = '/api/voice-triage/webhook';

  constructor() {
    console.log('🔗 VoiceTriageWebhookService initialized');
    this.setupMockWebhookListener();
  }

  /**
   * Process incoming webhook data from voice triage system
   */
  async processWebhook(webhookData: VoiceTriageWebhookData): Promise<boolean> {
    try {
      console.log(`📞 Processing voice triage webhook: ${webhookData.event} for call ${webhookData.callSid}`);

      switch (webhookData.event) {
        case 'call_started':
          return await this.handleCallStarted(webhookData);
        
        case 'transcript_added':
          return await this.handleTranscriptAdded(webhookData);
        
        case 'assessment_generated':
          return await this.handleAssessmentGenerated(webhookData);
        
        case 'call_completed':
          return await this.handleCallCompleted(webhookData);
        
        default:
          console.warn(`Unknown webhook event: ${webhookData.event}`);
          return false;
      }
    } catch (error) {
      console.error('Error processing voice triage webhook:', error);
      return false;
    }
  }

  /**
   * Handle call started event
   */
  private async handleCallStarted(webhookData: VoiceTriageWebhookData): Promise<boolean> {
    const callId = await voiceTriageIntegrationService.processVoiceTriageData({
      callSid: webhookData.callSid,
      phoneNumber: webhookData.phoneNumber,
      language: webhookData.language || 'en',
      patientId: webhookData.patientId,
      status: 'initiated'
    });

    return callId !== null;
  }

  /**
   * Handle transcript added event
   */
  private async handleTranscriptAdded(webhookData: VoiceTriageWebhookData): Promise<boolean> {
    if (!webhookData.data?.transcript) {
      console.warn('No transcript data in webhook');
      return false;
    }

    const callId = await voiceTriageIntegrationService.processVoiceTriageData({
      callSid: webhookData.callSid,
      phoneNumber: webhookData.phoneNumber,
      transcripts: [webhookData.data.transcript],
      status: 'in_progress'
    });

    return callId !== null;
  }

  /**
   * Handle assessment generated event
   */
  private async handleAssessmentGenerated(webhookData: VoiceTriageWebhookData): Promise<boolean> {
    if (!webhookData.data?.assessment) {
      console.warn('No assessment data in webhook');
      return false;
    }

    const assessment = webhookData.data.assessment;
    const callId = await voiceTriageIntegrationService.processVoiceTriageData({
      callSid: webhookData.callSid,
      phoneNumber: webhookData.phoneNumber,
      aiAssessment: {
        symptoms: assessment.symptoms,
        severity: assessment.severity,
        recommendations: assessment.recommendations,
        triageDecision: assessment.triageDecision
      }
    });

    return callId !== null;
  }

  /**
   * Handle call completed event
   */
  private async handleCallCompleted(webhookData: VoiceTriageWebhookData): Promise<boolean> {
    const callId = await voiceTriageIntegrationService.processVoiceTriageData({
      callSid: webhookData.callSid,
      phoneNumber: webhookData.phoneNumber,
      duration: webhookData.data?.callDuration,
      status: 'completed'
    });

    return callId !== null;
  }

  /**
   * Setup mock webhook listener for development/testing
   * In production, this would be replaced with actual HTTP endpoint
   */
  private setupMockWebhookListener(): void {
    // Make webhook handler available globally for testing
    (window as any).simulateVoiceTriageWebhook = (webhookData: VoiceTriageWebhookData) => {
      console.log('🧪 Simulating voice triage webhook:', webhookData);
      return this.processWebhook(webhookData);
    };

    // Simulate some sample voice triage calls for demonstration
    setTimeout(() => {
      this.simulateSampleVoiceCalls();
    }, 5000);
  }

  /**
   * Simulate sample voice triage calls for demonstration
   */
  private async simulateSampleVoiceCalls(): Promise<void> {
    console.log('🧪 Simulating sample voice triage calls...');

    // Sample call 1: Headache consultation
    const call1Sid = `AT_${Date.now()}_1`;
    
    // Call started
    await this.processWebhook({
      event: 'call_started',
      callSid: call1Sid,
      phoneNumber: '+27123456789',
      patientId: '8501015800087',
      language: 'en',
      timestamp: new Date().toISOString()
    });

    // Patient speaks
    setTimeout(async () => {
      await this.processWebhook({
        event: 'transcript_added',
        callSid: call1Sid,
        phoneNumber: '+27123456789',
        timestamp: new Date().toISOString(),
        data: {
          transcript: {
            speaker: 'patient',
            text: 'Hello, I have been having severe headaches for the past two days. The pain is really intense and I feel dizzy.',
            confidence: 0.95,
            timestamp: 15
          }
        }
      });
    }, 2000);

    // AI responds
    setTimeout(async () => {
      await this.processWebhook({
        event: 'transcript_added',
        callSid: call1Sid,
        phoneNumber: '+27123456789',
        timestamp: new Date().toISOString(),
        data: {
          transcript: {
            speaker: 'ai',
            text: 'I understand you are experiencing severe headaches with dizziness. Can you tell me if you have any nausea or vision changes?',
            confidence: 1.0,
            timestamp: 25
          }
        }
      });
    }, 4000);

    // Patient responds
    setTimeout(async () => {
      await this.processWebhook({
        event: 'transcript_added',
        callSid: call1Sid,
        phoneNumber: '+27123456789',
        timestamp: new Date().toISOString(),
        data: {
          transcript: {
            speaker: 'patient',
            text: 'Yes, I do feel nauseous and my vision seems a bit blurry. I am quite worried.',
            confidence: 0.92,
            timestamp: 45
          }
        }
      });
    }, 6000);

    // AI assessment generated
    setTimeout(async () => {
      await this.processWebhook({
        event: 'assessment_generated',
        callSid: call1Sid,
        phoneNumber: '+27123456789',
        timestamp: new Date().toISOString(),
        data: {
          assessment: {
            symptoms: ['Severe headache', 'Dizziness', 'Nausea', 'Blurred vision'],
            severity: 'High',
            recommendations: [
              'Seek immediate medical attention',
              'Monitor blood pressure',
              'Avoid driving or operating machinery'
            ],
            triageDecision: 'Urgent Care',
            riskLevel: 'high',
            urgencyScore: 8
          }
        }
      });
    }, 8000);

    // Call completed
    setTimeout(async () => {
      await this.processWebhook({
        event: 'call_completed',
        callSid: call1Sid,
        phoneNumber: '+27123456789',
        timestamp: new Date().toISOString(),
        data: {
          callDuration: 120, // 2 minutes
          callStatus: 'completed'
        }
      });
    }, 10000);

    // Sample call 2: Routine check-up inquiry (in isiZulu)
    setTimeout(async () => {
      const call2Sid = `AT_${Date.now()}_2`;
      
      await this.processWebhook({
        event: 'call_started',
        callSid: call2Sid,
        phoneNumber: '+27987654321',
        language: 'zu',
        timestamp: new Date().toISOString()
      });

      setTimeout(async () => {
        await this.processWebhook({
          event: 'transcript_added',
          callSid: call2Sid,
          phoneNumber: '+27987654321',
          timestamp: new Date().toISOString(),
          data: {
            transcript: {
              speaker: 'patient',
              text: 'Sawubona, ngifuna ukubuza ngokuhlolwa kwezempilo okuvamile. Angikabi nankinga kodwa ngifuna ukuqinisekisa ukuthi konke kulungile.',
              confidence: 0.88,
              timestamp: 10
            }
          }
        });
      }, 2000);

      setTimeout(async () => {
        await this.processWebhook({
          event: 'assessment_generated',
          callSid: call2Sid,
          phoneNumber: '+27987654321',
          timestamp: new Date().toISOString(),
          data: {
            assessment: {
              symptoms: ['General health inquiry'],
              severity: 'Low',
              recommendations: [
                'Schedule routine health check-up',
                'Continue healthy lifestyle practices'
              ],
              triageDecision: 'Schedule Appointment',
              riskLevel: 'low',
              urgencyScore: 2
            }
          }
        });
      }, 4000);

      setTimeout(async () => {
        await this.processWebhook({
          event: 'call_completed',
          callSid: call2Sid,
          phoneNumber: '+27987654321',
          timestamp: new Date().toISOString(),
          data: {
            callDuration: 90,
            callStatus: 'completed'
          }
        });
      }, 6000);
    }, 15000);
  }
}

export const voiceTriageWebhookService = new VoiceTriageWebhookService();
export default voiceTriageWebhookService;
