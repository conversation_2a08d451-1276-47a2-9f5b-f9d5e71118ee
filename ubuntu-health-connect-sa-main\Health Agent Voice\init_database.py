#!/usr/bin/env python3
"""
Database Initialization Script for Ubuntu Health Connect SA
Creates and initializes the SQLite database with sample data
"""

import os
import sys
from datetime import datetime, timedelta

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def init_database():
    """Initialize database with schema and sample data"""
    print("🗄️ UBUNTU HEALTH DATABASE INITIALIZATION")
    print("=" * 50)
    
    try:
        # Initialize database manager
        print("📝 Creating database manager...")
        db = DatabaseManager()
        
        # Test connection
        if not db.test_connection():
            print("❌ Database connection failed")
            return False
        
        print("✅ Database connection successful")
        
        # Get initial stats
        initial_stats = db.get_database_stats()
        print(f"📊 Initial database stats: {initial_stats}")
        
        # Add sample healthcare provider (if not exists)
        print("\n👨‍⚕️ Setting up healthcare providers...")
        
        # Add sample patients for testing
        print("\n👥 Adding sample patients...")
        sample_patients = [
            {
                'id': 'PAT_SAMPLE_001',
                'first_name': 'Sipho',
                'last_name': 'Mthembu',
                'id_number': '8501015800087',
                'phone_number': '+27821234567',
                'email': '<EMAIL>',
                'date_of_birth': '1985-01-01',
                'age': 39,
                'gender': 'Male',
                'address': 'Khayelitsha, Cape Town, Western Cape',
                'emergency_contact_name': 'Nomsa Mthembu',
                'emergency_contact_phone': '+27821234568',
                'emergency_contact_relationship': 'Wife'
            },
            {
                'id': 'PAT_SAMPLE_002',
                'first_name': 'Thandiwe',
                'last_name': 'Ndlovu',
                'id_number': '9203125800088',
                'phone_number': '+27821234569',
                'email': '<EMAIL>',
                'date_of_birth': '1992-03-12',
                'age': 32,
                'gender': 'Female',
                'address': 'Soweto, Johannesburg, Gauteng',
                'emergency_contact_name': 'Mandla Ndlovu',
                'emergency_contact_phone': '+27821234570',
                'emergency_contact_relationship': 'Brother'
            },
            {
                'id': 'PAT_SAMPLE_003',
                'first_name': 'Pieter',
                'last_name': 'van der Merwe',
                'id_number': '7809155800089',
                'phone_number': '+27821234571',
                'email': '<EMAIL>',
                'date_of_birth': '1978-09-15',
                'age': 46,
                'gender': 'Male',
                'address': 'Stellenbosch, Western Cape',
                'emergency_contact_name': 'Susan van der Merwe',
                'emergency_contact_phone': '+27821234572',
                'emergency_contact_relationship': 'Wife'
            }
        ]
        
        for patient in sample_patients:
            success = db.create_patient(patient)
            if success:
                print(f"✅ Created patient: {patient['first_name']} {patient['last_name']}")
                
                # Add sample medical history
                if patient['id'] == 'PAT_SAMPLE_001':
                    db.add_medical_condition(patient['id'], {
                        'condition_name': 'Hypertension',
                        'severity': 'Moderate',
                        'status': 'Under Treatment',
                        'diagnosed_date': '2020-05-15',
                        'notes': 'Well controlled with medication'
                    })
                    
                elif patient['id'] == 'PAT_SAMPLE_002':
                    db.add_medical_condition(patient['id'], {
                        'condition_name': 'Diabetes Type 2',
                        'severity': 'Mild',
                        'status': 'Active',
                        'diagnosed_date': '2021-08-20',
                        'notes': 'Diet controlled, regular monitoring'
                    })
                    
            else:
                print(f"⚠️ Patient may already exist: {patient['first_name']} {patient['last_name']}")
        
        # Add sample AI interactions
        print("\n🤖 Adding sample AI interactions...")
        sample_interactions = [
            {
                'id': 'AI_INT_001',
                'patient_id': 'PAT_SAMPLE_001',
                'interaction_type': 'chat',
                'summary': 'Patient reported headache and dizziness',
                'full_conversation': 'Patient: I have been having headaches\nAI: Can you describe the pain?\nPatient: It is a throbbing pain',
                'ai_assessment': 'Possible hypertension-related symptoms',
                'severity': 'Moderate',
                'recommendations': 'Check blood pressure, consider medication adjustment',
                'urgent_care': False
            },
            {
                'id': 'AI_INT_002',
                'patient_id': 'PAT_SAMPLE_002',
                'interaction_type': 'assessment',
                'summary': 'Routine diabetes check-in',
                'full_conversation': 'AI: How are your blood sugar levels?\nPatient: They have been stable',
                'ai_assessment': 'Diabetes well controlled',
                'severity': 'Low',
                'recommendations': 'Continue current management plan',
                'urgent_care': False
            }
        ]
        
        for interaction in sample_interactions:
            success = db.create_ai_interaction(interaction)
            if success:
                print(f"✅ Created AI interaction: {interaction['id']}")
            else:
                print(f"⚠️ AI interaction may already exist: {interaction['id']}")
        
        # Get final stats
        final_stats = db.get_database_stats()
        print(f"\n📊 Final database stats: {final_stats}")
        
        print("\n🎉 Database initialization completed successfully!")
        print("   The database is ready for use with sample data.")
        
        return True
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def reset_database():
    """Reset database by removing the database file"""
    print("🗑️ RESETTING DATABASE")
    print("=" * 30)
    
    db_path = "database/ubuntu_health.db"
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"✅ Removed existing database: {db_path}")
    else:
        print(f"ℹ️ No existing database found: {db_path}")
    
    # Reinitialize
    return init_database()

def main():
    """Main function"""
    print("🏥 Ubuntu Health Connect SA - Database Initialization")
    print(f"   Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    if len(sys.argv) > 1 and sys.argv[1] == 'reset':
        success = reset_database()
    else:
        success = init_database()
    
    if success:
        print("\n✅ Database is ready!")
        print("   You can now start the backend server with: python start_backend_api.py")
    else:
        print("\n❌ Database initialization failed!")
        print("   Please check the error messages above.")
    
    return success

if __name__ == '__main__':
    main()
