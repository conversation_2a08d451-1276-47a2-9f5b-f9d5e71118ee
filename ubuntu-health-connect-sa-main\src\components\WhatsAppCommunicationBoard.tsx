import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  MessageCircle,
  Send,
  Phone,
  User,
  Clock,
  CheckCircle,
  AlertCircle,
  Pill,
  Heart,
  Calendar,
  FileText,
  Search,
  Filter,
  MoreVertical
} from 'lucide-react';
import { whatsappApi, type Patient, type Message } from '@/services/whatsappApi';

// Interfaces are now imported from the API service

const WhatsAppCommunicationBoard = () => {
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'urgent' | 'unread'>('all');
  const [loading, setLoading] = useState(false);
  const [backendStatus, setBackendStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Load patients on component mount and set up test patient
  useEffect(() => {
    initializeWhatsAppChat();
  }, []);

  const initializeWhatsAppChat = async () => {
    try {
      setLoading(true);
      setBackendStatus('checking');

      // Check backend status first
      const stats = await whatsappApi.getDashboardStats();
      if (stats.openai_status || stats.whatsapp_status) {
        setBackendStatus('connected');
      } else {
        setBackendStatus('disconnected');
      }

      // First, set up your WhatsApp number as a test patient
      await whatsappApi.setupTestPatient();

      // Then load all patients
      const patientsData = await whatsappApi.getPatients();
      setPatients(patientsData);

      // Auto-select your WhatsApp number for testing
      const yourPatient = patientsData.find(p => p.phone === '+27761346606');
      if (yourPatient) {
        setSelectedPatient(yourPatient);
        loadMessages(yourPatient.id);
      }
    } catch (error) {
      console.error('Error initializing WhatsApp chat:', error);
      setBackendStatus('disconnected');
    } finally {
      setLoading(false);
    }
  };

  const loadPatients = async () => {
    try {
      setLoading(true);
      const patientsData = await whatsappApi.getPatients();
      setPatients(patientsData);
    } catch (error) {
      console.error('Error loading patients:', error);
    } finally {
      setLoading(false);
    }
  };

  // Messages are now loaded from the API

  useEffect(() => {
    if (selectedPatient) {
      loadMessages(selectedPatient.id);
    }
  }, [selectedPatient]);

  const loadMessages = async (patientId: string) => {
    try {
      setLoading(true);
      const messagesData = await whatsappApi.getMessages(patientId);
      setMessages(messagesData);
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedPatient) return;

    try {
      setLoading(true);

      // Optimistically add message to UI
      const tempMessage: Message = {
        id: Date.now().toString(),
        patientId: selectedPatient.id,
        sender: 'doctor',
        content: newMessage,
        timestamp: new Date().toISOString(),
        type: 'text',
        status: 'sending'
      };

      setMessages(prev => [...prev, tempMessage]);
      const messageToSend = newMessage;
      setNewMessage('');

      // Send message via WhatsApp API
      console.log(`📱 Sending WhatsApp message to ${selectedPatient.phone}: "${messageToSend}"`);
      const success = await whatsappApi.sendDoctorMessage(selectedPatient.phone, messageToSend);

      if (success) {
        // Update message status to sent
        setMessages(prev =>
          prev.map(msg =>
            msg.id === tempMessage.id
              ? { ...msg, status: 'sent' as const }
              : msg
          )
        );

        // Show success notification
        alert(`✅ WhatsApp message sent successfully to ${selectedPatient.phone}!\n\nMessage: "${messageToSend}"\n\nCheck your WhatsApp for the message!`);
      } else {
        // Update message status to failed if API call failed
        setMessages(prev =>
          prev.map(msg =>
            msg.id === tempMessage.id
              ? { ...msg, status: 'failed' as const }
              : msg
          )
        );
      }
    } catch (error) {
      console.error('Error sending message:', error);
      alert(`❌ Failed to send WhatsApp message: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const sendMedicationReminder = async (medicationName: string) => {
    if (!selectedPatient) return;

    try {
      setLoading(true);

      // Optimistically add message to UI
      const tempMessage: Message = {
        id: Date.now().toString(),
        patientId: selectedPatient.id,
        sender: 'system',
        content: `💊 Medication Reminder: Time to take your ${medicationName}. Please confirm when taken.`,
        timestamp: new Date().toISOString(),
        type: 'medication_reminder',
        status: 'sending',
        metadata: { medicationName }
      };

      setMessages(prev => [...prev, tempMessage]);

      // Send reminder via WhatsApp API
      console.log(`💊 Sending medication reminder to ${selectedPatient.phone}: ${medicationName}`);
      const success = await whatsappApi.sendMedicationReminder(selectedPatient.phone, medicationName);

      if (success) {
        // Update message status to sent
        setMessages(prev =>
          prev.map(msg =>
            msg.id === tempMessage.id
              ? { ...msg, status: 'sent' as const }
              : msg
          )
        );

        // Show success notification
        alert(`✅ Medication reminder sent to ${selectedPatient.phone}!\n\nReminder: ${medicationName}\n\nCheck your WhatsApp for the reminder!`);
      } else {
        // Update message status to failed if API call failed
        setMessages(prev =>
          prev.map(msg =>
            msg.id === tempMessage.id
              ? { ...msg, status: 'failed' as const }
              : msg
          )
        );
      }
    } catch (error) {
      console.error('Error sending medication reminder:', error);
      alert(`❌ Failed to send medication reminder: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const sendHealthCheckin = async () => {
    if (!selectedPatient) return;

    try {
      setLoading(true);

      // Optimistically add message to UI
      const tempMessage: Message = {
        id: Date.now().toString(),
        patientId: selectedPatient.id,
        sender: 'system',
        content: '🏥 Health Check-in: How are you feeling today? Any symptoms or concerns you\'d like to discuss?',
        timestamp: new Date().toISOString(),
        type: 'health_checkin',
        status: 'sending'
      };

      setMessages(prev => [...prev, tempMessage]);

      // Send health check-in via WhatsApp API
      console.log(`🏥 Sending health check-in to ${selectedPatient.phone}`);
      const success = await whatsappApi.sendHealthCheckin(selectedPatient.phone);

      if (success) {
        // Update message status to sent
        setMessages(prev =>
          prev.map(msg =>
            msg.id === tempMessage.id
              ? { ...msg, status: 'sent' as const }
              : msg
          )
        );

        // Show success notification
        alert(`✅ Health check-in sent to ${selectedPatient.phone}!\n\nCheck your WhatsApp for the health check-in message!`);
      } else {
        // Update message status to failed if API call failed
        setMessages(prev =>
          prev.map(msg =>
            msg.id === tempMessage.id
              ? { ...msg, status: 'failed' as const }
              : msg
          )
        );
      }
    } catch (error) {
      console.error('Error sending health check-in:', error);
      alert(`❌ Failed to send health check-in: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'medication_reminder': return <Pill className="w-4 h-4 text-blue-500" />;
      case 'health_checkin': return <Heart className="w-4 h-4 text-green-500" />;
      case 'appointment': return <Calendar className="w-4 h-4 text-purple-500" />;
      case 'emergency': return <AlertCircle className="w-4 h-4 text-red-500" />;
      default: return <MessageCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent': return <Clock className="w-3 h-3 text-gray-400" />;
      case 'delivered': return <CheckCircle className="w-3 h-3 text-blue-400" />;
      case 'read': return <CheckCircle className="w-3 h-3 text-green-400" />;
      case 'failed': return <AlertCircle className="w-3 h-3 text-red-400" />;
      default: return null;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'severe': return 'bg-orange-500';
      case 'moderate': return 'bg-yellow-500';
      case 'mild': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-400';
      case 'away': return 'bg-yellow-400';
      case 'offline': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const filteredPatients = patients.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         patient.phone.includes(searchTerm) ||
                         patient.condition.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterStatus === 'all' || 
                         (filterStatus === 'urgent' && ['critical', 'severe'].includes(patient.severity)) ||
                         (filterStatus === 'unread' && patient.unreadCount > 0);
    
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="h-[400px] bg-white rounded-md border-2 border-gray-300 overflow-hidden">
      <div className="flex h-full">
        {/* Patient List Sidebar */}
        <div className="w-1/3 bg-white border-r-2 border-gray-300 flex flex-col">
          {/* Header */}
          <div className="p-2 border-b-2 border-gray-300 bg-blue-600 text-white">
            <div className="flex items-center gap-2 mb-1">
              <div className="p-1 bg-white rounded">
                <MessageCircle className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <h2 className="text-sm font-bold text-white">WhatsApp</h2>
                <div className="flex items-center gap-2">
                  <p className="text-xs text-white font-semibold">Messaging</p>
                  <div className={`flex items-center space-x-1 px-2 py-1 rounded border text-xs ${
                    backendStatus === 'connected' ? 'bg-white text-green-700 border-white' :
                    backendStatus === 'checking' ? 'bg-white text-yellow-700 border-white' : 'bg-white text-red-700 border-white'
                  }`}>
                    <div className={`w-2 h-2 rounded-full ${
                      backendStatus === 'connected' ? 'bg-green-500' :
                      backendStatus === 'checking' ? 'bg-yellow-500' : 'bg-red-500'
                    }`}></div>
                    <span className="font-bold text-xs">
                      {backendStatus === 'connected' ? 'Online' :
                       backendStatus === 'checking' ? 'Connecting' : 'Offline'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Search and Filter */}
            <div className="space-y-2">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-600 w-3 h-3" />
                <Input
                  placeholder="Search patients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 bg-white border-2 border-gray-300 text-sm h-8 text-gray-900 placeholder-gray-600 focus:border-blue-500 font-medium"
                />
              </div>
              <div className="flex gap-1">
                <Button
                  variant={filterStatus === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterStatus('all')}
                  className={`flex-1 text-xs px-1 py-1 font-bold ${
                    filterStatus === 'all'
                      ? 'bg-white text-blue-700 hover:bg-gray-50 h-6 border-2 border-white'
                      : 'border-2 border-white text-white hover:bg-white/20 h-6'
                  }`}
                >
                  All
                </Button>
                <Button
                  variant={filterStatus === 'urgent' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterStatus('urgent')}
                  className={`flex-1 text-xs px-1 py-1 font-bold ${
                    filterStatus === 'urgent'
                      ? 'bg-white text-blue-700 hover:bg-gray-50 h-6 border-2 border-white'
                      : 'border-2 border-white text-white hover:bg-white/20 h-6'
                  }`}
                >
                  Urgent
                </Button>
                <Button
                  variant={filterStatus === 'unread' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterStatus('unread')}
                  className={`flex-1 text-xs px-1 py-1 font-bold ${
                    filterStatus === 'unread'
                      ? 'bg-white text-blue-700 hover:bg-gray-50 h-6 border-2 border-white'
                      : 'border-2 border-white text-white hover:bg-white/20 h-6'
                  }`}
                >
                  Unread
                </Button>
              </div>
            </div>
          </div>

          {/* Patient List - Clear Text */}
          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full px-1 py-1"
              style={{
                scrollbarWidth: 'thin',
                scrollbarColor: '#cbd5e1 #f1f5f9'
              }}
            >
              <div className="space-y-1 pr-1">
                {filteredPatients.map((patient) => (
                  <div
                    key={patient.id}
                    onClick={() => setSelectedPatient(patient)}
                    className={`p-2 rounded border-2 cursor-pointer transition-all duration-200 ${
                      selectedPatient?.id === patient.id
                        ? 'bg-blue-50 border-blue-400'
                        : 'bg-white hover:bg-gray-50 border-gray-300'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <div className="relative">
                          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center border-2 border-white">
                            <User className="w-4 h-4 text-white" />
                          </div>
                          <div className={`absolute -bottom-0.5 -right-0.5 w-2 h-2 ${getStatusColor(patient.status)} rounded-full border-2 border-white`}></div>
                        </div>
                        <div className="min-w-0 flex-1">
                          <h3 className="font-bold text-sm text-gray-900 truncate">{patient.name || 'WhatsApp Test'}</h3>
                          <p className="text-xs text-gray-700 font-semibold truncate">{patient.phone}</p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end gap-1">
                        <Badge className={`${getSeverityColor(patient.severity)} text-white text-xs px-2 py-1 font-bold border-2 border-white`}>
                          {patient.severity}
                        </Badge>
                        {patient.unreadCount > 0 && (
                          <Badge className="bg-red-600 text-white text-xs px-2 py-1 font-bold border-2 border-white">
                            {patient.unreadCount}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <p className="text-xs text-gray-800 mb-1 line-clamp-1 truncate font-medium">{patient.lastMessage}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-600 truncate font-semibold">{patient.lastMessageTime}</span>
                      <Badge variant="outline" className="text-xs px-2 py-1 border-2 border-gray-400 text-gray-800 font-bold">{patient.language}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {selectedPatient ? (
            <>
              {/* Chat Header */}
              <div className="p-2 border-b-2 border-gray-300 bg-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center border-2 border-white">
                        <User className="w-4 h-4 text-white" />
                      </div>
                      <div className={`absolute -bottom-0.5 -right-0.5 w-2 h-2 ${getStatusColor(selectedPatient.status)} rounded-full border-2 border-white`}></div>
                    </div>
                    <div>
                      <h3 className="text-sm font-bold text-gray-900">{selectedPatient.name || 'WhatsApp Test'}</h3>
                      <p className="text-xs text-gray-700 font-semibold">{selectedPatient.condition || selectedPatient.phone}</p>
                      <div className="flex items-center gap-1 mt-1">
                        <Badge className={`${getSeverityColor(selectedPatient.severity)} text-white text-xs px-2 py-1 font-bold border-2 border-white`}>
                          {selectedPatient.severity}
                        </Badge>
                        <Badge variant="outline" className="text-xs px-2 py-1 border-2 border-gray-400 text-gray-800 font-bold">{selectedPatient.language}</Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button variant="outline" size="sm" className="text-xs px-2 py-1 border-2 border-gray-400 text-gray-800 hover:bg-gray-50 font-bold">
                      <Phone className="w-3 h-3 mr-1" />
                      Call
                    </Button>
                    <Button variant="outline" size="sm" className="text-xs px-2 py-1 border-2 border-gray-400 text-gray-800 hover:bg-gray-50 font-bold">
                      <FileText className="w-3 h-3 mr-1" />
                      History
                    </Button>
                    <Button variant="outline" size="sm" className="text-xs px-1 py-1 border-2 border-gray-400 text-gray-800 hover:bg-gray-50 font-bold">
                      <MoreVertical className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Messages Area - Clear Text */}
              <div className="flex-1 overflow-hidden bg-gray-100">
                <ScrollArea className="h-full p-2"
                  style={{
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#cbd5e1 #f1f5f9'
                  }}
                >
                  <div className="space-y-2">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.sender === 'doctor' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-[75%] p-2 rounded border-2 ${
                            message.sender === 'doctor'
                              ? 'bg-blue-600 text-white border-blue-700'
                              : message.sender === 'system'
                              ? 'bg-purple-100 text-gray-900 border-purple-300'
                              : 'bg-white text-gray-900 border-gray-400'
                          }`}
                        >
                          <div className="flex items-start gap-1 mb-1">
                            {getMessageTypeIcon(message.type)}
                            <div className="flex-1">
                              <p className={`text-xs font-bold ${
                                message.sender === 'doctor' ? 'text-white' : 'text-gray-900'
                              }`}>
                                {message.sender === 'doctor' ? 'Dr. Sarah Williams' :
                                 message.sender === 'system' ? 'HealthConnect AI' : (selectedPatient.name || 'Patient')}
                              </p>
                              <p className={`text-xs font-semibold ${
                                message.sender === 'doctor' ? 'text-blue-100' : 'text-gray-600'
                              }`}>
                                {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                              </p>
                            </div>
                          </div>
                          <p className={`text-sm mb-1 leading-relaxed font-medium ${
                            message.sender === 'doctor' ? 'text-white' : 'text-gray-900'
                          }`}>{message.content}</p>
                          <div className="flex items-center justify-between">
                            {message.metadata && (
                              <div className={`text-xs font-semibold ${
                                message.sender === 'doctor' ? 'text-blue-100' : 'text-gray-700'
                              }`}>
                                {message.metadata.medicationName && `Medication: ${message.metadata.medicationName}`}
                              </div>
                            )}
                            <div className="flex items-center gap-1">
                              {getStatusIcon(message.status)}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                    <div ref={messagesEndRef} />
                  </div>
                </ScrollArea>
              </div>

              {/* Quick Actions */}
              <div className="p-2 border-t-2 border-gray-300 bg-white">
                <div className="flex gap-1 mb-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => sendMedicationReminder(selectedPatient.medications[0])}
                    className="flex items-center gap-1 text-xs px-2 py-1 border-2 border-gray-400 text-gray-800 hover:bg-gray-50 font-bold"
                  >
                    <Pill className="w-3 h-3" />
                    Medication
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={sendHealthCheckin}
                    className="flex items-center gap-1 text-xs px-2 py-1 border-2 border-gray-400 text-gray-800 hover:bg-gray-50 font-bold"
                  >
                    <Heart className="w-3 h-3" />
                    Check-in
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 text-xs px-2 py-1 border-2 border-gray-400 text-gray-800 hover:bg-gray-50 font-bold"
                  >
                    <Calendar className="w-3 h-3" />
                    Appointment
                  </Button>
                </div>

                {/* Message Input */}
                <div className="flex gap-2">
                  <Textarea
                    placeholder="Type your message here..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                    className="flex-1 min-h-[40px] resize-none bg-white border-2 border-gray-400 text-sm text-gray-900 placeholder-gray-600 focus:border-blue-500 font-medium"
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim()}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 disabled:opacity-50 font-bold border-2 border-blue-700"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </>
          ) : (
            /* No Patient Selected */
            <div className="flex-1 flex items-center justify-center bg-gray-100">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 border-4 border-white">
                  <MessageCircle className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Select a Patient</h3>
                <p className="text-sm text-gray-700 font-semibold">Choose a patient from the list to start messaging</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WhatsAppCommunicationBoard;
