<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Urgent Care Flow - Ubuntu Health Connect SA</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            margin-bottom: 20px;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .success { color: #4ade80; }
        .error { color: #f87171; }
        .warning { color: #fbbf24; }
        .info { color: #60a5fa; }
        .urgent-case {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 Ubuntu Health Connect SA - Urgent Care Flow Test</h1>
        <p>Test the complete flow from AI Health Assistant Chat → Medical Report → Urgent Care Dashboard</p>
    </div>

    <div class="test-section">
        <h2>🧪 Test Urgent Care Flow</h2>
        <button onclick="testCompleteFlow()">Test Complete Urgent Care Flow</button>
        <button onclick="debugPatientCases()">Debug Patient Cases</button>
        <button onclick="debugUrgentCases()">Debug Urgent Cases</button>
        <button onclick="clearAllCases()">Clear All Cases</button>
        <div id="testLog" class="log">Click buttons above to test the urgent care flow...</div>
    </div>

    <div class="test-section">
        <h2>📊 Current State</h2>
        <div id="currentState" class="log">Loading current state...</div>
    </div>

    <div class="test-section">
        <h2>🚨 Current Urgent Cases</h2>
        <div id="urgentCases" class="log">Loading urgent cases...</div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type;
            logElement.innerHTML += `<span class="${colorClass}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        async function testCompleteFlow() {
            log('🧪 Testing Complete Urgent Care Flow...', 'info');
            
            try {
                // Step 1: Create a realistic urgent medical report
                log('Step 1: Creating urgent medical report...', 'info');
                
                const urgentReport = {
                    symptomsReported: {
                        primary: ['Chest pain', 'Shortness of breath', 'Dizziness'],
                        secondary: ['Nausea', 'Sweating'],
                        duration: 'Started 30 minutes ago',
                        onset: 'Sudden onset while resting'
                    },
                    assessmentSummary: {
                        severity: 'critical',
                        priority: 'Emergency',
                        urgentCare: true,
                        description: 'Patient presenting with acute chest pain and breathing difficulties requiring immediate medical attention'
                    },
                    aiRecommendations: {
                        immediate: ['Call emergency services immediately', 'Do not drive yourself to hospital', 'Take aspirin if not allergic'],
                        shortTerm: ['Emergency room evaluation', 'ECG and cardiac enzymes', 'Chest X-ray'],
                        longTerm: ['Cardiology follow-up', 'Lifestyle modifications', 'Medication compliance']
                    },
                    followUpPlan: {
                        appointmentNeeded: true,
                        timeframe: 'Immediate - Emergency',
                        location: 'Nearest Emergency Department',
                        nextCheckIn: 'Continuous monitoring',
                        medicationDelivery: false,
                        additionalNotes: ['Patient requires immediate emergency care', 'Possible cardiac event']
                    },
                    conversationSummary: 'Patient reported sudden onset chest pain with breathing difficulties during AI Health Assistant Chat session. Symptoms consistent with possible cardiac emergency.',
                    riskFactors: ['Acute onset', 'Cardiac symptoms', 'Breathing difficulties'],
                    redFlags: ['Chest pain with shortness of breath', 'Sudden onset symptoms', 'Possible cardiac emergency']
                };

                // Step 2: Create patient case using the service
                log('Step 2: Creating patient case...', 'info');
                
                if (typeof window.patientCaseService === 'undefined') {
                    // Try to access the service from the global scope
                    if (typeof debugPatientCases === 'function') {
                        debugPatientCases();
                    }
                    
                    // Create a mock case for testing
                    const testCase = {
                        id: `urgent_test_${Date.now()}`,
                        patientId: 'TEST_URGENT_001',
                        patientName: 'Test Patient (Urgent)',
                        age: 45,
                        location: 'Cape Town, South Africa',
                        language: 'English',
                        reportData: urgentReport,
                        conversation: [
                            {
                                id: 'msg_1',
                                role: 'user',
                                content: 'I have severe chest pain and I can\'t breathe properly!',
                                timestamp: new Date()
                            },
                            {
                                id: 'msg_2',
                                role: 'assistant',
                                content: 'This sounds very serious. I recommend you call emergency services immediately. Do not drive yourself to the hospital.',
                                timestamp: new Date()
                            }
                        ],
                        createdAt: new Date(),
                        status: 'new'
                    };

                    // Store in localStorage for testing
                    const existingCases = JSON.parse(localStorage.getItem('ubuntu-health-patient-cases') || '[]');
                    existingCases.unshift(testCase);
                    localStorage.setItem('ubuntu-health-patient-cases', JSON.stringify(existingCases));
                    
                    log('✅ Test urgent case created in localStorage', 'success');
                } else {
                    // Use the actual service
                    const conversation = [
                        {
                            id: 'msg_1',
                            role: 'user',
                            content: 'I have severe chest pain and I can\'t breathe properly!',
                            timestamp: new Date()
                        },
                        {
                            id: 'msg_2',
                            role: 'assistant',
                            content: 'This sounds very serious. I recommend you call emergency services immediately.',
                            timestamp: new Date()
                        }
                    ];

                    await window.patientCaseService.createCase(
                        'TEST_URGENT_001',
                        'Test Patient (Urgent)',
                        45,
                        'Cape Town, South Africa',
                        'English',
                        urgentReport,
                        conversation
                    );
                    
                    log('✅ Test urgent case created via service', 'success');
                }

                // Step 3: Verify the case appears in urgent cases
                log('Step 3: Checking urgent cases...', 'info');
                await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for updates
                
                displayCurrentState();
                displayUrgentCases();
                
                log('✅ Complete urgent care flow test completed!', 'success');
                log('📋 Check the Healthcare Provider Dashboard to see if the urgent case appears', 'info');
                
            } catch (error) {
                log(`❌ Error in urgent care flow test: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }

        function debugPatientCases() {
            log('🔍 Debugging Patient Cases...', 'info');
            
            try {
                if (typeof window.debugPatientCases === 'function') {
                    window.debugPatientCases();
                    log('✅ Patient case debug function called', 'success');
                } else {
                    log('⚠️ Patient case debug function not available', 'warning');
                }
                
                // Also check localStorage
                const cases = JSON.parse(localStorage.getItem('ubuntu-health-patient-cases') || '[]');
                log(`📊 Cases in localStorage: ${cases.length}`, 'info');
                
                cases.forEach((case_, index) => {
                    const isUrgent = case_.reportData?.assessmentSummary?.urgentCare || 
                                   case_.reportData?.assessmentSummary?.severity === 'critical' ||
                                   case_.reportData?.assessmentSummary?.severity === 'high';
                    log(`Case ${index + 1}: ${case_.patientName} - Urgent: ${isUrgent}`, 'info');
                });
                
            } catch (error) {
                log(`❌ Error debugging cases: ${error.message}`, 'error');
            }
        }

        function debugUrgentCases() {
            log('🚨 Debugging Urgent Cases...', 'info');
            
            try {
                const cases = JSON.parse(localStorage.getItem('ubuntu-health-patient-cases') || '[]');
                const urgentCases = cases.filter(case_ => 
                    case_.reportData?.assessmentSummary?.urgentCare ||
                    case_.reportData?.assessmentSummary?.severity === 'critical' ||
                    case_.reportData?.assessmentSummary?.severity === 'high'
                );
                
                log(`🚨 Total urgent cases: ${urgentCases.length}`, urgentCases.length > 0 ? 'warning' : 'info');
                
                urgentCases.forEach((case_, index) => {
                    log(`Urgent Case ${index + 1}: ${case_.patientName} - ${case_.reportData.assessmentSummary.severity}`, 'warning');
                });
                
            } catch (error) {
                log(`❌ Error debugging urgent cases: ${error.message}`, 'error');
            }
        }

        function clearAllCases() {
            log('🗑️ Clearing all cases...', 'info');
            
            try {
                if (typeof window.clearAllCases === 'function') {
                    window.clearAllCases();
                    log('✅ Cases cleared via service', 'success');
                }
                
                localStorage.removeItem('ubuntu-health-patient-cases');
                log('✅ Cases cleared from localStorage', 'success');
                
                displayCurrentState();
                displayUrgentCases();
                
            } catch (error) {
                log(`❌ Error clearing cases: ${error.message}`, 'error');
            }
        }

        function displayCurrentState() {
            const stateElement = document.getElementById('currentState');
            
            try {
                const cases = JSON.parse(localStorage.getItem('ubuntu-health-patient-cases') || '[]');
                const urgentCount = cases.filter(case_ => 
                    case_.reportData?.assessmentSummary?.urgentCare ||
                    case_.reportData?.assessmentSummary?.severity === 'critical' ||
                    case_.reportData?.assessmentSummary?.severity === 'high'
                ).length;
                
                stateElement.innerHTML = `
Total Cases: ${cases.length}
Urgent Cases: ${urgentCount}
Last Updated: ${new Date().toLocaleTimeString()}

Service Status:
- Patient Case Service: ${typeof window.debugPatientCases === 'function' ? '✅ Available' : '❌ Not Available'}
- Clear Function: ${typeof window.clearAllCases === 'function' ? '✅ Available' : '❌ Not Available'}
                `;
                
            } catch (error) {
                stateElement.innerHTML = `Error loading state: ${error.message}`;
            }
        }

        function displayUrgentCases() {
            const urgentElement = document.getElementById('urgentCases');
            
            try {
                const cases = JSON.parse(localStorage.getItem('ubuntu-health-patient-cases') || '[]');
                const urgentCases = cases.filter(case_ => 
                    case_.reportData?.assessmentSummary?.urgentCare ||
                    case_.reportData?.assessmentSummary?.severity === 'critical' ||
                    case_.reportData?.assessmentSummary?.severity === 'high'
                );
                
                if (urgentCases.length === 0) {
                    urgentElement.innerHTML = '✅ No urgent cases found';
                } else {
                    urgentElement.innerHTML = urgentCases.map((case_, index) => `
<div class="urgent-case">
<strong>Case ${index + 1}: ${case_.patientName}</strong>
Patient ID: ${case_.patientId}
Severity: ${case_.reportData.assessmentSummary.severity}
Urgent Care: ${case_.reportData.assessmentSummary.urgentCare ? 'Yes' : 'No'}
Symptoms: ${case_.reportData.symptomsReported.primary.join(', ')}
Created: ${new Date(case_.createdAt).toLocaleString()}
Status: ${case_.status}
</div>
                    `).join('');
                }
                
            } catch (error) {
                urgentElement.innerHTML = `Error loading urgent cases: ${error.message}`;
            }
        }

        // Initialize display
        document.addEventListener('DOMContentLoaded', function() {
            displayCurrentState();
            displayUrgentCases();
            
            // Refresh every 5 seconds
            setInterval(() => {
                displayCurrentState();
                displayUrgentCases();
            }, 5000);
        });
    </script>
</body>
</html>
