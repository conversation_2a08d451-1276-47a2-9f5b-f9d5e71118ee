# 🚀 Ubuntu Health Connect SA - Quick Reference

## 📁 **Project Structure**

```
ubuntu-health-connect-sa/
├── 📱 Frontend (React + TypeScript)
│   ├── src/
│   │   ├── components/          # UI Components
│   │   ├── pages/              # Application Pages  
│   │   ├── services/           # API Services & Integration
│   │   └── types/              # TypeScript Definitions
│   ├── package.json            # Frontend Dependencies
│   └── .env                    # Frontend Configuration
│
├── 🏥 Enhanced Backend (Python Flask)
│   ├── Health Agent Voice/
│   │   ├── app_enhanced.py     # Enhanced Backend API
│   │   ├── database/           # Database Layer
│   │   ├── requirements.txt    # Python Dependencies
│   │   └── .env               # Backend Configuration
│
├── 📞 Voice Triage System
│   ├── voice-triage/
│   │   ├── api/               # Voice Triage APIs
│   │   ├── services/          # AI & Speech Services
│   │   └── database/          # Triage Database
│
├── 🔗 Shared Resources
│   ├── shared/types/          # Shared TypeScript Types
│   └── database/              # Shared Database
│
└── 📚 Documentation
    ├── README_CONSOLIDATED.md  # Main Documentation
    └── QUICK_REFERENCE.md     # This File
```

## ⚡ **Quick Commands**

### **Start Development Environment**
```bash
# 1. Start Enhanced Backend
cd "Health Agent Voice"
python app_enhanced.py

# 2. Start Frontend (new terminal)
npm run dev

# 3. Access Applications
# Frontend: http://localhost:8081
# Backend:  http://localhost:5000
```

### **Test System Health**
```bash
# Test enhanced backend
python test_restructured_system.py

# Test integration
node test_enhanced_integration.js
```

## 🔧 **Configuration Files**

### **Frontend (.env)**
```env
VITE_API_BASE_URL=http://localhost:5000
VITE_OPENAI_API_KEY=your-openai-key
```

### **Backend (.env)**
```env
OPENAI_API_KEY=your-openai-key
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
```

## 🌐 **Key API Endpoints**

```
Health Check:
GET /health

Patient Management:
POST /api/patients
GET  /api/patients/{id_number}
GET  /api/patients?search=term

AI Interactions:
POST /api/patients/{id}/ai-interactions
GET  /api/patients/{id}/ai-interactions
```

## 🎯 **Core Features**

- ✅ **Enhanced Backend**: Proper MVC architecture
- ✅ **Type-Safe Frontend**: Full TypeScript support
- ✅ **Voice Triage**: 24/7 AI-powered phone consultations
- ✅ **WhatsApp Integration**: Real-time messaging
- ✅ **Multilingual Support**: All 11 SA languages
- ✅ **Provider Dashboard**: Complete patient management
- ✅ **Emergency Detection**: Automatic routing to 10177

## 🔒 **Security Features**

- ✅ **POPIA Compliant**: SA data protection standards
- ✅ **Encrypted Communications**: SSL/TLS everywhere
- ✅ **Input Validation**: Comprehensive sanitization
- ✅ **Access Control**: Role-based permissions
- ✅ **Audit Logging**: Complete interaction trails

## 📊 **System Status**

### **Current Implementation**
- ✅ Enhanced backend running on port 5000
- ✅ Frontend configured for port 8081
- ✅ Database with real patient data
- ✅ API integration layer complete
- ✅ Voice triage system integrated
- ✅ WhatsApp functionality active

### **Ready for Production**
- ✅ Comprehensive testing suite
- ✅ Error handling and monitoring
- ✅ Performance optimization
- ✅ Security compliance
- ✅ Documentation complete

---

**For complete documentation, see [README_CONSOLIDATED.md](README_CONSOLIDATED.md)**
