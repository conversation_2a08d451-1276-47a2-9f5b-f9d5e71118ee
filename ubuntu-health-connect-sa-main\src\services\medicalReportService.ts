// Medical Report Service for AI Health Assistant Chat
// Uses backend proxy to avoid CORS issues, with intelligent fallback system
import { ChatMessage } from './openaiService';

export interface MedicalReportData {
  assessmentSummary: {
    severity: 'low' | 'moderate' | 'high' | 'critical';
    priority: string;
    description: string;
    urgentCare: boolean;
  };
  symptomsReported: {
    primary: string[];
    secondary: string[];
    duration: string;
    onset: string;
  };
  aiRecommendations: {
    immediate: string[];
    shortTerm: string[];
    longTerm: string[];
  };
  followUpPlan: {
    appointmentNeeded: boolean;
    timeframe: string;
    location: string;
    medicationDelivery: boolean;
    nextCheckIn: string;
    additionalNotes: string[];
  };
  conversationSummary: string;
  riskFactors: string[];
  redFlags: string[];
}

class MedicalReportService {
  private reportSystemPrompt = `You are a specialized medical report generation AI for HealthConnect SA. Your role is to analyze patient conversations with the health assistant and generate comprehensive, structured medical assessment reports.

IMPORTANT GUIDELINES:
- Analyze the ENTIRE conversation to understand the patient's health situation
- Extract all symptoms mentioned throughout the conversation
- Identify progression or changes in symptoms
- Note any risk factors or concerning patterns
- Generate appropriate recommendations based on the full context
- Be thorough but concise in your analysis
- Always maintain professional medical language while being accessible
- Consider South African healthcare context and resources

ANALYSIS REQUIREMENTS:
1. Severity Assessment: Analyze all symptoms and determine overall severity
2. Symptom Extraction: List all symptoms mentioned, categorize by importance
3. Timeline Analysis: Understand when symptoms started and how they've progressed
4. Risk Assessment: Identify any red flags or concerning patterns
5. Recommendation Generation: Provide immediate, short-term, and long-term recommendations
6. Follow-up Planning: Suggest appropriate next steps and timeframes

RESPONSE FORMAT:
You must respond with a valid JSON object containing the following structure:
{
  "assessmentSummary": {
    "severity": "low|moderate|high|critical",
    "priority": "Low Priority|Moderate Priority|High Priority|Critical Priority",
    "description": "Brief description of overall assessment",
    "urgentCare": boolean
  },
  "symptomsReported": {
    "primary": ["main symptoms"],
    "secondary": ["additional symptoms"],
    "duration": "how long symptoms have been present",
    "onset": "when symptoms started"
  },
  "aiRecommendations": {
    "immediate": ["immediate care recommendations"],
    "shortTerm": ["recommendations for next 24-48 hours"],
    "longTerm": ["ongoing care recommendations"]
  },
  "followUpPlan": {
    "appointmentNeeded": boolean,
    "timeframe": "when to seek care",
    "location": "suggested healthcare facility type",
    "medicationDelivery": boolean,
    "nextCheckIn": "when system should follow up",
    "additionalNotes": ["any additional important notes"]
  },
  "conversationSummary": "Brief summary of the conversation",
  "riskFactors": ["identified risk factors"],
  "redFlags": ["any concerning symptoms or patterns"]
}

Remember: Base your analysis on the ENTIRE conversation, not just the last message.`;

  async generateMedicalReport(
    conversation: ChatMessage[],
    userLanguage: string = 'en'
  ): Promise<MedicalReportData> {
    try {
      console.log('🔄 Generating medical report...', { conversationLength: conversation.length, language: userLanguage });

      // Prepare conversation for analysis
      const conversationText = this.formatConversationForAnalysis(conversation);

      // Try backend proxy first
      try {
        console.log('📤 Trying backend proxy for medical report generation...');

        const response = await fetch('http://localhost:5000/api/openai/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'gpt-4o-mini',
            messages: [
              {
                role: 'system',
                content: `${this.reportSystemPrompt}\n\nUser's language: ${this.getLanguageName(userLanguage)}. Generate recommendations in this language when possible.`
              },
              {
                role: 'user',
                content: `Please analyze this patient conversation and generate a comprehensive medical assessment report:\n\n${conversationText}`
              }
            ],
            max_tokens: 1500,
            temperature: 0.3
          })
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data && result.data.choices && result.data.choices[0]) {
            const responseContent = result.data.choices[0].message.content;

            try {
              const reportData = JSON.parse(responseContent) as MedicalReportData;
              console.log('✅ Medical report generated via backend proxy');
              return this.validateAndEnhanceReport(reportData, conversation);
            } catch (parseError) {
              console.log('⚠️ Failed to parse backend proxy response, using fallback...');
            }
          }
        }
        console.log('⚠️ Backend proxy failed, using fallback medical report generation...');
      } catch (proxyError) {
        console.log('⚠️ Backend proxy error, using fallback:', proxyError.message);
      }

      // Fallback: Generate a comprehensive mock report based on conversation analysis
      console.log('🔄 Generating fallback medical report...');
      const fallbackReport = this.generateFallbackReport(conversation, userLanguage);
      console.log('✅ Fallback medical report generated');

      return this.validateAndEnhanceReport(fallbackReport, conversation);

    } catch (error) {
      console.error('❌ Medical Report Generation Error:', error);
      throw new Error(`Medical report generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private generateFallbackReport(conversation: ChatMessage[], userLanguage: string): MedicalReportData {
    // Analyze conversation to extract symptoms and generate appropriate report
    const userMessages = conversation.filter(msg => msg.role === 'user');
    const allText = userMessages.map(msg => msg.content.toLowerCase()).join(' ');

    // Extract symptoms
    const symptoms = this.extractSymptomsFromText(allText);
    const severity = this.assessSeverityFromText(allText);
    const urgentCare = this.checkUrgentCareNeeded(allText);

    return {
      assessmentSummary: {
        severity: severity,
        priority: severity === 'critical' ? 'Critical Priority' :
                 severity === 'high' ? 'High Priority' :
                 severity === 'moderate' ? 'Moderate Priority' : 'Low Priority',
        description: `Patient reported ${symptoms.primary.length > 0 ? symptoms.primary.join(', ') : 'health concerns'} during AI health assistant consultation.`,
        urgentCare: urgentCare
      },
      symptomsReported: symptoms,
      aiRecommendations: this.generateRecommendations(severity, urgentCare, userLanguage),
      followUpPlan: this.generateFollowUpPlan(severity, urgentCare),
      conversationSummary: `Patient engaged in AI health assistant chat reporting ${symptoms.primary.length > 0 ? symptoms.primary.join(' and ') : 'various health concerns'}. Assessment completed with appropriate recommendations provided.`,
      riskFactors: this.identifyRiskFactors(allText),
      redFlags: urgentCare ? ['Symptoms requiring immediate medical attention identified'] : []
    };
  }

  private formatConversationForAnalysis(conversation: ChatMessage[]): string {
    return conversation
      .map(msg => `${msg.role.toUpperCase()}: ${msg.content}`)
      .join('\n\n');
  }

  private validateAndEnhanceReport(report: MedicalReportData, conversation: ChatMessage[]): MedicalReportData {
    // Ensure all required fields are present with defaults
    return {
      assessmentSummary: {
        severity: report.assessmentSummary?.severity || 'moderate',
        priority: report.assessmentSummary?.priority || 'Moderate Priority',
        description: report.assessmentSummary?.description || 'Assessment based on reported symptoms',
        urgentCare: report.assessmentSummary?.urgentCare || false
      },
      symptomsReported: {
        primary: report.symptomsReported?.primary || ['Symptoms reported in conversation'],
        secondary: report.symptomsReported?.secondary || [],
        duration: report.symptomsReported?.duration || 'Duration not specified',
        onset: report.symptomsReported?.onset || 'Onset not specified'
      },
      aiRecommendations: {
        immediate: report.aiRecommendations?.immediate || ['Rest and monitor symptoms'],
        shortTerm: report.aiRecommendations?.shortTerm || ['Consider healthcare consultation'],
        longTerm: report.aiRecommendations?.longTerm || ['Maintain healthy lifestyle']
      },
      followUpPlan: {
        appointmentNeeded: report.followUpPlan?.appointmentNeeded ?? true,
        timeframe: report.followUpPlan?.timeframe || 'Within 24-48 hours if symptoms persist',
        location: report.followUpPlan?.location || 'Local community health centre',
        medicationDelivery: report.followUpPlan?.medicationDelivery || false,
        nextCheckIn: report.followUpPlan?.nextCheckIn || '24 hours',
        additionalNotes: report.followUpPlan?.additionalNotes || []
      },
      conversationSummary: report.conversationSummary || 'Patient consulted AI health assistant regarding health concerns',
      riskFactors: report.riskFactors || [],
      redFlags: report.redFlags || []
    };
  }



  private extractSymptomsFromText(text: string): { primary: string[], secondary: string[], duration: string, onset: string } {
    const symptoms = {
      primary: [] as string[],
      secondary: [] as string[],
      duration: 'Duration not specified',
      onset: 'Recent onset'
    };

    // Primary symptoms (more serious)
    const primarySymptoms = [
      'chest pain', 'difficulty breathing', 'severe pain', 'bleeding', 'unconscious',
      'heart attack', 'stroke', 'severe headache', 'high fever', 'vomiting blood'
    ];

    // Common symptoms
    const commonSymptoms = [
      'headache', 'fever', 'cough', 'fatigue', 'tired', 'nausea', 'dizzy',
      'sore throat', 'stomach ache', 'back pain', 'muscle pain', 'runny nose'
    ];

    // Check for primary symptoms
    primarySymptoms.forEach(symptom => {
      if (text.includes(symptom)) {
        symptoms.primary.push(symptom.charAt(0).toUpperCase() + symptom.slice(1));
      }
    });

    // Check for common symptoms
    commonSymptoms.forEach(symptom => {
      if (text.includes(symptom) && !symptoms.primary.some(p => p.toLowerCase().includes(symptom))) {
        symptoms.secondary.push(symptom.charAt(0).toUpperCase() + symptom.slice(1));
      }
    });

    // Extract duration if mentioned
    if (text.includes('days') || text.includes('weeks') || text.includes('hours')) {
      const durationMatch = text.match(/(\d+)\s*(day|week|hour|month)s?/);
      if (durationMatch) {
        symptoms.duration = `${durationMatch[1]} ${durationMatch[2]}${durationMatch[1] !== '1' ? 's' : ''}`;
      }
    }

    // If no symptoms found, add generic
    if (symptoms.primary.length === 0 && symptoms.secondary.length === 0) {
      symptoms.secondary.push('General health concerns');
    }

    return symptoms;
  }

  private assessSeverityFromText(text: string): 'low' | 'moderate' | 'high' | 'critical' {
    const criticalKeywords = ['chest pain', 'can\'t breathe', 'difficulty breathing', 'severe pain', 'bleeding heavily', 'unconscious'];
    const highKeywords = ['severe', 'intense', 'high fever', 'vomiting blood', 'persistent'];
    const moderateKeywords = ['pain', 'fever', 'headache', 'nausea', 'dizzy'];

    if (criticalKeywords.some(keyword => text.includes(keyword))) {
      return 'critical';
    }
    if (highKeywords.some(keyword => text.includes(keyword))) {
      return 'high';
    }
    if (moderateKeywords.some(keyword => text.includes(keyword))) {
      return 'moderate';
    }
    return 'low';
  }

  private checkUrgentCareNeeded(text: string): boolean {
    const urgentKeywords = [
      'chest pain', 'can\'t breathe', 'difficulty breathing', 'severe pain',
      'bleeding heavily', 'unconscious', 'heart attack', 'stroke', 'emergency'
    ];
    return urgentKeywords.some(keyword => text.includes(keyword));
  }

  private generateRecommendations(severity: string, urgentCare: boolean, language: string) {
    if (urgentCare) {
      return {
        immediate: ['Seek immediate medical attention', 'Call emergency services', 'Go to nearest hospital'],
        shortTerm: ['Follow up with healthcare provider', 'Monitor symptoms closely'],
        longTerm: ['Regular health check-ups', 'Maintain emergency contact information']
      };
    }

    switch (severity) {
      case 'high':
        return {
          immediate: ['Schedule appointment with healthcare provider within 24 hours', 'Monitor symptoms closely', 'Rest and stay hydrated'],
          shortTerm: ['Follow prescribed treatment plan', 'Return if symptoms worsen'],
          longTerm: ['Regular follow-up appointments', 'Lifestyle modifications as recommended']
        };
      case 'moderate':
        return {
          immediate: ['Rest and stay hydrated', 'Monitor symptoms', 'Consider over-the-counter remedies if appropriate'],
          shortTerm: ['Schedule appointment if symptoms persist', 'Maintain healthy diet'],
          longTerm: ['Regular health check-ups', 'Preventive care measures']
        };
      default:
        return {
          immediate: ['Continue monitoring symptoms', 'Maintain healthy lifestyle', 'Stay hydrated'],
          shortTerm: ['Contact healthcare provider if symptoms worsen', 'Get adequate rest'],
          longTerm: ['Regular health screenings', 'Maintain healthy habits']
        };
    }
  }

  private generateFollowUpPlan(severity: string, urgentCare: boolean) {
    if (urgentCare) {
      return {
        appointmentNeeded: true,
        timeframe: 'Immediate',
        location: 'Emergency department or nearest hospital',
        medicationDelivery: false,
        nextCheckIn: 'As directed by emergency medical team',
        additionalNotes: ['This is an urgent medical situation requiring immediate attention']
      };
    }

    switch (severity) {
      case 'high':
        return {
          appointmentNeeded: true,
          timeframe: 'Within 24 hours',
          location: 'Local clinic or healthcare provider',
          medicationDelivery: true,
          nextCheckIn: '24-48 hours',
          additionalNotes: ['Monitor symptoms closely', 'Return if condition worsens']
        };
      case 'moderate':
        return {
          appointmentNeeded: true,
          timeframe: 'Within 2-3 days if symptoms persist',
          location: 'Community health centre',
          medicationDelivery: false,
          nextCheckIn: '3-5 days',
          additionalNotes: ['Self-care measures may be sufficient initially']
        };
      default:
        return {
          appointmentNeeded: false,
          timeframe: 'If symptoms worsen or persist beyond a week',
          location: 'Local clinic',
          medicationDelivery: false,
          nextCheckIn: '1 week',
          additionalNotes: ['Continue self-monitoring', 'Maintain healthy lifestyle']
        };
    }
  }

  private identifyRiskFactors(text: string): string[] {
    const riskFactors = [];

    if (text.includes('smoke') || text.includes('smoking')) {
      riskFactors.push('Smoking history');
    }
    if (text.includes('diabetes')) {
      riskFactors.push('Diabetes');
    }
    if (text.includes('high blood pressure') || text.includes('hypertension')) {
      riskFactors.push('Hypertension');
    }
    if (text.includes('heart disease') || text.includes('cardiac')) {
      riskFactors.push('Cardiovascular risk factors');
    }
    if (text.includes('elderly') || text.includes('old age')) {
      riskFactors.push('Advanced age');
    }

    return riskFactors;
  }

  private getLanguageName(code: string): string {
    const languages: Record<string, string> = {
      'en': 'English',
      'zu': 'isiZulu (Zulu)',
      'xh': 'isiXhosa (Xhosa)',
      'af': 'Afrikaans'
    };
    return languages[code] || 'English';
  }
}

export const medicalReportService = new MedicalReportService();
