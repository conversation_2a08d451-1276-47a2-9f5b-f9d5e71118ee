# Ubuntu Health Connect SA - System Architecture

## 🏗️ Architecture Overview

Ubuntu Health Connect SA is a comprehensive healthcare platform built with a modern, scalable microservices architecture. The system combines AI-powered health assistance, real-time patient monitoring, and provider management tools to deliver exceptional healthcare services across South Africa.

## 🎯 Architecture Principles

- **Microservices Architecture**: Modular, loosely coupled services
- **API-First Design**: RESTful APIs with clear separation of concerns
- **Real-time Communication**: Live updates and notifications
- **Scalable Infrastructure**: Designed for growth and high availability
- **Security-First**: Comprehensive data protection and privacy
- **Mobile-Responsive**: Optimized for all device types

## 📊 System Layers

### 1. **Frontend Layer (React + TypeScript)**

#### User Interfaces
- **Patient Portal**: Registration, AI chat, medical reports
- **Provider Dashboard**: Urgent cases, case management, AI monitoring
- **Health Statistics**: Analytics, reports, population health trends

#### Core Components
- **Symptom Checker**: AI-powered health assessment
- **Monitoring Dashboard**: Real-time patient tracking
- **Patient Detail Modal**: Complete medical record view
- **Voice Triage**: Phone integration for remote areas

#### Technology Stack
```typescript
- React 18 with TypeScript
- Vite for build tooling
- Tailwind CSS for styling
- Radix UI for components
- React Router for navigation
- React Hook Form for forms
- Recharts for data visualization
```

### 2. **Service Layer (TypeScript)**

#### Core Services
- **OpenAI Service**: Health chat, medical reports, symptom analysis
- **Patient Case Service**: Case management, urgent detection, status tracking
- **Medical Report Service**: AI assessment, severity classification

#### Monitoring Services
- **Intelligent Monitoring Agent**: Auto patient monitoring, check-ins
- **Healthcare Monitoring**: Provider notifications, session management
- **Chat Interaction Service**: Conversation logging, medical records

#### Data Services
- **Patient Registration**: User management, authentication
- **Auth Database Service**: Patient lookup, verification, security
- **Healthcare Database**: Medical records, AI interactions, case history

### 3. **Backend Layer (Python Flask)**

#### API Layer
```python
- Health API: /health endpoint
- Patients API: /api/patients
- AI Interactions API: /api/ai-interactions
- OpenAI Proxy: /api/openai/chat
```

#### Backend Services
- **AI Service**: OpenAI integration and processing
- **Patient Service**: Data management and business logic
- **Database Manager**: SQLite operations and queries

#### Utilities
- **Validators**: Input validation and sanitization
- **Response Handlers**: API response formatting
- **Configuration**: Environment management

### 4. **Database Layer**

#### Primary Database: SQLite
```sql
-- Core Tables
patients: Personal info, medical history, contact details
ai_interactions: Chat logs, AI assessments, medical reports
patient_cases: Case status, urgency level, provider actions
```

#### Voice Database
- Call records and voice interaction logs
- Speech-to-text processing results
- Voice triage assessments

### 5. **Voice Triage System (Python)**

#### Components
- **Voice Triage API**: Phone integration and call handling
- **Voice Services**: Call processing, speech recognition, AI assessment
- **Voice Database**: Call records and interaction history

#### Integration
- Africa's Talking for voice/SMS services
- Respondio for chat platform integration
- OpenAI for voice-based health assessments

## 🔄 Data Flow Architecture

### Patient Journey Flow
```mermaid
Patient Registration → Authentication → AI Health Chat → Medical Report Generation → Case Creation → Provider Notification → Monitoring & Follow-up
```

### Urgent Case Flow
```mermaid
Symptom Assessment → Severity Classification → Urgent Case Detection → Provider Alert → AI Monitoring → Healthcare Response
```

### Provider Workflow
```mermaid
Dashboard Login → Case Review → Patient Communication → Treatment Planning → Progress Monitoring → Case Closure
```

## 🌐 External Integrations

### AI & Communication Services
- **OpenAI GPT-4**: Medical AI for health assessments
- **WhatsApp API**: Patient communication and follow-ups
- **Africa's Talking**: Voice/SMS services for rural areas
- **Respondio**: Multi-channel chat platform

### Healthcare Integrations (Future)
- South African Medical Schemes
- Provincial Health Departments
- Laboratory Information Systems
- Hospital Information Systems

## 🔒 Security Architecture

### Authentication & Authorization
- Multi-factor authentication for providers
- Patient ID and phone number verification
- Role-based access control (RBAC)
- Session management and timeout

### Data Protection
- Encryption at rest and in transit
- POPIA compliance features
- Audit trails for all interactions
- Secure API endpoints with rate limiting

### Privacy Controls
- Patient consent management
- Data anonymization for analytics
- Secure data sharing protocols
- Regular security audits

## 📱 Mobile & Accessibility

### Responsive Design
- Mobile-first approach
- Touch-optimized interfaces
- Offline capability for rural areas
- Progressive Web App (PWA) features

### Multilingual Support
- English, Zulu, Xhosa, Afrikaans
- Cultural sensitivity features
- Local healthcare context awareness
- Traditional medicine considerations

## 🚀 Deployment Architecture

### Development Environment
```bash
Frontend: Vite dev server (port 5173)
Backend: Flask development server (port 5000)
Database: Local SQLite file
Voice Services: Local Python services
```

### Production Environment (Recommended)
```bash
Frontend: Nginx + Static files
Backend: Gunicorn + Flask
Database: PostgreSQL or MySQL
Voice Services: Docker containers
Load Balancer: Nginx or AWS ALB
```

## 📊 Performance & Scalability

### Current Capabilities
- Concurrent users: 100+ simultaneous
- Response time: <2 seconds for API calls
- Database: Handles 10,000+ patient records
- Real-time updates: WebSocket connections

### Scaling Strategy
- Horizontal scaling with load balancers
- Database sharding by geographic region
- CDN for static assets
- Microservices containerization

## 🔧 Development Tools & Standards

### Code Quality
- TypeScript for type safety
- ESLint for code linting
- Prettier for code formatting
- Git hooks for pre-commit checks

### Testing Strategy
- Unit tests for core services
- Integration tests for API endpoints
- End-to-end tests for user workflows
- Performance testing for scalability

### Monitoring & Logging
- Application performance monitoring
- Error tracking and alerting
- User analytics and behavior tracking
- System health monitoring

## 🎯 Architecture Benefits

### For Healthcare Providers
- **Efficiency**: Streamlined workflows and automation
- **Accuracy**: AI-powered diagnostic assistance
- **Real-time**: Live patient monitoring and alerts
- **Comprehensive**: Complete patient view and history

### For Patients
- **Accessibility**: 24/7 AI health assistance
- **Convenience**: Mobile-friendly interface
- **Multilingual**: Native language support
- **Privacy**: Secure and confidential interactions

### For System Administrators
- **Maintainability**: Modular, well-documented code
- **Scalability**: Easy to scale and extend
- **Reliability**: Robust error handling and recovery
- **Monitoring**: Comprehensive logging and analytics

## 🛠️ Technology Stack Summary

### Frontend Technologies
```json
{
  "framework": "React 18 + TypeScript",
  "build_tool": "Vite",
  "styling": "Tailwind CSS + Radix UI",
  "routing": "React Router DOM",
  "forms": "React Hook Form + Zod",
  "charts": "Recharts",
  "state_management": "React Hooks + Context",
  "http_client": "Axios"
}
```

### Backend Technologies
```json
{
  "framework": "Python Flask",
  "database": "SQLite (development) / PostgreSQL (production)",
  "ai_integration": "OpenAI GPT-4",
  "voice_services": "Africa's Talking API",
  "communication": "WhatsApp API + Respondio",
  "cors": "Flask-CORS",
  "validation": "Custom validators"
}
```

### Development Tools
```json
{
  "package_manager": "npm",
  "linting": "ESLint + TypeScript ESLint",
  "formatting": "Prettier",
  "version_control": "Git",
  "environment": "Node.js + Python 3.11",
  "testing": "Vitest (planned)"
}
```

## 📁 Project Structure

```
ubuntu-health-connect-sa-main/
├── src/                          # Frontend source code
│   ├── components/               # React components
│   ├── pages/                    # Page components
│   ├── services/                 # API and business logic
│   ├── hooks/                    # Custom React hooks
│   ├── types/                    # TypeScript type definitions
│   ├── utils/                    # Utility functions
│   └── config/                   # Configuration files
├── backend/                      # Python Flask backend
│   ├── app/                      # Flask application
│   │   ├── api/                  # API endpoints
│   │   ├── services/             # Business logic
│   │   ├── database/             # Database management
│   │   └── utils/                # Utility functions
│   └── requirements.txt          # Python dependencies
├── voice-triage/                 # Voice triage system
├── Health Agent Voice/           # Voice agent system
├── database/                     # SQLite database files
├── public/                       # Static assets
└── dist/                         # Built application
```

## 🔄 API Endpoints

### Core APIs
```
GET  /health                      # Health check
GET  /api/patients               # Get patients
POST /api/patients               # Create patient
GET  /api/ai-interactions        # Get AI interactions
POST /api/ai-interactions        # Create AI interaction
POST /api/openai/chat            # OpenAI proxy endpoint
```

### Voice Triage APIs
```
POST /voice/triage               # Voice triage assessment
GET  /voice/history              # Voice interaction history
POST /voice/callback             # Africa's Talking callback
```

---

*This architecture supports Ubuntu Health Connect SA's mission to provide world-class healthcare technology that serves the diverse needs of South African communities while maintaining the highest standards of security, privacy, and clinical excellence.*
