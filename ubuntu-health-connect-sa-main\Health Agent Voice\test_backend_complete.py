#!/usr/bin/env python3
"""
Complete Backend Test Suite for Ubuntu Health Connect SA
Tests database, API endpoints, and frontend integration
"""

import os
import sys
import json
import requests
import time
from datetime import datetime

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

class BackendTester:
    def __init__(self):
        self.base_url = 'http://localhost:5000'
        self.db = None
        self.test_patient_id = None
        
    def run_complete_test(self):
        """Run complete backend test suite"""
        print("🧪 UBUNTU HEALTH BACKEND COMPLETE TEST SUITE")
        print("=" * 60)
        
        tests = [
            ("Database Connection", self.test_database_connection),
            ("Database Operations", self.test_database_operations),
            ("Backend Server", self.test_backend_server),
            ("Patient API Endpoints", self.test_patient_api),
            ("AI Interaction API", self.test_ai_interaction_api),
            ("Frontend Integration", self.test_frontend_integration)
        ]
        
        results = {}
        for test_name, test_func in tests:
            print(f"\n🔍 Testing: {test_name}")
            print("-" * 40)
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED")
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
                results[test_name] = False
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
        
        print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED - Backend is ready for production!")
        else:
            print("⚠️ Some tests failed - check the issues above")
        
        return passed == total
    
    def test_database_connection(self):
        """Test database connection and initialization"""
        try:
            self.db = DatabaseManager()
            
            # Test connection
            if not self.db.test_connection():
                print("❌ Database connection failed")
                return False
            
            print("✅ Database connection successful")
            
            # Test stats
            stats = self.db.get_database_stats()
            print(f"📊 Database stats: {stats}")
            
            return True
        except Exception as e:
            print(f"❌ Database test error: {e}")
            return False
    
    def test_database_operations(self):
        """Test database CRUD operations"""
        try:
            if not self.db:
                return False
            
            # Test patient creation
            test_patient = {
                'id': 'TEST_PAT_001',
                'first_name': 'Test',
                'last_name': 'Patient',
                'id_number': '9001015800087',
                'phone_number': '+27821234567',
                'email': '<EMAIL>',
                'age': 35,
                'gender': 'Male',
                'address': 'Test Address'
            }
            
            success = self.db.create_patient(test_patient)
            if not success:
                print("❌ Failed to create test patient")
                return False
            
            print("✅ Patient creation successful")
            self.test_patient_id = test_patient['id']
            
            # Test patient retrieval
            retrieved = self.db.get_patient_by_id(test_patient['id'])
            if not retrieved:
                print("❌ Failed to retrieve test patient")
                return False
            
            print("✅ Patient retrieval successful")
            
            # Test search
            search_results = self.db.search_patients('Test')
            if not search_results:
                print("❌ Patient search failed")
                return False
            
            print(f"✅ Patient search successful - found {len(search_results)} patients")
            
            return True
        except Exception as e:
            print(f"❌ Database operations error: {e}")
            return False
    
    def test_backend_server(self):
        """Test backend server health and endpoints"""
        try:
            # Test health endpoint
            response = requests.get(f'{self.base_url}/health', timeout=5)
            if not response.ok:
                print(f"❌ Health endpoint failed: {response.status_code}")
                return False
            
            health_data = response.json()
            print(f"✅ Health endpoint: {health_data['status']}")
            print(f"   Database connected: {health_data.get('database', {}).get('connected', False)}")
            print(f"   Features: {health_data.get('features', {})}")
            
            return health_data['status'] in ['healthy', 'degraded']
        except Exception as e:
            print(f"❌ Backend server test error: {e}")
            return False
    
    def test_patient_api(self):
        """Test patient API endpoints"""
        try:
            # Test patient creation via API
            test_patient_api = {
                'patient_id': 'TEST_API_001',
                'first_name': 'API',
                'last_name': 'Test',
                'id_number': '9002025800088',
                'phone_number': '+27821234568',
                'email': '<EMAIL>',
                'age': 30,
                'gender': 'Female'
            }
            
            response = requests.post(f'{self.base_url}/api/patients', 
                                   json=test_patient_api, timeout=10)
            if not response.ok:
                print(f"❌ Patient creation API failed: {response.status_code}")
                return False
            
            result = response.json()
            print(f"✅ Patient creation API: {result['action']}")
            
            # Test patient retrieval via API
            response = requests.get(f'{self.base_url}/api/patients/{test_patient_api["id_number"]}', 
                                  timeout=5)
            if not response.ok:
                print(f"❌ Patient retrieval API failed: {response.status_code}")
                return False
            
            patient_data = response.json()
            print(f"✅ Patient retrieval API: {patient_data['first_name']} {patient_data['last_name']}")
            
            # Test get all patients
            response = requests.get(f'{self.base_url}/api/patients', timeout=5)
            if not response.ok:
                print(f"❌ Get all patients API failed: {response.status_code}")
                return False
            
            all_patients = response.json()
            print(f"✅ Get all patients API: {all_patients['count']} patients")
            
            return True
        except Exception as e:
            print(f"❌ Patient API test error: {e}")
            return False
    
    def test_ai_interaction_api(self):
        """Test AI interaction API endpoints"""
        try:
            if not self.test_patient_id:
                print("❌ No test patient available for AI interaction test")
                return False
            
            # Test AI interaction creation
            interaction_data = {
                'interaction_type': 'chat',
                'summary': 'Test AI interaction',
                'full_conversation': 'Patient: Hello\nAI: How can I help you?',
                'ai_assessment': 'Patient seems healthy',
                'severity': 'Low',
                'recommendations': 'Continue monitoring',
                'urgent_care': False
            }
            
            response = requests.post(f'{self.base_url}/api/patients/{self.test_patient_id}/ai-interactions',
                                   json=interaction_data, timeout=10)
            if not response.ok:
                print(f"❌ AI interaction creation failed: {response.status_code}")
                return False
            
            result = response.json()
            print(f"✅ AI interaction creation: {result['interaction_id']}")
            
            # Test AI interaction retrieval
            response = requests.get(f'{self.base_url}/api/patients/{self.test_patient_id}/ai-interactions',
                                  timeout=5)
            if not response.ok:
                print(f"❌ AI interaction retrieval failed: {response.status_code}")
                return False
            
            interactions = response.json()
            print(f"✅ AI interaction retrieval: {interactions['count']} interactions")
            
            return True
        except Exception as e:
            print(f"❌ AI interaction API test error: {e}")
            return False
    
    def test_frontend_integration(self):
        """Test frontend integration points"""
        try:
            # Test CORS headers
            response = requests.options(f'{self.base_url}/api/patients', 
                                      headers={'Origin': 'http://localhost:8081'}, 
                                      timeout=5)
            
            # Test data format compatibility
            response = requests.get(f'{self.base_url}/api/patients', timeout=5)
            if response.ok:
                data = response.json()
                required_fields = ['success', 'patients', 'count']
                if all(field in data for field in required_fields):
                    print("✅ Frontend data format compatible")
                    return True
                else:
                    print("❌ Frontend data format incompatible")
                    return False
            
            return False
        except Exception as e:
            print(f"❌ Frontend integration test error: {e}")
            return False

def main():
    """Main test function"""
    print(f"🏥 Ubuntu Health Connect SA - Backend Test Suite")
    print(f"   Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tester = BackendTester()
    success = tester.run_complete_test()
    
    if success:
        print("\n🎉 Backend is ready for frontend integration!")
        print("   You can now start the frontend and test the complete system.")
    else:
        print("\n⚠️ Backend has issues that need to be resolved.")
        print("   Please fix the failing tests before proceeding.")
    
    return success

if __name__ == '__main__':
    main()
