/**
 * Enhanced Patient Service for Ubuntu Health Connect SA
 * Improved patient management with better error handling and caching
 */

import { enhancedApiClient, ApiResponse } from './apiClient';

// Enhanced Patient Types
export interface Patient {
  patient_id: string;
  first_name: string;
  last_name: string;
  id_number: string;
  phone_number: string;
  email?: string;
  date_of_birth?: string;
  age?: number;
  gender?: 'Male' | 'Female' | 'Other';
  address?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  medical_history?: MedicalCondition[];
  ai_interactions?: AIInteraction[];
  created_at?: string;
  updated_at?: string;
  found_in_backend?: boolean;
}

export interface MedicalCondition {
  id?: number;
  condition_name: string;
  severity?: 'Mild' | 'Moderate' | 'Severe' | 'Critical';
  status?: 'Active' | 'Chronic' | 'Under Treatment' | 'Resolved';
  diagnosed_date?: string;
  notes?: string;
}

export interface AIInteraction {
  id: string;
  patient_id: string;
  interaction_type: 'chat' | 'voice' | 'assessment' | 'monitoring';
  summary?: string;
  full_conversation?: string;
  ai_assessment?: string;
  severity?: 'Low' | 'Moderate' | 'High' | 'Critical';
  recommendations?: string;
  urgent_care?: boolean;
  timestamp?: string;
}

export interface CreatePatientRequest {
  patient_id?: string;
  first_name: string;
  last_name: string;
  id_number: string;
  phone_number: string;
  email?: string;
  date_of_birth?: string;
  age?: number;
  gender?: 'Male' | 'Female' | 'Other';
  address?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  medical_history?: MedicalCondition[];
}

// Enhanced Patient Service
class EnhancedPatientService {
  private readonly basePath = '/api/patients';
  private patientCache = new Map<string, Patient>();
  private cacheExpiry = new Map<string, number>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Create or update a patient with enhanced validation
   */
  async createPatient(patientData: CreatePatientRequest): Promise<ApiResponse<{
    patient_id: string;
    action: 'created' | 'updated';
    message: string;
  }>> {
    try {
      console.log('📝 Enhanced: Creating/updating patient:', patientData.first_name, patientData.last_name);
      
      // Validate required fields
      this.validatePatientData(patientData);
      
      const response = await enhancedApiClient.post(this.basePath, patientData);
      
      // Clear cache for this patient
      if (response.data?.patient_id) {
        this.clearPatientCache(patientData.id_number);
      }
      
      return response;
    } catch (error: any) {
      console.error('❌ Enhanced: Patient creation failed:', error);
      throw error;
    }
  }

  /**
   * Get patient by SA ID number with caching
   */
  async getPatientByIdNumber(idNumber: string): Promise<ApiResponse<Patient>> {
    try {
      console.log('🔍 Enhanced: Getting patient by ID number:', idNumber);
      
      // Check cache first
      const cached = this.getCachedPatient(idNumber);
      if (cached) {
        console.log('📋 Enhanced: Returning cached patient data');
        return {
          success: true,
          data: cached
        };
      }
      
      const response = await enhancedApiClient.get(`${this.basePath}/${idNumber}`);
      
      // Cache the result
      if (response.success && response.data) {
        this.cachePatient(idNumber, response.data);
      }
      
      return response;
    } catch (error: any) {
      console.error('❌ Enhanced: Get patient failed:', error);
      throw error;
    }
  }

  /**
   * Search patients with enhanced filtering
   */
  async searchPatients(searchTerm: string = '', providerId: string = 'PROV001'): Promise<ApiResponse<{
    patients: Patient[];
    count: number;
    search_term: string;
  }>> {
    try {
      console.log('🔍 Enhanced: Searching patients:', searchTerm);
      
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (providerId) params.append('provider_id', providerId);
      
      const response = await enhancedApiClient.get(`${this.basePath}?${params.toString()}`);
      
      // Cache individual patients
      if (response.success && response.data?.patients) {
        response.data.patients.forEach((patient: Patient) => {
          this.cachePatient(patient.id_number, patient);
        });
      }
      
      return response;
    } catch (error: any) {
      console.error('❌ Enhanced: Search patients failed:', error);
      throw error;
    }
  }

  /**
   * Get all patients
   */
  async getAllPatients(providerId: string = 'PROV001'): Promise<ApiResponse<{
    patients: Patient[];
    count: number;
    search_term: string;
  }>> {
    console.log('📋 Enhanced: Getting all patients for provider:', providerId);
    return this.searchPatients('', providerId);
  }

  /**
   * Update patient with cache invalidation
   */
  async updatePatient(patientId: string, updates: Partial<Patient>): Promise<ApiResponse<{
    patient_id: string;
    message: string;
  }>> {
    try {
      console.log('✏️ Enhanced: Updating patient:', patientId);
      
      const response = await enhancedApiClient.put(`${this.basePath}/${patientId}`, updates);
      
      // Clear cache for this patient
      this.clearPatientCacheById(patientId);
      
      return response;
    } catch (error: any) {
      console.error('❌ Enhanced: Update patient failed:', error);
      throw error;
    }
  }

  /**
   * Create AI interaction for patient
   */
  async createAIInteraction(patientId: string, interactionData: Omit<AIInteraction, 'id' | 'patient_id'>): Promise<ApiResponse<{
    interaction_id: string;
    message: string;
  }>> {
    try {
      console.log('🤖 Enhanced: Creating AI interaction for patient:', patientId);
      
      const response = await enhancedApiClient.post(`${this.basePath}/${patientId}/ai-interactions`, interactionData);
      
      // Clear patient cache to refresh AI interactions
      this.clearPatientCacheById(patientId);
      
      return response;
    } catch (error: any) {
      console.error('❌ Enhanced: AI interaction creation failed:', error);
      throw error;
    }
  }

  /**
   * Get AI interactions for patient
   */
  async getPatientAIInteractions(patientId: string): Promise<ApiResponse<{
    interactions: AIInteraction[];
    count: number;
  }>> {
    try {
      console.log('🤖 Enhanced: Getting AI interactions for patient:', patientId);
      
      return await enhancedApiClient.get(`${this.basePath}/${patientId}/ai-interactions`);
    } catch (error: any) {
      console.error('❌ Enhanced: Get AI interactions failed:', error);
      throw error;
    }
  }

  // Private helper methods
  private validatePatientData(data: CreatePatientRequest): void {
    const required = ['first_name', 'last_name', 'id_number', 'phone_number'];
    const missing = required.filter(field => !data[field as keyof CreatePatientRequest]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required fields: ${missing.join(', ')}`);
    }

    // Validate SA ID number (13 digits)
    if (!/^\d{13}$/.test(data.id_number)) {
      throw new Error('SA ID number must be 13 digits');
    }

    // Validate phone number
    if (!/^(\+27|0)[0-9]{9}$/.test(data.phone_number)) {
      throw new Error('Phone number must be valid SA format');
    }
  }

  private getCachedPatient(idNumber: string): Patient | null {
    const cached = this.patientCache.get(idNumber);
    const expiry = this.cacheExpiry.get(idNumber);
    
    if (cached && expiry && Date.now() < expiry) {
      return cached;
    }
    
    // Remove expired cache
    this.patientCache.delete(idNumber);
    this.cacheExpiry.delete(idNumber);
    return null;
  }

  private cachePatient(idNumber: string, patient: Patient): void {
    this.patientCache.set(idNumber, patient);
    this.cacheExpiry.set(idNumber, Date.now() + this.CACHE_DURATION);
  }

  private clearPatientCache(idNumber: string): void {
    this.patientCache.delete(idNumber);
    this.cacheExpiry.delete(idNumber);
  }

  private clearPatientCacheById(patientId: string): void {
    // Find and remove by patient ID
    for (const [idNumber, patient] of this.patientCache.entries()) {
      if (patient.patient_id === patientId) {
        this.clearPatientCache(idNumber);
        break;
      }
    }
  }

  // Cache management
  public clearAllCache(): void {
    this.patientCache.clear();
    this.cacheExpiry.clear();
    console.log('🗑️ Enhanced: Patient cache cleared');
  }

  public getCacheStats(): { size: number; entries: string[] } {
    return {
      size: this.patientCache.size,
      entries: Array.from(this.patientCache.keys())
    };
  }
}

// Create singleton instance
export const enhancedPatientService = new EnhancedPatientService();

// Make available globally for debugging
(window as any).enhancedPatientService = enhancedPatientService;
