"""
Mock Voice Triage System
Simulates the complete voice triage experience without requiring real phone infrastructure
This demonstrates exactly how the system would work when properly deployed
"""

import os
import json
import time
from datetime import datetime
from typing import Dict, List
import uuid

class MockVoiceTriageSystem:
    """Mock implementation of the voice triage system"""
    
    def __init__(self):
        self.session_id = str(uuid.uuid4())
        self.caller_number = "+27727803582"  # Your number
        self.triage_number = "+27727803582"  # Mock triage number
        self.conversation_log = []
        
        print("🏥 UBUNTU HEALTH VOICE TRIAGE SYSTEM")
        print("=" * 50)
        print("📞 Simulating call to:", self.triage_number)
        print("📱 Caller:", self.caller_number)
        print("🆔 Session ID:", self.session_id)
        print("=" * 50)
    
    def simulate_incoming_call(self):
        """Simulate the complete incoming call flow"""
        
        print("\n🔔 Incoming call detected...")
        time.sleep(1)
        
        # Step 1: AI Greeting
        self.ai_speak("Welcome to Ubuntu Health AI Triage Assistant. I'm here to help assess your health concerns and connect you with appropriate care. Please tell me what's bothering you today.")
        
        # Step 2: Patient describes symptoms
        patient_input = self.get_patient_input("Please describe your symptoms:")
        
        # Step 3: AI asks follow-up questions
        self.ai_speak("I understand. Can you tell me when these symptoms started?")
        
        symptom_duration = self.get_patient_input("When did symptoms start?")
        
        # Step 4: AI asks about severity
        self.ai_speak("On a scale of 1 to 10, how would you rate your pain or discomfort?")
        
        pain_level = self.get_patient_input("Pain level (1-10):")
        
        # Step 5: AI assessment
        self.ai_speak("Thank you for providing that information. I'm now analyzing your symptoms and determining the appropriate level of care.")
        
        print("\n🧠 AI Processing...")
        time.sleep(2)
        
        # Step 6: Generate assessment
        assessment = self.generate_assessment(patient_input, symptom_duration, pain_level)
        
        # Step 7: AI provides recommendations
        self.ai_speak(assessment['ai_response'])
        
        # Step 8: Next actions based on risk level
        self.handle_next_actions(assessment)
        
        # Step 9: Call completion
        self.ai_speak("Thank you for using Ubuntu Health. Take care and don't hesitate to call back if you have any concerns.")
        
        print("\n📞 Call ended")
        
        return assessment
    
    def ai_speak(self, message: str):
        """Simulate AI speaking"""
        print(f"\n🤖 AI: {message}")
        self.conversation_log.append({
            'speaker': 'AI',
            'message': message,
            'timestamp': datetime.now().isoformat()
        })
        time.sleep(1)  # Simulate speech time
    
    def get_patient_input(self, prompt: str) -> str:
        """Get patient input (simulated)"""
        print(f"\n👤 {prompt}")
        
        # Simulate patient responses based on common scenarios
        responses = {
            "Please describe your symptoms:": [
                "I have a severe headache and feel nauseous",
                "I'm having chest pain and difficulty breathing", 
                "I have a sore throat and mild cough",
                "I cut my finger while cooking"
            ],
            "When did symptoms start?": [
                "About 2 hours ago",
                "This morning",
                "Yesterday evening",
                "Just now"
            ],
            "Pain level (1-10):": ["8", "3", "5", "2"]
        }
        
        # For demo, cycle through responses
        if prompt in responses:
            import random
            response = random.choice(responses[prompt])
        else:
            response = "I'm not sure"
        
        print(f"👤 Patient: {response}")
        
        self.conversation_log.append({
            'speaker': 'Patient',
            'message': response,
            'timestamp': datetime.now().isoformat()
        })
        
        return response
    
    def generate_assessment(self, symptoms: str, duration: str, pain_level: str) -> Dict:
        """Generate AI assessment based on symptoms"""
        
        # Simple rule-based assessment for demo
        pain_score = int(pain_level) if pain_level.isdigit() else 5
        
        if "chest pain" in symptoms.lower() or "breathing" in symptoms.lower():
            risk_level = "critical"
            urgency_score = 9
            recommendations = [
                "Seek immediate emergency care",
                "Call 10177 or go to nearest emergency room",
                "Do not drive yourself - call ambulance"
            ]
            ai_response = "Based on your symptoms of chest pain and breathing difficulties, this requires immediate medical attention. I'm immediately notifying our emergency medical team and strongly recommend you go to the nearest emergency room or call 10177 right away."
            
        elif pain_score >= 7 or "severe" in symptoms.lower():
            risk_level = "high"
            urgency_score = 7
            recommendations = [
                "Contact your healthcare provider today",
                "Monitor symptoms closely",
                "Seek care if symptoms worsen"
            ]
            ai_response = "Your symptoms indicate a high priority condition. I recommend contacting your healthcare provider today. I'm sending a detailed report to our medical team who will contact you within the next few hours."
            
        elif pain_score >= 4:
            risk_level = "moderate"
            urgency_score = 4
            recommendations = [
                "Monitor symptoms for 24-48 hours",
                "Contact provider if symptoms worsen",
                "Rest and stay hydrated"
            ]
            ai_response = "Your symptoms appear to be moderate and should be monitored carefully. I recommend rest and staying hydrated. Contact us again if symptoms worsen or don't improve in 24-48 hours."
            
        else:
            risk_level = "low"
            urgency_score = 2
            recommendations = [
                "Self-care at home",
                "Monitor for changes",
                "Contact provider if concerned"
            ]
            ai_response = "Based on your symptoms, this appears to be a minor condition that can be managed at home. Continue with basic self-care and monitor for any changes."
        
        return {
            'risk_level': risk_level,
            'urgency_score': urgency_score,
            'recommendations': recommendations,
            'ai_response': ai_response,
            'symptoms': symptoms,
            'duration': duration,
            'pain_level': pain_level
        }
    
    def handle_next_actions(self, assessment: Dict):
        """Handle next actions based on assessment"""
        
        risk_level = assessment['risk_level']
        
        print(f"\n📋 TRIAGE ASSESSMENT COMPLETE")
        print(f"Risk Level: {risk_level.upper()}")
        print(f"Urgency Score: {assessment['urgency_score']}/10")
        
        if risk_level == "critical":
            print("\n🚨 CRITICAL ALERT TRIGGERED")
            print("✅ Emergency medical team notified")
            print("✅ SMS sent to patient with emergency instructions")
            print("✅ Healthcare providers alerted immediately")
            
        elif risk_level == "high":
            print("\n⚠️ HIGH PRIORITY CASE")
            print("✅ On-call healthcare provider notified")
            print("✅ Patient added to priority review queue")
            print("✅ Follow-up call scheduled within 2 hours")
            
        elif risk_level == "moderate":
            print("\n📋 MODERATE CASE")
            print("✅ Added to daily review queue")
            print("✅ Follow-up call scheduled in 24-48 hours")
            print("✅ Self-care instructions sent via SMS")
            
        else:
            print("\n✅ LOW PRIORITY CASE")
            print("✅ Documented in patient record")
            print("✅ Self-care instructions sent via SMS")
            print("✅ Optional follow-up in 1 week")
    
    def save_call_report(self, assessment: Dict):
        """Save the complete call report"""
        
        report = {
            'session_id': self.session_id,
            'caller_number': self.caller_number,
            'triage_number': self.triage_number,
            'call_timestamp': datetime.now().isoformat(),
            'conversation_log': self.conversation_log,
            'assessment': assessment,
            'call_duration_seconds': len(self.conversation_log) * 2  # Rough estimate
        }
        
        filename = f"voice_triage_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Call report saved: {filename}")
        return filename

def main():
    """Run the mock voice triage simulation"""
    
    print("🚀 Starting Mock Voice Triage Simulation")
    print("This demonstrates exactly how the real system would work")
    print("when properly deployed with Africa's Talking")
    
    # Create mock system
    triage_system = MockVoiceTriageSystem()
    
    # Simulate the call
    assessment = triage_system.simulate_incoming_call()
    
    # Save report
    report_file = triage_system.save_call_report(assessment)
    
    print("\n" + "=" * 60)
    print("🎯 SIMULATION COMPLETE")
    print("=" * 60)
    print("This simulation shows exactly what would happen when:")
    print("1. ✅ You have a production Africa's Talking account")
    print("2. ✅ You purchase a dedicated phone number")
    print("3. ✅ You configure webhooks properly")
    print("4. ✅ You deploy the Flask API to a public server")
    print("5. ✅ Patients call the triage number")
    print("\n📞 The AI would answer, assess symptoms, and route appropriately!")

if __name__ == '__main__':
    main()
