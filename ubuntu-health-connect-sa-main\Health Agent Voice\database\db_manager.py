"""
Database Manager for Ubuntu Health Connect SA
Handles all database operations with SQLite backend
"""

import sqlite3
import json
import os
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from contextlib import contextmanager

class DatabaseManager:
    def __init__(self, db_path: str = "database/ubuntu_health.db"):
        """Initialize database manager"""
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # Ensure database directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # Initialize database
        self.init_database()
    
    def init_database(self):
        """Initialize database with schema"""
        try:
            with self.get_connection() as conn:
                # Read and execute schema
                schema_path = os.path.join(os.path.dirname(__file__), 'schema.sql')
                if os.path.exists(schema_path):
                    with open(schema_path, 'r') as f:
                        schema = f.read()
                    conn.executescript(schema)
                    self.logger.info("✅ Database initialized successfully")
                else:
                    self.logger.error("❌ Schema file not found")
        except Exception as e:
            self.logger.error(f"❌ Error initializing database: {e}")
    
    @contextmanager
    def get_connection(self):
        """Get database connection with context manager"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable dict-like access
        try:
            yield conn
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    # PATIENT OPERATIONS
    def create_patient(self, patient_data: Dict[str, Any]) -> bool:
        """Create a new patient record"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO patients (
                        id, first_name, last_name, id_number, phone_number, email,
                        date_of_birth, age, gender, address, emergency_contact_name,
                        emergency_contact_phone, emergency_contact_relationship
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    patient_data['id'],
                    patient_data['first_name'],
                    patient_data['last_name'],
                    patient_data['id_number'],
                    patient_data['phone_number'],
                    patient_data.get('email', ''),
                    patient_data.get('date_of_birth'),
                    patient_data.get('age'),
                    patient_data.get('gender'),
                    patient_data.get('address', ''),
                    patient_data.get('emergency_contact_name', ''),
                    patient_data.get('emergency_contact_phone', ''),
                    patient_data.get('emergency_contact_relationship', '')
                ))
                
                # Grant access to default provider
                cursor.execute("""
                    INSERT OR IGNORE INTO patient_provider_access (patient_id, provider_id, access_level)
                    VALUES (?, 'PROV001', 'write')
                """, (patient_data['id'],))
                
                self.logger.info(f"✅ Created patient: {patient_data['id']}")
                return True
        except Exception as e:
            self.logger.error(f"❌ Error creating patient: {e}")
            return False
    
    def get_patient_by_id(self, patient_id: str) -> Optional[Dict[str, Any]]:
        """Get patient by ID"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM patients WHERE id = ?", (patient_id,))
                row = cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            self.logger.error(f"❌ Error getting patient by ID: {e}")
            return None
    
    def get_patient_by_id_number(self, id_number: str) -> Optional[Dict[str, Any]]:
        """Get patient by SA ID number"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM patients WHERE id_number = ?", (id_number,))
                row = cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            self.logger.error(f"❌ Error getting patient by ID number: {e}")
            return None
    
    def search_patients(self, search_term: str = "", provider_id: str = "PROV001") -> List[Dict[str, Any]]:
        """Search patients with provider access control"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if search_term:
                    cursor.execute("""
                        SELECT p.* FROM patients p
                        JOIN patient_provider_access ppa ON p.id = ppa.patient_id
                        WHERE ppa.provider_id = ? AND (
                            p.first_name LIKE ? OR p.last_name LIKE ? OR 
                            p.id_number LIKE ? OR p.phone_number LIKE ? OR p.id LIKE ?
                        )
                        ORDER BY p.created_at DESC
                    """, (provider_id, f"%{search_term}%", f"%{search_term}%", 
                          f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"))
                else:
                    cursor.execute("""
                        SELECT p.* FROM patients p
                        JOIN patient_provider_access ppa ON p.id = ppa.patient_id
                        WHERE ppa.provider_id = ?
                        ORDER BY p.created_at DESC
                    """, (provider_id,))
                
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"❌ Error searching patients: {e}")
            return []

    def get_all_patients(self) -> List[Dict[str, Any]]:
        """Get all patients"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM patients ORDER BY last_name, first_name")

                rows = cursor.fetchall()
                patients = []

                for row in rows:
                    patient = dict(row)
                    # Add computed fields
                    patient['full_name'] = f"{patient['first_name']} {patient['last_name']}"
                    patients.append(patient)

                return patients

        except Exception as e:
            self.logger.error(f"❌ Error getting all patients: {e}")
            return []

    def update_patient(self, patient_id: str, updates: Dict[str, Any]) -> bool:
        """Update patient information"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Build dynamic update query
                set_clauses = []
                values = []
                for key, value in updates.items():
                    if key != 'id':  # Don't update ID
                        set_clauses.append(f"{key} = ?")
                        values.append(value)
                
                if set_clauses:
                    values.append(patient_id)
                    query = f"UPDATE patients SET {', '.join(set_clauses)}, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
                    cursor.execute(query, values)
                    self.logger.info(f"✅ Updated patient: {patient_id}")
                    return True
                return False
        except Exception as e:
            self.logger.error(f"❌ Error updating patient: {e}")
            return False
    
    # AI INTERACTIONS
    def create_ai_interaction(self, interaction_data: Dict[str, Any]) -> bool:
        """Create AI interaction record"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO ai_interactions (
                        id, patient_id, interaction_type, summary, full_conversation,
                        ai_assessment, severity, recommendations, urgent_care
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    interaction_data['id'],
                    interaction_data['patient_id'],
                    interaction_data.get('interaction_type', 'chat'),
                    interaction_data.get('summary', ''),
                    interaction_data.get('full_conversation', ''),
                    interaction_data.get('ai_assessment', ''),
                    interaction_data.get('severity', 'Low'),
                    interaction_data.get('recommendations', ''),
                    interaction_data.get('urgent_care', False)
                ))
                self.logger.info(f"✅ Created AI interaction: {interaction_data['id']}")
                return True
        except Exception as e:
            self.logger.error(f"❌ Error creating AI interaction: {e}")
            return False
    
    def get_ai_interactions_by_patient(self, patient_id: str) -> List[Dict[str, Any]]:
        """Get AI interactions for a patient"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM ai_interactions 
                    WHERE patient_id = ? 
                    ORDER BY timestamp DESC
                """, (patient_id,))
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"❌ Error getting AI interactions: {e}")
            return []

    def get_all_ai_interactions(self) -> List[Dict[str, Any]]:
        """Get all AI interactions"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT ai.*, p.first_name, p.last_name
                    FROM ai_interactions ai
                    LEFT JOIN patients p ON ai.patient_id = p.id
                    ORDER BY ai.timestamp DESC
                """)

                rows = cursor.fetchall()
                interactions = []

                for row in rows:
                    interaction = dict(row)
                    # Add patient name if available
                    if interaction.get('first_name') and interaction.get('last_name'):
                        interaction['patient_name'] = f"{interaction['first_name']} {interaction['last_name']}"
                    interactions.append(interaction)

                return interactions

        except Exception as e:
            self.logger.error(f"❌ Error getting all AI interactions: {e}")
            return []

    # MEDICAL HISTORY
    def add_medical_condition(self, patient_id: str, condition_data: Dict[str, Any]) -> bool:
        """Add medical condition to patient history"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO medical_history (
                        patient_id, condition_name, severity, status, diagnosed_date, notes
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    patient_id,
                    condition_data['condition_name'],
                    condition_data.get('severity', 'Mild'),
                    condition_data.get('status', 'Active'),
                    condition_data.get('diagnosed_date'),
                    condition_data.get('notes', '')
                ))
                return True
        except Exception as e:
            self.logger.error(f"❌ Error adding medical condition: {e}")
            return False
    
    def get_medical_history(self, patient_id: str) -> List[Dict[str, Any]]:
        """Get patient's medical history"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM medical_history 
                    WHERE patient_id = ? 
                    ORDER BY diagnosed_date DESC
                """, (patient_id,))
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"❌ Error getting medical history: {e}")
            return []
    
    # UTILITY METHODS
    def get_database_stats(self) -> Dict[str, int]:
        """Get database statistics"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                stats = {}
                tables = ['patients', 'ai_interactions', 'medical_history', 'medications', 'appointments']
                
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    stats[table] = cursor.fetchone()[0]
                
                return stats
        except Exception as e:
            self.logger.error(f"❌ Error getting database stats: {e}")
            return {}
    
    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                return True
        except Exception as e:
            self.logger.error(f"❌ Database connection test failed: {e}")
            return False
