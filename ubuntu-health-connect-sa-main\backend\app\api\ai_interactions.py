"""
AI Interactions API endpoints for Ubuntu Health Connect SA
"""

from flask import Blueprint, request, jsonify, current_app
from app.api import bp
from app.database import get_db
from app.services.ai_service import AIService
from app.utils.responses import success_response, error_response
import uuid

@bp.route('/patients/<patient_id>/ai-interactions', methods=['POST'])
def create_ai_interaction(patient_id):
    """
    Create AI interaction record
    """
    try:
        data = request.get_json()
        
        current_app.logger.info(f"📝 Creating AI interaction for patient: {patient_id}")
        
        # Prepare interaction data
        interaction_data = {
            'id': str(uuid.uuid4()),
            'patient_id': patient_id,
            'interaction_type': data.get('interaction_type', 'chat'),
            'summary': data.get('summary', ''),
            'full_conversation': data.get('full_conversation', ''),
            'ai_assessment': data.get('ai_assessment', ''),
            'severity': data.get('severity', 'Low'),
            'recommendations': data.get('recommendations', ''),
            'urgent_care': data.get('urgent_care', False)
        }
        
        # Use AI service
        ai_service = AIService(get_db())
        result = ai_service.create_interaction(interaction_data)
        
        if result['success']:
            current_app.logger.info(f"✅ AI interaction created: {interaction_data['id']}")
            return success_response(result, 201)
        else:
            return error_response(result['error'], 500)
        
    except Exception as e:
        current_app.logger.error(f"❌ Error creating AI interaction: {str(e)}")
        return error_response(str(e), 500)

@bp.route('/patients/<patient_id>/ai-interactions', methods=['GET'])
def get_ai_interactions(patient_id):
    """
    Get AI interactions for a patient
    """
    try:
        current_app.logger.info(f"🔍 Getting AI interactions for patient: {patient_id}")
        
        ai_service = AIService(get_db())
        interactions = ai_service.get_patient_interactions(patient_id)
        
        current_app.logger.info(f"✅ Found {len(interactions)} AI interactions")
        
        return success_response({
            'interactions': interactions,
            'count': len(interactions)
        })
        
    except Exception as e:
        current_app.logger.error(f"❌ Error getting AI interactions: {str(e)}")
        return error_response(str(e), 500)

@bp.route('/ai-interactions/<interaction_id>', methods=['GET'])
def get_ai_interaction(interaction_id):
    """
    Get specific AI interaction
    """
    try:
        ai_service = AIService(get_db())
        interaction = ai_service.get_interaction_by_id(interaction_id)
        
        if interaction:
            return success_response(interaction)
        else:
            return error_response('AI interaction not found', 404)
        
    except Exception as e:
        current_app.logger.error(f"❌ Error getting AI interaction: {str(e)}")
        return error_response(str(e), 500)

@bp.route('/ai-interactions/<interaction_id>', methods=['PUT'])
def update_ai_interaction(interaction_id):
    """
    Update AI interaction
    """
    try:
        data = request.get_json()

        ai_service = AIService(get_db())
        result = ai_service.update_interaction(interaction_id, data)

        if result['success']:
            return success_response(result)
        else:
            return error_response(result['error'], 500)

    except Exception as e:
        current_app.logger.error(f"❌ Error updating AI interaction: {str(e)}")
        return error_response(str(e), 500)

@bp.route('/ai-interactions', methods=['GET'])
def get_all_ai_interactions():
    """
    Get all AI interactions (for urgent cases dashboard)
    """
    try:
        current_app.logger.info("🔍 Getting all AI interactions")

        ai_service = AIService(get_db())
        interactions = ai_service.get_all_interactions()

        current_app.logger.info(f"✅ Found {len(interactions)} AI interactions")

        return success_response({
            'interactions': interactions,
            'count': len(interactions)
        })

    except Exception as e:
        current_app.logger.error(f"❌ Error getting all AI interactions: {str(e)}")
        return error_response(str(e), 500)

@bp.route('/ai-interactions', methods=['POST'])
def create_general_ai_interaction():
    """
    Create AI interaction (general endpoint)
    """
    try:
        data = request.get_json()

        current_app.logger.info(f"📝 Creating AI interaction for patient: {data.get('patient_id')}")

        # Prepare interaction data
        interaction_data = {
            'id': data.get('id', str(uuid.uuid4())),
            'patient_id': data.get('patient_id'),
            'interaction_type': data.get('interaction_type', 'chat'),
            'summary': data.get('summary', ''),
            'full_conversation': data.get('full_conversation', ''),
            'ai_assessment': data.get('ai_assessment', ''),
            'severity': data.get('severity', 'Low'),
            'recommendations': data.get('recommendations', ''),
            'urgent_care': data.get('urgent_care', False)
        }

        # Use AI service
        ai_service = AIService(get_db())
        result = ai_service.create_interaction(interaction_data)

        if result['success']:
            current_app.logger.info(f"✅ AI interaction created: {interaction_data['id']}")
            return success_response(result, 201)
        else:
            return error_response(result['error'], 500)

    except Exception as e:
        current_app.logger.error(f"❌ Error creating AI interaction: {str(e)}")
        return error_response(str(e), 500)

@bp.route('/ai-interactions', methods=['DELETE'])
def clear_all_ai_interactions():
    """
    Clear all AI interactions (for testing)
    """
    try:
        current_app.logger.info("🗑️ Clearing all AI interactions")

        ai_service = AIService(get_db())
        result = ai_service.clear_all_interactions()

        if result['success']:
            current_app.logger.info("✅ All AI interactions cleared")
            return success_response(result)
        else:
            return error_response(result['error'], 500)

    except Exception as e:
        current_app.logger.error(f"❌ Error clearing AI interactions: {str(e)}")
        return error_response(str(e), 500)
