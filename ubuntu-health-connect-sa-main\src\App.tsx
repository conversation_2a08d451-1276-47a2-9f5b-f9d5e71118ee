
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import Index from "./pages/Index";
import PatientDashboard from "./pages/PatientDashboard";
import ProviderDashboard from "./pages/ProviderDashboard";
import HealthStatistics from "./pages/HealthStatistics";
import HealthStatisticsSimple from "./pages/HealthStatisticsSimple";
import TestPage from "./pages/TestPage";
import NotFound from "./pages/NotFound";
import { OpenAITest } from "./components/OpenAITest";
import { TestPatientCaseFlow } from "./components/TestPatientCaseFlow";
import { CommunicationTest } from "./components/CommunicationTest";
import { ChatInteractionTest } from "./components/ChatInteractionTest";

// Initialize chat interaction services
import "./services/chatInteractionService";
import "./services/voiceTriageIntegration";
import "./services/voiceTriageWebhook";

const queryClient = new QueryClient();

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/patient" element={<PatientDashboard />} />
            <Route path="/provider" element={<ProviderDashboard />} />
            <Route path="/statistics" element={
              <ErrorBoundary>
                <HealthStatistics />
              </ErrorBoundary>
            } />
            <Route path="/test" element={<TestPage />} />
            <Route path="/openai-test" element={
              <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 p-8">
                <OpenAITest />
              </div>
            } />
            <Route path="/test-case-flow" element={
              <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 p-8">
                <TestPatientCaseFlow />
              </div>
            } />
            <Route path="/communication-test" element={
              <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 p-8">
                <CommunicationTest />
              </div>
            } />
            <Route path="/chat-interaction-test" element={<ChatInteractionTest />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
