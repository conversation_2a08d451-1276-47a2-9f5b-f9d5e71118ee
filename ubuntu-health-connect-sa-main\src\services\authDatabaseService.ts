/**
 * Authentication Database Service
 * Connects the frontend to our patient authentication database
 */

export interface AuthDatabasePatient {
  patient_id: string;
  id_number: string;
  phone_number: string;
  first_name: string;
  last_name: string;
  email: string;
  date_of_birth: string;
  age: number;
  gender: string;
  address: string;
  emergency_contact_name: string;
  emergency_contact_phone: string;
  emergency_contact_relationship: string;
  medical_aid_provider: string;
  medical_aid_number: string;
  registration_date: string;
  last_login: string | null;
  is_active: boolean;
}

export interface AuthDatabaseResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

export interface PatientSearchResponse {
  patients: AuthDatabasePatient[];
  count: number;
  total_patients: number;
  search_term: string;
}

class AuthDatabaseService {
  private readonly baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';

  /**
   * Get all patients with optional search
   */
  async getAllPatients(searchTerm: string = ''): Promise<AuthDatabasePatient[]> {
    try {
      const url = new URL(`${this.baseUrl}/api/patients`);
      if (searchTerm) {
        url.searchParams.append('search', searchTerm);
      }

      console.log(`🔍 Fetching patients from auth database${searchTerm ? ` (search: "${searchTerm}")` : ''}`);

      const response = await fetch(url.toString());
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result: AuthDatabaseResponse<PatientSearchResponse> = await response.json();
      
      if (result.success && result.data) {
        console.log(`✅ Retrieved ${result.data.count} patients from auth database`);
        return result.data.patients;
      } else {
        throw new Error(result.error || 'Failed to fetch patients');
      }
    } catch (error) {
      console.error('❌ Error fetching patients from auth database:', error);
      return [];
    }
  }

  /**
   * Get patient by ID number
   */
  async getPatientByIdNumber(idNumber: string): Promise<AuthDatabasePatient | null> {
    try {
      console.log(`🔍 Looking up patient by ID: ${idNumber}`);

      const response = await fetch(`${this.baseUrl}/api/patients/${idNumber}`);
      
      if (response.status === 404) {
        console.log(`ℹ️ Patient not found: ${idNumber}`);
        return null;
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result: AuthDatabaseResponse<AuthDatabasePatient> = await response.json();
      
      if (result.success && result.data) {
        console.log(`✅ Found patient: ${result.data.first_name} ${result.data.last_name}`);
        return result.data;
      } else {
        throw new Error(result.error || 'Failed to fetch patient');
      }
    } catch (error) {
      console.error('❌ Error fetching patient by ID:', error);
      return null;
    }
  }

  /**
   * Search patients by various criteria
   */
  async searchPatients(searchTerm: string): Promise<AuthDatabasePatient[]> {
    return this.getAllPatients(searchTerm);
  }

  /**
   * Convert auth database patient to frontend format for compatibility
   */
  convertToFrontendFormat(authPatient: AuthDatabasePatient): any {
    return {
      patient: {
        id: authPatient.patient_id,
        personalInfo: {
          firstName: authPatient.first_name,
          lastName: authPatient.last_name,
          dateOfBirth: authPatient.date_of_birth,
          age: authPatient.age,
          gender: authPatient.gender as 'Male' | 'Female',
          idNumber: authPatient.id_number,
          phone: authPatient.phone_number,
          email: authPatient.email,
          address: authPatient.address,
          emergencyContact: {
            name: authPatient.emergency_contact_name,
            relationship: authPatient.emergency_contact_relationship,
            phone: authPatient.emergency_contact_phone
          }
        },
        medicalHistory: [],
        medications: [],
        allergies: [],
        vaccinations: [],
        labResults: [],
        appointments: [],
        insurance: {
          provider: authPatient.medical_aid_provider,
          policyNumber: authPatient.medical_aid_number,
          groupNumber: '',
          validUntil: ''
        },
        createdAt: authPatient.registration_date,
        updatedAt: authPatient.last_login || authPatient.registration_date,
        healthcareProviders: [],
        aiInteractions: []
      },
      verified: true,
      source: 'auth_database'
    };
  }

  /**
   * Get all patients in frontend format
   */
  async getAllPatientsForFrontend(searchTerm: string = ''): Promise<any[]> {
    const authPatients = await this.getAllPatients(searchTerm);
    return authPatients.map(patient => this.convertToFrontendFormat(patient));
  }

  /**
   * Get patient by ID in frontend format
   */
  async getPatientByIdNumberForFrontend(idNumber: string): Promise<any | null> {
    const authPatient = await this.getPatientByIdNumber(idNumber);
    return authPatient ? this.convertToFrontendFormat(authPatient) : null;
  }

  /**
   * Get database statistics
   */
  async getStats(): Promise<{
    total_patients: number;
    patients_with_logins: number;
    recent_registrations: number;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.database) {
        const allPatients = await this.getAllPatients();
        const patientsWithLogins = allPatients.filter(p => p.last_login).length;
        const recentRegistrations = allPatients.filter(p => {
          const regDate = new Date(p.registration_date);
          const weekAgo = new Date();
          weekAgo.setDate(weekAgo.getDate() - 7);
          return regDate > weekAgo;
        }).length;

        return {
          total_patients: result.database.total_patients || allPatients.length,
          patients_with_logins: patientsWithLogins,
          recent_registrations: recentRegistrations
        };
      }

      return {
        total_patients: 0,
        patients_with_logins: 0,
        recent_registrations: 0
      };
    } catch (error) {
      console.error('❌ Error fetching database stats:', error);
      return {
        total_patients: 0,
        patients_with_logins: 0,
        recent_registrations: 0
      };
    }
  }

  /**
   * Test connection to auth database
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      return response.ok;
    } catch (error) {
      console.error('❌ Auth database connection test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const authDatabaseService = new AuthDatabaseService();
export default authDatabaseService;
