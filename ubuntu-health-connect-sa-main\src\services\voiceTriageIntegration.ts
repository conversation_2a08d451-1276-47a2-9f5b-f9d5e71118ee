// Voice Triage Integration Service - Connects voice triage calls to medical records
// This service bridges the voice triage system with the chat interaction service

import { chatInteractionService } from './chatInteractionService';

export interface VoiceTriageCall {
  id: string;
  patientId?: string;
  phoneNumber: string;
  callSid: string;
  language: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  transcripts: VoiceTranscript[];
  aiAssessment?: {
    symptoms: string[];
    severity: 'Low' | 'Medium' | 'High' | 'Critical';
    recommendations: string[];
    triageDecision: 'Self-Care' | 'Schedule Appointment' | 'Urgent Care' | 'Emergency';
  };
  status: 'initiated' | 'in_progress' | 'completed' | 'failed';
}

export interface VoiceTranscript {
  id: string;
  speaker: 'patient' | 'ai';
  text: string;
  confidence: number;
  timestamp: number; // seconds from call start
  language?: string;
}

class VoiceTriageIntegrationService {
  private activeCalls = new Map<string, VoiceTriageCall>();
  private callChatSessions = new Map<string, string>(); // callId -> chatSessionId

  constructor() {
    console.log('📞 VoiceTriageIntegrationService initialized - Connecting voice calls to medical records');
  }

  /**
   * Start a new voice triage call and create corresponding chat session
   */
  startVoiceTriageCall(
    callSid: string,
    phoneNumber: string,
    language: string = 'en',
    patientId?: string
  ): string {
    const callId = `voice_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // Create chat session for this voice call
    const chatSessionId = chatInteractionService.startChatSession(
      'Voice Triage',
      language,
      {
        patientId,
        phone: phoneNumber
      }
    );

    const voiceCall: VoiceTriageCall = {
      id: callId,
      patientId,
      phoneNumber,
      callSid,
      language,
      startTime: new Date(),
      transcripts: [],
      status: 'initiated'
    };

    this.activeCalls.set(callId, voiceCall);
    this.callChatSessions.set(callId, chatSessionId);

    // Add initial system message to chat session
    const initialMessage = {
      id: `voice_init_${Date.now()}`,
      role: 'system' as const,
      content: `Voice triage call initiated. Phone: ${phoneNumber}, Language: ${language}`,
      timestamp: new Date()
    };
    chatInteractionService.addMessageToSession(chatSessionId, initialMessage);

    console.log(`📞 Started voice triage call: ${callId} with chat session: ${chatSessionId}`);
    return callId;
  }

  /**
   * Add a transcript to a voice call and corresponding chat session
   */
  addTranscript(
    callId: string,
    speaker: 'patient' | 'ai',
    text: string,
    confidence: number = 1.0,
    timestamp: number = 0
  ): boolean {
    const call = this.activeCalls.get(callId);
    if (!call) {
      console.warn(`Voice call ${callId} not found`);
      return false;
    }

    const transcript: VoiceTranscript = {
      id: `transcript_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      speaker,
      text,
      confidence,
      timestamp,
      language: call.language
    };

    call.transcripts.push(transcript);

    // Add to chat session as well
    const chatSessionId = this.callChatSessions.get(callId);
    if (chatSessionId) {
      const chatMessage = {
        id: transcript.id,
        role: speaker === 'patient' ? 'user' as const : 'assistant' as const,
        content: text,
        timestamp: new Date()
      };
      chatInteractionService.addMessageToSession(chatSessionId, chatMessage);
    }

    console.log(`📝 Added transcript to call ${callId}: ${speaker} - ${text.substring(0, 50)}...`);
    return true;
  }

  /**
   * Update call status
   */
  updateCallStatus(callId: string, status: VoiceTriageCall['status']): boolean {
    const call = this.activeCalls.get(callId);
    if (!call) {
      console.warn(`Voice call ${callId} not found`);
      return false;
    }

    call.status = status;
    console.log(`📞 Updated call ${callId} status to: ${status}`);
    return true;
  }

  /**
   * End a voice triage call and finalize the chat session
   */
  async endVoiceTriageCall(
    callId: string,
    duration?: number,
    aiAssessment?: VoiceTriageCall['aiAssessment']
  ): Promise<boolean> {
    const call = this.activeCalls.get(callId);
    if (!call) {
      console.warn(`Voice call ${callId} not found`);
      return false;
    }

    call.endTime = new Date();
    call.duration = duration;
    call.status = 'completed';
    call.aiAssessment = aiAssessment;

    // End the corresponding chat session
    const chatSessionId = this.callChatSessions.get(callId);
    if (chatSessionId) {
      const summary = this.generateCallSummary(call);
      await chatInteractionService.endChatSession(chatSessionId, summary);
      this.callChatSessions.delete(callId);
    }

    // Move to completed calls (you could store this in a separate map or database)
    this.activeCalls.delete(callId);

    console.log(`📞 Ended voice triage call: ${callId}`);
    return true;
  }

  /**
   * Generate a summary of the voice call
   */
  private generateCallSummary(call: VoiceTriageCall): string {
    const patientTranscripts = call.transcripts.filter(t => t.speaker === 'patient');
    const aiTranscripts = call.transcripts.filter(t => t.speaker === 'ai');
    
    const durationText = call.duration ? `${Math.round(call.duration / 60)} minutes` : 'Unknown duration';
    
    let summary = `Voice triage call completed. Duration: ${durationText}. `;
    summary += `Patient messages: ${patientTranscripts.length}, AI responses: ${aiTranscripts.length}. `;
    
    if (call.aiAssessment) {
      summary += `Assessment: ${call.aiAssessment.severity} severity. `;
      summary += `Symptoms: ${call.aiAssessment.symptoms.join(', ')}. `;
      summary += `Recommendation: ${call.aiAssessment.triageDecision}.`;
    }

    if (patientTranscripts.length > 0) {
      const firstMessage = patientTranscripts[0].text;
      summary += ` Patient initially reported: "${firstMessage.substring(0, 100)}..."`;
    }

    return summary;
  }

  /**
   * Get active voice calls
   */
  getActiveCalls(): VoiceTriageCall[] {
    return Array.from(this.activeCalls.values());
  }

  /**
   * Get call by ID
   */
  getCallById(callId: string): VoiceTriageCall | undefined {
    return this.activeCalls.get(callId);
  }

  /**
   * Get call by call SID (Africa's Talking session ID)
   */
  getCallByCallSid(callSid: string): VoiceTriageCall | undefined {
    for (const call of this.activeCalls.values()) {
      if (call.callSid === callSid) {
        return call;
      }
    }
    return undefined;
  }

  /**
   * Process voice triage data from external systems
   * This method can be called by webhook handlers or API endpoints
   */
  async processVoiceTriageData(data: {
    callSid: string;
    phoneNumber: string;
    language?: string;
    patientId?: string;
    transcripts?: Array<{
      speaker: 'patient' | 'ai';
      text: string;
      confidence?: number;
      timestamp?: number;
    }>;
    duration?: number;
    status?: VoiceTriageCall['status'];
    aiAssessment?: VoiceTriageCall['aiAssessment'];
  }): Promise<string | null> {
    try {
      // Check if call already exists
      let call = this.getCallByCallSid(data.callSid);
      let callId: string;

      if (!call) {
        // Create new call
        callId = this.startVoiceTriageCall(
          data.callSid,
          data.phoneNumber,
          data.language || 'en',
          data.patientId
        );
      } else {
        callId = call.id;
      }

      // Add transcripts if provided
      if (data.transcripts) {
        for (const transcript of data.transcripts) {
          this.addTranscript(
            callId,
            transcript.speaker,
            transcript.text,
            transcript.confidence || 1.0,
            transcript.timestamp || 0
          );
        }
      }

      // Update status if provided
      if (data.status) {
        this.updateCallStatus(callId, data.status);
      }

      // End call if completed
      if (data.status === 'completed') {
        await this.endVoiceTriageCall(callId, data.duration, data.aiAssessment);
      }

      return callId;
    } catch (error) {
      console.error('Error processing voice triage data:', error);
      return null;
    }
  }
}

export const voiceTriageIntegrationService = new VoiceTriageIntegrationService();
export default voiceTriageIntegrationService;
